#ifdef _WIN32
#include "dxgi_compute.hpp"
#include <d3dcompiler.h>
#include <filesystem>
#include <fstream>
#include <sstream>

#pragma comment(lib, "d3dcompiler.lib")

namespace capscr {

DXGICompute::DXGICompute(ComPtr<ID3D11Device> device, ComPtr<ID3D11DeviceContext> context)
    : device_(device), context_(context), initialized_(false) {
}

DXGICompute::~DXGICompute() {
    // ComPtr handles cleanup automatically
}

Result DXGICompute::initialize() {
    if (initialized_) {
        return Result::Ok;
    }
    
    if (!device_ || !context_) {
        CAPSCR_LOG_ERROR("DXGICompute::initialize() - Device or context is null");
        return Result::Error;
    }
    
    CAPSCR_LOG_INFO("DXGICompute::initialize() - Loading and compiling compute shaders");
    
    // Load resize shader
    Result result = loadShaderFromFile("resize.hlsl", "CSHorizontalResize", horizontalResizeShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to load horizontal resize shader");
        return result;
    }
    
    result = loadShaderFromFile("resize.hlsl", "CSVerticalResize", verticalResizeShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to load vertical resize shader");
        return result;
    }
    
    result = loadShaderFromFile("resize.hlsl", "CSSinglePassResize", singlePassResizeShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to load single pass resize shader");
        return result;
    }
    
    // Load conversion shaders
    result = loadShaderFromFile("convert.hlsl", "CSConvertPixelFormat", convertShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to load pixel format conversion shader");
        return result;
    }
    
    result = loadShaderFromFile("convert.hlsl", "CSConvertBGRAToRGBA", bgraToRgbaShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to load BGRA to RGBA conversion shader");
        return result;
    }
    
    result = loadShaderFromFile("convert.hlsl", "CSConvertRGBAToBGRA", rgbaToBgraShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to load RGBA to BGRA conversion shader");
        return result;
    }

    // Load NV12 conversion shaders (Y plane and UV plane)
    result = loadShaderFromFile("convert_nv12.hlsl", "CSNV12_Y_Plane", nv12YShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_WARN("NV12 Y-plane shader not found or failed to compile; NV12 path will be unavailable");
    }

    result = loadShaderFromFile("convert_nv12.hlsl", "CSNV12_UV_Plane", nv12UVShader_);
    if (result != Result::Ok) {
        CAPSCR_LOG_WARN("NV12 UV-plane shader not found or failed to compile; NV12 path will be unavailable");
    }
    
    // Create linear sampler
    D3D11_SAMPLER_DESC samplerDesc = {};
    samplerDesc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
    samplerDesc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.ComparisonFunc = D3D11_COMPARISON_NEVER;
    samplerDesc.MinLOD = 0;
    samplerDesc.MaxLOD = D3D11_FLOAT32_MAX;
    
    HRESULT hr = device_->CreateSamplerState(&samplerDesc, &linearSampler_);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create linear sampler");
        return Result::Error;
    }
    
    initialized_ = true;
    CAPSCR_LOG_INFO("DXGICompute initialized successfully");
    return Result::Ok;
}

Result DXGICompute::resizeTexture(ComPtr<ID3D11Texture2D> inputTexture,
                                 UINT inputWidth, UINT inputHeight,
                                 UINT outputWidth, UINT outputHeight,
                                 int quality,
                                 ComPtr<ID3D11Texture2D>& outputTexture) {
    if (!initialized_) {
        CAPSCR_LOG_ERROR("DXGICompute not initialized");
        return Result::Error;
    }
    
    if (!inputTexture) {
        CAPSCR_LOG_ERROR("Input texture is null");
        return Result::Error;
    }
    
    // Validate dimensions
    if (inputWidth == 0 || inputHeight == 0 || outputWidth == 0 || outputHeight == 0) {
        CAPSCR_LOG_ERROR("Invalid dimensions: input={}x{}, output={}x{}", 
                         inputWidth, inputHeight, outputWidth, outputHeight);
        return Result::Error;
    }
    
    // Reasonable dimension limits to prevent crashes
    constexpr UINT MAX_DIMENSION = 16384; // 16K resolution limit
    if (inputWidth > MAX_DIMENSION || inputHeight > MAX_DIMENSION ||
        outputWidth > MAX_DIMENSION || outputHeight > MAX_DIMENSION) {
        CAPSCR_LOG_ERROR("Dimensions exceed maximum: input={}x{}, output={}x{} (max={})", 
                         inputWidth, inputHeight, outputWidth, outputHeight, MAX_DIMENSION);
        return Result::Error;
    }
    
    CAPSCR_LOG_DEBUG("DXGICompute::resizeTexture: {}x{} -> {}x{}, quality={}", 
                     inputWidth, inputHeight, outputWidth, outputHeight, quality);
    
    // Check if we should use single-pass or two-pass resize
    // Use single-pass for small scale changes (< 2x), two-pass for larger changes
    float scaleX = float(outputWidth) / float(inputWidth);
    float scaleY = float(outputHeight) / float(inputHeight);
    bool useTwoPass = (scaleX < 0.5f || scaleX > 2.0f || scaleY < 0.5f || scaleY > 2.0f);
    
    if (!useTwoPass && quality <= 1) {
        // Use single-pass for better performance with small scale changes
        return resizeSinglePass(inputTexture, inputWidth, inputHeight, 
                               outputWidth, outputHeight, quality, outputTexture);
    } else {
        // Use two-pass for better quality with large scale changes
        return resizeTwoPass(inputTexture, inputWidth, inputHeight,
                            outputWidth, outputHeight, quality, outputTexture);
    }
}

Result DXGICompute::resizeSinglePass(ComPtr<ID3D11Texture2D> inputTexture,
                                    UINT inputWidth, UINT inputHeight,
                                    UINT outputWidth, UINT outputHeight,
                                    int quality,
                                    ComPtr<ID3D11Texture2D>& outputTexture) {
    CAPSCR_LOG_DEBUG("resizeSinglePass: {}x{} -> {}x{}, quality={}", 
                     inputWidth, inputHeight, outputWidth, outputHeight, quality);
    
    // Get input texture format to ensure compatibility
    D3D11_TEXTURE2D_DESC inputDesc;
    inputTexture->GetDesc(&inputDesc);
    DXGI_FORMAT outputFormat = inputDesc.Format; // Use same format as input
    
    CAPSCR_LOG_DEBUG("Setting up texture format for processing");
    
    // Create output texture
    Result result = createIntermediateTexture(outputWidth, outputHeight, outputFormat, outputTexture);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to create intermediate texture for single pass resize");
        return result;
    }
    
    // Create texture views
    TextureViews inputViews, outputViews;
    result = createTextureViews(inputTexture, inputViews);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to create input texture views");
        return result;
    }
    
    result = createTextureViews(outputTexture, outputViews);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to create output texture views");
        return result;
    }
    
    // Set up resize parameters
    ResizeConstantBuffer resizeParams = {};
    resizeParams.dimensions.x = inputWidth;
    resizeParams.dimensions.y = inputHeight;
    resizeParams.dimensions.z = outputWidth;
    resizeParams.dimensions.w = outputHeight;
    
    // Mitchell-Netravali filter parameters
    // B=1/3, C=1/3 for balanced sharpness and ringing suppression
    resizeParams.params.x = 2.0f; // filter support
    resizeParams.params.y = 1.0f / 3.0f; // B parameter
    resizeParams.params.z = 1.0f / 3.0f; // C parameter
    resizeParams.params.w = 0.0f; // single pass
    
    resizeParams.scale_offset.x = float(outputWidth) / float(inputWidth);
    resizeParams.scale_offset.y = float(outputHeight) / float(inputHeight);
    resizeParams.scale_offset.z = 0.0f; // offset x
    resizeParams.scale_offset.w = 0.0f; // offset y
    
    // Create and update constant buffer
    ComPtr<ID3D11Buffer> constantBuffer;
    result = createConstantBuffer(sizeof(ResizeConstantBuffer), constantBuffer);
    if (result != Result::Ok) {
        return result;
    }
    
    context_->UpdateSubresource(constantBuffer.Get(), 0, nullptr, &resizeParams, 0, 0);
    
    // Set up compute shader pipeline
    context_->CSSetShader(singlePassResizeShader_.Get(), nullptr, 0);
    context_->CSSetConstantBuffers(0, 1, constantBuffer.GetAddressOf());
    context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
    context_->CSSetUnorderedAccessViews(0, 1, outputViews.uav.GetAddressOf(), nullptr);
    context_->CSSetSamplers(0, 1, linearSampler_.GetAddressOf());
    
    // Dispatch compute shader safely
    result = safeDispatch(outputWidth, outputHeight);
    if (result != Result::Ok) {
        return result;
    }
    
    // Clean up pipeline state
    ID3D11ShaderResourceView* nullSRV[] = { nullptr };
    ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
    context_->CSSetShaderResources(0, 1, nullSRV);
    context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
    context_->CSSetShader(nullptr, nullptr, 0);
    
    return Result::Ok;
}

Result DXGICompute::resizeTwoPass(ComPtr<ID3D11Texture2D> inputTexture,
                                 UINT inputWidth, UINT inputHeight,
                                 UINT outputWidth, UINT outputHeight,
                                 int quality,
                                 ComPtr<ID3D11Texture2D>& outputTexture) {
    CAPSCR_LOG_DEBUG("resizeTwoPass: {}x{} -> {}x{}, quality={}", 
                     inputWidth, inputHeight, outputWidth, outputHeight, quality);
    
    // Get input texture format to ensure compatibility
    D3D11_TEXTURE2D_DESC inputDesc;
    inputTexture->GetDesc(&inputDesc);
    DXGI_FORMAT textureFormat = inputDesc.Format;
    
    // First pass: horizontal resize
    ComPtr<ID3D11Texture2D> intermediateTexture;
    Result result = createIntermediateTexture(outputWidth, inputHeight, textureFormat, intermediateTexture);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to create intermediate texture for horizontal pass");
        return result;
    }
    
    // Horizontal pass
    result = resizeHorizontal(inputTexture, inputWidth, inputHeight, outputWidth, inputHeight, quality, intermediateTexture);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Horizontal resize pass failed");
        return result;
    }
    
    // Second pass: vertical resize
    result = createIntermediateTexture(outputWidth, outputHeight, textureFormat, outputTexture);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to create output texture for vertical pass");
        return result;
    }
    
    result = resizeVertical(intermediateTexture, outputWidth, inputHeight, outputWidth, outputHeight, quality, outputTexture);
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Vertical resize pass failed");
        return result;
    }
    
    return Result::Ok;
}

Result DXGICompute::resizeHorizontal(ComPtr<ID3D11Texture2D> inputTexture,
                                     UINT inputWidth, UINT inputHeight,
                                     UINT outputWidth, UINT outputHeight,
                                     int quality,
                                     ComPtr<ID3D11Texture2D> outputTexture) {
    // Create texture views
    TextureViews inputViews, outputViews;
    Result result = createTextureViews(inputTexture, inputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    result = createTextureViews(outputTexture, outputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    // Set up resize parameters
    ResizeConstantBuffer resizeParams = {};
    resizeParams.dimensions.x = inputWidth;
    resizeParams.dimensions.y = inputHeight;
    resizeParams.dimensions.z = outputWidth;
    resizeParams.dimensions.w = outputHeight;
    
    resizeParams.params.x = 2.0f; // filter support
    resizeParams.params.y = 1.0f / 3.0f; // B parameter
    resizeParams.params.z = 1.0f / 3.0f; // C parameter
    resizeParams.params.w = 1.0f; // horizontal pass
    
    resizeParams.scale_offset.x = float(outputWidth) / float(inputWidth);
    resizeParams.scale_offset.y = 1.0f; // no vertical scaling in this pass
    resizeParams.scale_offset.z = 0.0f;
    resizeParams.scale_offset.w = 0.0f;
    
    // Create and update constant buffer
    ComPtr<ID3D11Buffer> constantBuffer;
    result = createConstantBuffer(sizeof(ResizeConstantBuffer), constantBuffer);
    if (result != Result::Ok) {
        return result;
    }
    
    context_->UpdateSubresource(constantBuffer.Get(), 0, nullptr, &resizeParams, 0, 0);
    
    // Set up compute shader pipeline
    context_->CSSetShader(horizontalResizeShader_.Get(), nullptr, 0);
    context_->CSSetConstantBuffers(0, 1, constantBuffer.GetAddressOf());
    context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
    context_->CSSetUnorderedAccessViews(0, 1, outputViews.uav.GetAddressOf(), nullptr);
    context_->CSSetSamplers(0, 1, linearSampler_.GetAddressOf());
    
    // Dispatch compute shader safely
    result = safeDispatch(outputWidth, outputHeight);
    if (result != Result::Ok) {
        return result;
    }
    
    // Clean up pipeline state
    ID3D11ShaderResourceView* nullSRV[] = { nullptr };
    ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
    context_->CSSetShaderResources(0, 1, nullSRV);
    context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
    context_->CSSetShader(nullptr, nullptr, 0);
    
    return Result::Ok;
}

Result DXGICompute::resizeVertical(ComPtr<ID3D11Texture2D> inputTexture,
                                   UINT inputWidth, UINT inputHeight,
                                   UINT outputWidth, UINT outputHeight,
                                   int quality,
                                   ComPtr<ID3D11Texture2D> outputTexture) {
    // Create texture views
    TextureViews inputViews, outputViews;
    Result result = createTextureViews(inputTexture, inputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    result = createTextureViews(outputTexture, outputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    // Set up resize parameters
    ResizeConstantBuffer resizeParams = {};
    resizeParams.dimensions.x = inputWidth;
    resizeParams.dimensions.y = inputHeight;
    resizeParams.dimensions.z = outputWidth;
    resizeParams.dimensions.w = outputHeight;
    
    resizeParams.params.x = 2.0f; // filter support
    resizeParams.params.y = 1.0f / 3.0f; // B parameter
    resizeParams.params.z = 1.0f / 3.0f; // C parameter
    resizeParams.params.w = 2.0f; // vertical pass
    
    resizeParams.scale_offset.x = 1.0f; // no horizontal scaling in this pass
    resizeParams.scale_offset.y = float(outputHeight) / float(inputHeight);
    resizeParams.scale_offset.z = 0.0f;
    resizeParams.scale_offset.w = 0.0f;
    
    // Create and update constant buffer
    ComPtr<ID3D11Buffer> constantBuffer;
    result = createConstantBuffer(sizeof(ResizeConstantBuffer), constantBuffer);
    if (result != Result::Ok) {
        return result;
    }
    
    context_->UpdateSubresource(constantBuffer.Get(), 0, nullptr, &resizeParams, 0, 0);
    
    // Set up compute shader pipeline
    context_->CSSetShader(verticalResizeShader_.Get(), nullptr, 0);
    context_->CSSetConstantBuffers(0, 1, constantBuffer.GetAddressOf());
    context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
    context_->CSSetUnorderedAccessViews(0, 1, outputViews.uav.GetAddressOf(), nullptr);
    context_->CSSetSamplers(0, 1, linearSampler_.GetAddressOf());
    
    // Dispatch compute shader safely
    result = safeDispatch(outputWidth, outputHeight);
    if (result != Result::Ok) {
        return result;
    }
    
    // Clean up pipeline state
    ID3D11ShaderResourceView* nullSRV[] = { nullptr };
    ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
    context_->CSSetShaderResources(0, 1, nullSRV);
    context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
    context_->CSSetShader(nullptr, nullptr, 0);
    
    return Result::Ok;
}

Result DXGICompute::convertPixelFormat(ComPtr<ID3D11Texture2D> inputTexture,
                                      PixelFormat inputFormat,
                                      PixelFormat outputFormat,
                                      int colorSpace,
                                      ComPtr<ID3D11Texture2D>& outputTexture) {
    if (!initialized_) {
        CAPSCR_LOG_ERROR("DXGICompute not initialized");
        return Result::Error;
    }
    
    if (!inputTexture) {
        CAPSCR_LOG_ERROR("Input texture is null");
        return Result::Error;
    }
    
    // Get input texture dimensions
    D3D11_TEXTURE2D_DESC inputDesc;
    inputTexture->GetDesc(&inputDesc);
    
    // Create output texture
    Result result = createIntermediateTexture(inputDesc.Width, inputDesc.Height, 
                                             DXGI_FORMAT_R8G8B8A8_UNORM, outputTexture);
    if (result != Result::Ok) {
        return result;
    }
    // Validate pixel formats before attempting GPU conversion
    if (!isValidPixelFormat(inputFormat) || !isValidPixelFormat(outputFormat)) {
        CAPSCR_LOG_ERROR("DXGICompute::convertPixelFormat: invalid pixel format input=%d output=%d", (int)inputFormat, (int)outputFormat);
        return Result::Error;
    }
    
    // Check for fast path conversions
    if (inputFormat == PixelFormat::BGRA32 && outputFormat == PixelFormat::RGBA32) {
        return convertBGRAToRGBA(inputTexture, outputTexture);
    } else if (inputFormat == PixelFormat::RGBA32 && outputFormat == PixelFormat::BGRA32) {
        return convertRGBAToBGRA(inputTexture, outputTexture);
    }
    
    // Use general conversion shader
    return convertGeneral(inputTexture, inputFormat, outputFormat, colorSpace, outputTexture);
}

Result DXGICompute::convertBGRAToRGBA(ComPtr<ID3D11Texture2D> inputTexture,
                                     ComPtr<ID3D11Texture2D> outputTexture) {
    // Get texture dimensions
    D3D11_TEXTURE2D_DESC desc;
    inputTexture->GetDesc(&desc);
    
    // Create texture views
    TextureViews inputViews, outputViews;
    Result result = createTextureViews(inputTexture, inputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    result = createTextureViews(outputTexture, outputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    // Set up constant buffer with dimensions
    ConvertConstantBuffer convertParams = {};
    convertParams.dimensions.x = desc.Width;
    convertParams.dimensions.y = desc.Height;
    
    ComPtr<ID3D11Buffer> constantBuffer;
    result = createConstantBuffer(sizeof(ConvertConstantBuffer), constantBuffer);
    if (result != Result::Ok) {
        return result;
    }
    
    context_->UpdateSubresource(constantBuffer.Get(), 0, nullptr, &convertParams, 0, 0);
    
    // Set up compute shader pipeline
    context_->CSSetShader(bgraToRgbaShader_.Get(), nullptr, 0);
    context_->CSSetConstantBuffers(0, 1, constantBuffer.GetAddressOf());
    context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
    context_->CSSetUnorderedAccessViews(0, 1, outputViews.uav.GetAddressOf(), nullptr);
    
    // Dispatch compute shader safely
    result = safeDispatch(desc.Width, desc.Height);
    if (result != Result::Ok) {
        return result;
    }
    
    // Clean up pipeline state
    ID3D11ShaderResourceView* nullSRV[] = { nullptr };
    ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
    context_->CSSetShaderResources(0, 1, nullSRV);
    context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
    context_->CSSetShader(nullptr, nullptr, 0);
    
    return Result::Ok;
}

Result DXGICompute::convertRGBAToBGRA(ComPtr<ID3D11Texture2D> inputTexture,
                                     ComPtr<ID3D11Texture2D> outputTexture) {
    // Get texture dimensions
    D3D11_TEXTURE2D_DESC desc;
    inputTexture->GetDesc(&desc);
    
    // Create texture views
    TextureViews inputViews, outputViews;
    Result result = createTextureViews(inputTexture, inputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    result = createTextureViews(outputTexture, outputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    // Set up constant buffer with dimensions
    ConvertConstantBuffer convertParams = {};
    convertParams.dimensions.x = desc.Width;
    convertParams.dimensions.y = desc.Height;
    
    ComPtr<ID3D11Buffer> constantBuffer;
    result = createConstantBuffer(sizeof(ConvertConstantBuffer), constantBuffer);
    if (result != Result::Ok) {
        return result;
    }
    
    context_->UpdateSubresource(constantBuffer.Get(), 0, nullptr, &convertParams, 0, 0);
    
    // Set up compute shader pipeline
    context_->CSSetShader(rgbaToBgraShader_.Get(), nullptr, 0);
    context_->CSSetConstantBuffers(0, 1, constantBuffer.GetAddressOf());
    context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
    context_->CSSetUnorderedAccessViews(0, 1, outputViews.uav.GetAddressOf(), nullptr);
    
    // Dispatch compute shader safely
    result = safeDispatch(desc.Width, desc.Height);
    if (result != Result::Ok) {
        return result;
    }
    
    // Clean up pipeline state
    ID3D11ShaderResourceView* nullSRV[] = { nullptr };
    ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
    context_->CSSetShaderResources(0, 1, nullSRV);
    context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
    context_->CSSetShader(nullptr, nullptr, 0);
    
    return Result::Ok;
}

Result DXGICompute::convertGeneral(ComPtr<ID3D11Texture2D> inputTexture,
                                  PixelFormat inputFormat,
                                  PixelFormat outputFormat,
                                  int colorSpace,
                                  ComPtr<ID3D11Texture2D> outputTexture) {
    // Get texture dimensions
    D3D11_TEXTURE2D_DESC desc;
    inputTexture->GetDesc(&desc);
    
    // Create texture views
    TextureViews inputViews, outputViews;
    Result result = createTextureViews(inputTexture, inputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    result = createTextureViews(outputTexture, outputViews);
    if (result != Result::Ok) {
        return result;
    }
    
    // Set up conversion parameters
    ConvertConstantBuffer convertParams = {};
    convertParams.dimensions.x = desc.Width;
    convertParams.dimensions.y = desc.Height;
    // Validate formats
    if (!isValidPixelFormat(inputFormat) || !isValidPixelFormat(outputFormat)) {
        CAPSCR_LOG_ERROR("DXGICompute::convertGeneral: invalid pixel format input=%d output=%d", (int)inputFormat, (int)outputFormat);
        return Result::Error;
    }
    convertParams.formats.x = static_cast<UINT>(inputFormat);
    convertParams.formats.y = static_cast<UINT>(outputFormat);
    convertParams.formats.z = static_cast<UINT>(colorSpace);
    convertParams.formats.w = 0; // flags
    
    ComPtr<ID3D11Buffer> constantBuffer;
    result = createConstantBuffer(sizeof(ConvertConstantBuffer), constantBuffer);
    if (result != Result::Ok) {
        return result;
    }
    
    context_->UpdateSubresource(constantBuffer.Get(), 0, nullptr, &convertParams, 0, 0);
    
    // Set up compute shader pipeline
    context_->CSSetShader(convertShader_.Get(), nullptr, 0);
    context_->CSSetConstantBuffers(0, 1, constantBuffer.GetAddressOf());
    context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
    context_->CSSetUnorderedAccessViews(0, 1, outputViews.uav.GetAddressOf(), nullptr);
    
    // Dispatch compute shader safely
    result = safeDispatch(desc.Width, desc.Height);
    if (result != Result::Ok) {
        return result;
    }
    
    // Clean up pipeline state
    ID3D11ShaderResourceView* nullSRV[] = { nullptr };
    ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
    context_->CSSetShaderResources(0, 1, nullSRV);
    context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
    context_->CSSetShader(nullptr, nullptr, 0);
    
    return Result::Ok;
}

Result DXGICompute::convertToNV12(ComPtr<ID3D11Texture2D> inputTexture,
                                  UINT outputWidth, UINT outputHeight,
                                  ComPtr<ID3D11Texture2D>& yPlane,
                                  ComPtr<ID3D11Texture2D>& uvPlane,
                                  bool enableSharing) {
    if (!initialized_) {
        CAPSCR_LOG_ERROR("DXGICompute not initialized");
        return Result::Error;
    }
    if (!inputTexture) {
        CAPSCR_LOG_ERROR("Input texture is null");
        return Result::Error;
    }

    // Create Y plane (R8_UNORM)
    D3D11_TEXTURE2D_DESC yDesc = {};
    yDesc.Width = outputWidth;
    yDesc.Height = outputHeight;
    yDesc.MipLevels = 1;
    yDesc.ArraySize = 1;
    yDesc.Format = DXGI_FORMAT_R8_UNORM;
    yDesc.SampleDesc.Count = 1;
    yDesc.Usage = D3D11_USAGE_DEFAULT;
    yDesc.BindFlags = D3D11_BIND_UNORDERED_ACCESS | D3D11_BIND_SHADER_RESOURCE;
    yDesc.CPUAccessFlags = 0;
    yDesc.MiscFlags = enableSharing ? D3D11_RESOURCE_MISC_SHARED : 0;

    HRESULT hr = device_->CreateTexture2D(&yDesc, nullptr, &yPlane);
    if (FAILED(hr) || !yPlane) {
        CAPSCR_LOG_ERROR("Failed to create Y plane texture: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }

    // Create UV plane (R8G8_UNORM) at half resolution
    UINT uvW = (outputWidth + 1) / 2;
    UINT uvH = (outputHeight + 1) / 2;
    D3D11_TEXTURE2D_DESC uvDesc = yDesc;
    uvDesc.Width = uvW;
    uvDesc.Height = uvH;
    uvDesc.Format = DXGI_FORMAT_R8G8_UNORM;

    hr = device_->CreateTexture2D(&uvDesc, nullptr, &uvPlane);
    if (FAILED(hr) || !uvPlane) {
        CAPSCR_LOG_ERROR("Failed to create UV plane texture: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }

    // If NV12 shaders are available, run compute shaders to populate planes
    if (nv12YShader_ && nv12UVShader_) {
        // Create SRV for input and UAVs for output
        TextureViews inputViews, yViews, uvViews;
        if (createTextureViews(inputTexture, inputViews) != Result::Ok) {
            CAPSCR_LOG_ERROR("Failed to create input views for NV12");
            return Result::Error;
        }
        if (createTextureViews(yPlane, yViews) != Result::Ok) {
            CAPSCR_LOG_ERROR("Failed to create Y plane views for NV12");
            return Result::Error;
        }
        if (createTextureViews(uvPlane, uvViews) != Result::Ok) {
            CAPSCR_LOG_ERROR("Failed to create UV plane views for NV12");
            return Result::Error;
        }

        // Update constant buffer for dimensions
        ConvertConstantBuffer params = {};
        params.dimensions.x = outputWidth;
        params.dimensions.y = outputHeight;

        ComPtr<ID3D11Buffer> cb;
        if (createConstantBuffer(sizeof(ConvertConstantBuffer), cb) != Result::Ok) {
            return Result::Error;
        }
        context_->UpdateSubresource(cb.Get(), 0, nullptr, &params, 0, 0);

        // Y pass
        context_->CSSetShader(nv12YShader_.Get(), nullptr, 0);
        context_->CSSetConstantBuffers(0, 1, cb.GetAddressOf());
        context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
        context_->CSSetUnorderedAccessViews(0, 1, yViews.uav.GetAddressOf(), nullptr);
        Result r = safeDispatch(outputWidth, outputHeight);
        if (r != Result::Ok) return r;

        // UV pass (half resolution)
        context_->CSSetShader(nv12UVShader_.Get(), nullptr, 0);
        context_->CSSetConstantBuffers(0, 1, cb.GetAddressOf());
        context_->CSSetShaderResources(0, 1, inputViews.srv.GetAddressOf());
        context_->CSSetUnorderedAccessViews(0, 1, uvViews.uav.GetAddressOf(), nullptr);
        r = safeDispatch(uvW, uvH);
        if (r != Result::Ok) return r;

        // Cleanup
        ID3D11ShaderResourceView* nullSRV[] = { nullptr };
        ID3D11UnorderedAccessView* nullUAV[] = { nullptr };
        context_->CSSetShaderResources(0, 1, nullSRV);
        context_->CSSetUnorderedAccessViews(0, 1, nullUAV, nullptr);
        context_->CSSetShader(nullptr, nullptr, 0);

        return Result::Ok;
    }

    // Fallback: CPU path - map input and write planes into staging then copy back.
    CAPSCR_LOG_WARN("NV12 shaders unavailable; falling back to CPU conversion (slow)");

    // Get input desc and create staging readback
    D3D11_TEXTURE2D_DESC inDesc;
    inputTexture->GetDesc(&inDesc);

    D3D11_TEXTURE2D_DESC stagingDesc = inDesc;
    stagingDesc.Usage = D3D11_USAGE_STAGING;
    stagingDesc.BindFlags = 0;
    stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stagingDesc.MiscFlags = 0;

    ComPtr<ID3D11Texture2D> staging;
    hr = device_->CreateTexture2D(&stagingDesc, nullptr, &staging);
    if (FAILED(hr) || !staging) {
        CAPSCR_LOG_ERROR("Failed to create staging texture for CPU NV12: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }

    // Copy input to staging
    context_->CopyResource(staging.Get(), inputTexture.Get());
    // Map staging
    D3D11_MAPPED_SUBRESOURCE mapped;
    hr = context_->Map(staging.Get(), 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to map staging texture for CPU NV12: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }

    // Allocate temporary buffers
    std::vector<uint8_t> ybuf(outputWidth * outputHeight);
    std::vector<uint8_t> uvbuf(uvW * uvH * 2);

    for (UINT row = 0; row < outputHeight; ++row) {
        uint8_t* srcRow = reinterpret_cast<uint8_t*>(reinterpret_cast<uint8_t*>(mapped.pData) + row * mapped.RowPitch);
        for (UINT col = 0; col < outputWidth; ++col) {
            uint8_t b = srcRow[col * 4 + 0];
            uint8_t g = srcRow[col * 4 + 1];
            uint8_t r = srcRow[col * 4 + 2];
            float luma = 0.299f * r + 0.587f * g + 0.114f * b;
            ybuf[row * outputWidth + col] = static_cast<uint8_t>(luma);
        }
    }

    // UV
    for (UINT uy = 0; uy < uvH; ++uy) {
        for (UINT ux = 0; ux < uvW; ++ux) {
            int count = 0;
            int sumU = 0;
            int sumV = 0;
            for (int oy = 0; oy < 2; ++oy) {
                for (int ox = 0; ox < 2; ++ox) {
                    UINT px = ux * 2 + ox;
                    UINT py = uy * 2 + oy;
                    if (px >= outputWidth || py >= outputHeight) continue;
                    uint8_t* srcRow = reinterpret_cast<uint8_t*>(reinterpret_cast<uint8_t*>(mapped.pData) + py * mapped.RowPitch);
                    uint8_t b = srcRow[px * 4 + 0];
                    uint8_t g = srcRow[px * 4 + 1];
                    uint8_t r = srcRow[px * 4 + 2];
                    float luma = 0.299f * r + 0.587f * g + 0.114f * b;
                    // crude U/V approximation
                    int u = static_cast<int>((r - luma) * 0.493f + 128);
                    int v = static_cast<int>((b - luma) * 0.877f + 128);
                    sumU += u; sumV += v; count++;
                }
            }
            if (count == 0) {
                uvbuf[(uy * uvW + ux) * 2 + 0] = 128;
                uvbuf[(uy * uvW + ux) * 2 + 1] = 128;
            } else {
                uvbuf[(uy * uvW + ux) * 2 + 0] = static_cast<uint8_t>(sumU / count);
                uvbuf[(uy * uvW + ux) * 2 + 1] = static_cast<uint8_t>(sumV / count);
            }
        }
    }

    context_->Unmap(staging.Get(), 0);

    // Create staging textures for Y and UV and update then copy into GPU textures
    D3D11_TEXTURE2D_DESC yStDesc = yDesc;
    yStDesc.Usage = D3D11_USAGE_STAGING;
    yStDesc.BindFlags = 0;
    yStDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    yStDesc.MiscFlags = 0;
    ComPtr<ID3D11Texture2D> yStaging;
    hr = device_->CreateTexture2D(&yStDesc, nullptr, &yStaging);
    if (FAILED(hr) || !yStaging) return Result::Error;

    D3D11_TEXTURE2D_DESC uvStDesc = uvDesc;
    uvStDesc.Usage = D3D11_USAGE_STAGING;
    uvStDesc.BindFlags = 0;
    uvStDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    uvStDesc.MiscFlags = 0;
    ComPtr<ID3D11Texture2D> uvStaging;
    hr = device_->CreateTexture2D(&uvStDesc, nullptr, &uvStaging);
    if (FAILED(hr) || !uvStaging) return Result::Error;

    // Map and write Y
    D3D11_MAPPED_SUBRESOURCE yMap;
    hr = context_->Map(yStaging.Get(), 0, D3D11_MAP_WRITE, 0, &yMap);
    if (FAILED(hr)) return Result::Error;
    for (UINT row = 0; row < outputHeight; ++row) {
        uint8_t* dst = reinterpret_cast<uint8_t*>(yMap.pData) + static_cast<size_t>(row) * yMap.RowPitch;
        const uint8_t* src = &ybuf[row * outputWidth];
        size_t expected = static_cast<size_t>(outputWidth);
        size_t available = static_cast<size_t>(yMap.RowPitch);
        size_t copyBytes = (available < expected) ? available : expected;
        memcpy(dst, src, copyBytes);
        if (available > copyBytes) memset(dst + copyBytes, 0, available - copyBytes);
    }
    context_->Unmap(yStaging.Get(), 0);

    // Map and write UV
    D3D11_MAPPED_SUBRESOURCE uvMap;
    hr = context_->Map(uvStaging.Get(), 0, D3D11_MAP_WRITE, 0, &uvMap);
    if (FAILED(hr)) return Result::Error;
    for (UINT row = 0; row < uvH; ++row) {
        uint8_t* dst = reinterpret_cast<uint8_t*>(uvMap.pData) + static_cast<size_t>(row) * uvMap.RowPitch;
        const uint8_t* src = &uvbuf[row * uvW * 2];
        size_t expected = static_cast<size_t>(uvW * 2);
        size_t available = static_cast<size_t>(uvMap.RowPitch);
        size_t copyBytes = (available < expected) ? available : expected;
        memcpy(dst, src, copyBytes);
        if (available > copyBytes) memset(dst + copyBytes, 0, available - copyBytes);
    }
    context_->Unmap(uvStaging.Get(), 0);

    // Copy staging into GPU textures
    context_->CopyResource(yPlane.Get(), yStaging.Get());
    context_->CopyResource(uvPlane.Get(), uvStaging.Get());
    context_->Flush();

    return Result::Ok;
}

Result DXGICompute::processTexture(ComPtr<ID3D11Texture2D> inputTexture,
                                  const CaptureParams& params,
                                  ComPtr<ID3D11Texture2D>& outputTexture) {
    if (!initialized_) {
        CAPSCR_LOG_ERROR("DXGICompute not initialized");
        return Result::Error;
    }
    
    if (!inputTexture) {
        CAPSCR_LOG_ERROR("Input texture is null");
        return Result::Error;
    }
    
    // Get input texture dimensions
    D3D11_TEXTURE2D_DESC inputDesc;
    inputTexture->GetDesc(&inputDesc);
    
    UINT inputWidth = inputDesc.Width;
    UINT inputHeight = inputDesc.Height;
    UINT outputWidth = (params.output_width > 0) ? params.output_width : inputWidth;
    UINT outputHeight = (params.output_height > 0) ? params.output_height : inputHeight;
    
    ComPtr<ID3D11Texture2D> currentTexture = inputTexture;
    ComPtr<ID3D11Texture2D> tempTexture;
    
    // Step 1: Resize if needed
    if (outputWidth != inputWidth || outputHeight != inputHeight) {
        int quality = static_cast<int>(params.resize_quality);
        Result result = resizeTexture(currentTexture, inputWidth, inputHeight,
                                     outputWidth, outputHeight, quality, tempTexture);
        if (result != Result::Ok) {
            CAPSCR_LOG_ERROR("Failed to resize texture");
            return result;
        }
        currentTexture = tempTexture;
    }
    
    // Step 2: Convert pixel format if needed
    PixelFormat currentFormat = PixelFormat::BGRA32; // DXGI uses BGRA
    if (params.output_format != currentFormat) {
        ComPtr<ID3D11Texture2D> convertedTexture;
        int colorSpace = static_cast<int>(params.color_space);
        Result result = convertPixelFormat(currentTexture, currentFormat, params.output_format,
                                          colorSpace, convertedTexture);
        if (result != Result::Ok) {
            CAPSCR_LOG_ERROR("Failed to convert pixel format");
            return result;
        }
        currentTexture = convertedTexture;
    }
    
    outputTexture = currentTexture;
    return Result::Ok;
}

bool DXGICompute::isAvailable() const {
    return initialized_;
}

Result DXGICompute::loadShaderFromFile(const std::string& filename, const std::string& entryPoint,
                                      ComPtr<ID3D11ComputeShader>& shader) {
    // Try to find shader file in multiple locations. Prefer a precompiled .cso
    // file (same name but .cso). If not found, fall back to the HLSL source.
    std::vector<std::string> searchPaths = {
        filename,
        "shaders/" + filename,
        "src/windows/shaders/" + filename,
        "../src/windows/shaders/" + filename,
        "../../src/windows/shaders/" + filename
    };
    
    std::string shaderPath;
    bool found = false;
    
    // Debug: list search paths
    for (const auto& pth : searchPaths) CAPSCR_LOG_DEBUG("shader search path: {}", pth);

    for (const auto& path : searchPaths) {
        // First check for a precompiled .cso sibling (replace .hlsl with .cso)
        std::filesystem::path p(path);
        std::string csoPath = p.replace_extension(".cso").string();
        if (std::filesystem::exists(csoPath)) {
            // Load precompiled blob and create shader directly
            std::ifstream blobFile(csoPath, std::ios::binary | std::ios::ate);
            if (!blobFile.is_open()) continue;
            std::streamsize size = blobFile.tellg();
            blobFile.seekg(0, std::ios::beg);
            std::vector<char> buffer(size);
            if (!blobFile.read(buffer.data(), size)) continue;
            HRESULT hr = device_->CreateComputeShader(buffer.data(), (SIZE_T)size, nullptr, &shader);
            if (FAILED(hr)) {
                CAPSCR_LOG_ERROR("Failed to create compute shader from .cso: " + csoPath);
                return Result::Error;
            }
            CAPSCR_LOG_INFO("Loaded precompiled shader: " + csoPath);
            return Result::Ok;
        }

        // Next try the HLSL source file
        if (std::filesystem::exists(path)) {
            shaderPath = path;
            found = true;
            break;
        }
    }

    if (!found) {
        CAPSCR_LOG_ERROR("Shader file not found: " + filename);
        return Result::Error;
    }

    // Read shader source
    std::ifstream file(shaderPath);
    if (!file.is_open()) {
        CAPSCR_LOG_ERROR("Failed to open shader file: " + shaderPath);
        return Result::Error;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string shaderSource = buffer.str();

    // Compile shader from source
    return loadComputeShader(shaderSource, entryPoint, shader);
}

Result DXGICompute::loadComputeShader(const std::string& shaderCode, const std::string& entryPoint,
                                     ComPtr<ID3D11ComputeShader>& shader) {
    ComPtr<ID3DBlob> shaderBlob;
    ComPtr<ID3DBlob> errorBlob;
    
    DWORD flags = D3DCOMPILE_ENABLE_STRICTNESS;
#ifdef _DEBUG
    flags |= D3DCOMPILE_DEBUG | D3DCOMPILE_SKIP_OPTIMIZATION;
#else
    flags |= D3DCOMPILE_OPTIMIZATION_LEVEL3;
#endif
    
    HRESULT hr = D3DCompile(
        shaderCode.c_str(),
        shaderCode.length(),
        nullptr,        // source name
        nullptr,        // defines
        nullptr,        // include
        entryPoint.c_str(),
        "cs_5_0",       // target
        flags,
        0,              // effect flags
        &shaderBlob,
        &errorBlob
    );
    
    if (FAILED(hr)) {
        std::string errorMsg = "Failed to compile compute shader: " + entryPoint;
        if (errorBlob) {
            errorMsg += "\n";
            errorMsg += static_cast<char*>(errorBlob->GetBufferPointer());
        }
        CAPSCR_LOG_ERROR(errorMsg);
        return Result::Error;
    }
    
    hr = device_->CreateComputeShader(
        shaderBlob->GetBufferPointer(),
        shaderBlob->GetBufferSize(),
        nullptr,
        &shader
    );
    
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create compute shader: " + entryPoint);
        return Result::Error;
    }
    
    CAPSCR_LOG_INFO("Successfully loaded compute shader: " + entryPoint);
    return Result::Ok;
}

Result DXGICompute::createConstantBuffer(UINT size, ComPtr<ID3D11Buffer>& buffer) {
    D3D11_BUFFER_DESC bufferDesc = {};
    bufferDesc.ByteWidth = size;
    bufferDesc.Usage = D3D11_USAGE_DEFAULT;
    bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    bufferDesc.CPUAccessFlags = 0;
    bufferDesc.MiscFlags = 0;
    bufferDesc.StructureByteStride = 0;
    
    HRESULT hr = device_->CreateBuffer(&bufferDesc, nullptr, &buffer);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create constant buffer");
        return Result::Error;
    }
    
    return Result::Ok;
}

Result DXGICompute::createTextureViews(ComPtr<ID3D11Texture2D> texture, TextureViews& views) {
    if (!texture) {
        CAPSCR_LOG_ERROR("Texture is null");
        return Result::Error;
    }
    
    D3D11_TEXTURE2D_DESC textureDesc;
    texture->GetDesc(&textureDesc);
    
    CAPSCR_LOG_DEBUG("Creating texture views for {}x{} texture, format={}", 
                     textureDesc.Width, textureDesc.Height, static_cast<int>(textureDesc.Format));
    
    // Validate texture properties
    if (textureDesc.Width == 0 || textureDesc.Height == 0) {
        CAPSCR_LOG_ERROR("Invalid texture dimensions: {}x{}", textureDesc.Width, textureDesc.Height);
        return Result::Error;
    }
    
    // Check if texture supports the required bind flags
    const UINT requiredFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
    if ((textureDesc.BindFlags & requiredFlags) != requiredFlags) {
        CAPSCR_LOG_ERROR("Texture doesn't support required bind flags. Has: 0x{:08X}, Needs: 0x{:08X}", 
                         textureDesc.BindFlags, requiredFlags);
        return Result::Error;
    }
    
    // Create Shader Resource View
    D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
    srvDesc.Format = textureDesc.Format;
    srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
    srvDesc.Texture2D.MostDetailedMip = 0;
    srvDesc.Texture2D.MipLevels = 1;
    
    HRESULT hr = device_->CreateShaderResourceView(texture.Get(), &srvDesc, &views.srv);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create shader resource view: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }
    
    // Create Unordered Access View
    D3D11_UNORDERED_ACCESS_VIEW_DESC uavDesc = {};
    uavDesc.Format = textureDesc.Format;
    uavDesc.ViewDimension = D3D11_UAV_DIMENSION_TEXTURE2D;
    uavDesc.Texture2D.MipSlice = 0;
    
    hr = device_->CreateUnorderedAccessView(texture.Get(), &uavDesc, &views.uav);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create unordered access view: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }
    
    CAPSCR_LOG_DEBUG("Successfully created texture views");
    return Result::Ok;
}

Result DXGICompute::createIntermediateTexture(UINT width, UINT height, DXGI_FORMAT format,
                                             ComPtr<ID3D11Texture2D>& texture) {
    // Validate parameters
    if (width == 0 || height == 0) {
        CAPSCR_LOG_ERROR("Invalid texture dimensions: {}x{}", width, height);
        return Result::Error;
    }
    
    // Reasonable limits
    constexpr UINT MAX_DIMENSION = 16384;
    if (width > MAX_DIMENSION || height > MAX_DIMENSION) {
        CAPSCR_LOG_ERROR("Texture dimensions too large: {}x{} (max={})", width, height, MAX_DIMENSION);
        return Result::Error;
    }
    
    CAPSCR_LOG_DEBUG("Creating intermediate texture for compute processing");
    
    D3D11_TEXTURE2D_DESC textureDesc = {};
    textureDesc.Width = width;
    textureDesc.Height = height;
    textureDesc.MipLevels = 1;
    textureDesc.ArraySize = 1;
    textureDesc.Format = format;
    textureDesc.SampleDesc.Count = 1;
    textureDesc.SampleDesc.Quality = 0;
    textureDesc.Usage = D3D11_USAGE_DEFAULT;
    textureDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
    textureDesc.CPUAccessFlags = 0;
    textureDesc.MiscFlags = 0;
    
    HRESULT hr = device_->CreateTexture2D(&textureDesc, nullptr, &texture);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create intermediate texture: HRESULT=0x{:08X}", hr);
        return Result::Error;
    }
    
    CAPSCR_LOG_DEBUG("Successfully created intermediate texture");
    return Result::Ok;
}

void DXGICompute::calculateDispatchDimensions(UINT width, UINT height, UINT& groupsX, UINT& groupsY) {
    // Ensure minimum dimensions to avoid zero dispatch
    if (width == 0 || height == 0) {
        CAPSCR_LOG_ERROR("DXGICompute::calculateDispatchDimensions: Invalid dimensions {}x{}", width, height);
        groupsX = 1;
        groupsY = 1;
        return;
    }
    
    groupsX = (width + THREAD_GROUP_SIZE_X - 1) / THREAD_GROUP_SIZE_X;
    groupsY = (height + THREAD_GROUP_SIZE_Y - 1) / THREAD_GROUP_SIZE_Y;
    
    // Ensure minimum of 1 group in each dimension
    groupsX = (groupsX == 0) ? 1 : groupsX;
    groupsY = (groupsY == 0) ? 1 : groupsY;
    
    CAPSCR_LOG_DEBUG("DXGICompute::calculateDispatchDimensions: {}x{} -> {}x{} groups", 
                     width, height, groupsX, groupsY);
}

Result DXGICompute::safeDispatch(UINT outputWidth, UINT outputHeight) {
    UINT groupsX, groupsY;
    calculateDispatchDimensions(outputWidth, outputHeight, groupsX, groupsY);
    
    // Final safety check
    if (groupsX == 0 || groupsY == 0) {
        CAPSCR_LOG_ERROR("Invalid dispatch dimensions: {}x{} groups for {}x{} output", 
                         groupsX, groupsY, outputWidth, outputHeight);
        return Result::Error;
    }
    
    // Dispatch compute shader with bounds checking
    CAPSCR_LOG_DEBUG("Dispatching compute shader for texture processing");
    context_->Dispatch(groupsX, groupsY, 1);
    
    return Result::Ok;
}

} // namespace capscr

#endif // _WIN32
