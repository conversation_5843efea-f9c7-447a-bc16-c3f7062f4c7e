#include <iostream>
#include <memory>
#include "capscr/capture.hpp"
#include "capscr/logging.hpp"
#ifdef _WIN32
#include "capscr/platform/windows/capturer_dxgi.hpp"
#endif

using namespace capscr;

int main() {
    std::cout << "=== GPU Texture Capture Test ===" << std::endl;
    
    // Initialize logging system
    capscr::logging::init();
    capscr::logging::set_level(capscr::LogLevel::Debug);
    
    std::cout << "Logging system initialized" << std::endl;
    
    // Test basic logging
    CAPSCR_LOG_INFO("Test application started");
    
    // Create DXGI capturer specifically
#ifdef _WIN32
    auto capturer = capscr::createDXGICapturer();
#else
    auto capturer = createBestCapturer();
#endif
    
    if (!capturer) {
        std::cout << "FAILED: Could not create capturer" << std::endl;
        return 1;
    }
    
    std::cout << "Capturer created" << std::endl;
    
    // List displays first
    std::cout << "Available displays:" << std::endl;
    auto displays = capturer->listDisplays();
    for (size_t i = 0; i < displays.size(); ++i) {
        std::cout << i << ": " << displays[i].id << " " << displays[i].bounds.width << "x" << displays[i].bounds.height << std::endl;
    }
    
    // Initialize for full screen capture
    auto result = capturer->init(CaptureTargetType::FullScreen, "", nullptr, "");
    if (result != Result::Ok) {
        std::cout << "FAILED: Could not initialize capturer (result=" << static_cast<int>(result) << ")" << std::endl;
        return 1;
    }
    
    std::cout << "Capturer initialized successfully" << std::endl;
    
    // Check GPU capture capability
    bool gpuAvailable = capturer->isGpuCaptureAvailable();
    std::cout << "GPU capture available: " << (gpuAvailable ? "YES" : "NO") << std::endl;
    
    if (gpuAvailable) {
        std::cout << "Attempting GPU texture capture..." << std::endl;
        
        GpuTexture gpuTexture;
        auto gpuResult = capturer->captureGpu(gpuTexture);
        
        if (gpuResult == Result::Ok) {
            std::cout << "SUCCESS: GPU texture captured!" << std::endl;
            std::cout << "  Texture size: " << gpuTexture.width << "x" << gpuTexture.height << std::endl;
            std::cout << "  D3D11 Device: " << (gpuTexture.d3d11_device ? "Valid" : "NULL") << std::endl;
            std::cout << "  D3D11 Context: " << (gpuTexture.d3d11_context ? "Valid" : "NULL") << std::endl;
            std::cout << "  D3D11 Texture: " << (gpuTexture.d3d11_texture ? "Valid" : "NULL") << std::endl;
            std::cout << "  Is Valid: " << (gpuTexture.is_valid() ? "YES" : "NO") << std::endl;
            
            // This represents a zero-copy path for hardware encoding
            std::cout << "  Zero-copy texture available for hardware encoding!" << std::endl;
            
        } else {
            std::cout << "GPU texture capture result: " << static_cast<int>(gpuResult) << std::endl;
            std::cout << "(This may be normal if no screen updates occurred)" << std::endl;
        }
    } else {
        std::cout << "GPU capture not available - DXGI device may not be initialized" << std::endl;
    }
    
    // Test traditional capture for comparison
    std::cout << "Attempting traditional frame capture..." << std::endl;
    Frame frame;
    auto frameResult = capturer->capture(frame);
    if (frameResult == Result::Ok) {
        std::cout << "SUCCESS: Traditional frame captured!" << std::endl;
        std::cout << "  Frame size: " << frame.width << "x" << frame.height << std::endl;
        std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    } else {
        std::cout << "Traditional frame capture result: " << static_cast<int>(frameResult) << std::endl;
    }
    
    std::cout << "=== Test completed ===" << std::endl;
    
    capscr::logging::shutdown();
    return 0;
}
