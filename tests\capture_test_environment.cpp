#include "capture_test_environment.hpp"
#include <iostream>
#include <algorithm>

CaptureTestEnvironment* CaptureTestEnvironment::instance_ = nullptr;

CaptureTestEnvironment* CaptureTestEnvironment::Instance() {
    if (!instance_) {
        instance_ = new CaptureTestEnvironment();
    }
    return instance_;
}

void CaptureTestEnvironment::SetUp() {
    std::cout << "\n=== Setting up global capture test environment ===" << std::endl;
    
    // Enumerate available monitors
    EnumerateMonitors();
    
    std::cout << "Found " << monitors_.size() << " monitor(s):" << std::endl;
    for (size_t i = 0; i < monitors_.size(); ++i) {
        const auto& monitor = monitors_[i];
        std::cout << "  Monitor " << i << ": " << monitor.deviceName 
                  << " (" << monitor.rect.left << "," << monitor.rect.top 
                  << " " << (monitor.rect.right - monitor.rect.left) << "x" 
                  << (monitor.rect.bottom - monitor.rect.top) << ")"
                  << (monitor.isPrimary ? " [PRIMARY]" : "") << std::endl;
    }
    
    // Create primary test window with known pattern
    primaryWindow_ = std::make_unique<TestWindow>();
    TestWindow::TestPattern primaryPattern;
    primaryPattern.width = 400;
    primaryPattern.height = 300;
    primaryPattern.backgroundColor = RGB(255, 0, 0); // Red
    primaryPattern.testRect = {50, 50, 100, 50, RGB(0, 255, 0)}; // Green
    primaryPattern.testCircle = {200, 100, 80, 80, RGB(0, 0, 255)}; // Blue
    primaryPattern.text = "PRIMARY_TEST_WINDOW";
    
    if (!primaryWindow_->Create(primaryPattern)) {
        std::cerr << "Failed to create primary test window!" << std::endl;
        return;
    }
    
    // Create secondary test window with different pattern
    secondaryWindow_ = std::make_unique<TestWindow>();
    TestWindow::TestPattern secondaryPattern;
    secondaryPattern.width = 350;
    secondaryPattern.height = 250;
    secondaryPattern.backgroundColor = RGB(0, 128, 255); // Light blue
    secondaryPattern.testRect = {30, 40, 80, 60, RGB(255, 255, 0)}; // Yellow
    secondaryPattern.testCircle = {180, 80, 60, 60, RGB(255, 0, 255)}; // Magenta
    secondaryPattern.text = "SECONDARY_TEST_WINDOW";
    
    if (!secondaryWindow_->Create(secondaryPattern)) {
        std::cerr << "Failed to create secondary test window!" << std::endl;
        return;
    }
    
    // Position secondary window offset from primary
    secondaryWindow_->MoveTo(550, 100);
    
    // Create dynamic window for movement tests
    dynamicWindow_ = std::make_unique<TestWindow>();
    TestWindow::TestPattern dynamicPattern;
    dynamicPattern.width = 300;
    dynamicPattern.height = 200;
    dynamicPattern.backgroundColor = RGB(128, 128, 128); // Gray
    dynamicPattern.testRect = {50, 30, 60, 40, RGB(255, 128, 0)}; // Orange
    dynamicPattern.testCircle = {150, 60, 50, 50, RGB(0, 255, 128)}; // Cyan
    dynamicPattern.text = "DYNAMIC_TEST_WINDOW";
    
    if (!dynamicWindow_->Create(dynamicPattern)) {
        std::cerr << "Failed to create dynamic test window!" << std::endl;
        return;
    }
    
    // Position dynamic window
    dynamicWindow_->MoveTo(900, 100);
    
    // Give windows time to fully initialize
    Sleep(200);
    
    initialized_ = true;
    std::cout << "Test environment initialized successfully" << std::endl;
}

void CaptureTestEnvironment::TearDown() {
    std::cout << "\n=== Tearing down global capture test environment ===" << std::endl;
    
    if (primaryWindow_) {
        std::cerr << "Destroying primaryWindow_" << std::endl;
        primaryWindow_->Destroy();
        std::cerr << "primaryWindow_->Destroy() completed" << std::endl;
        primaryWindow_.reset();
        std::cerr << "primaryWindow_.reset() completed" << std::endl;
    }
    
    if (secondaryWindow_) {
        std::cerr << "Destroying secondaryWindow_" << std::endl;
        secondaryWindow_->Destroy();
        std::cerr << "secondaryWindow_->Destroy() completed" << std::endl;
        secondaryWindow_.reset();
        std::cerr << "secondaryWindow_.reset() completed" << std::endl;
    }
    
    if (dynamicWindow_) {
        std::cerr << "Destroying dynamicWindow_" << std::endl;
        dynamicWindow_->Destroy();
        std::cerr << "dynamicWindow_->Destroy() completed" << std::endl;
        dynamicWindow_.reset();
        std::cerr << "dynamicWindow_.reset() completed" << std::endl;
    }
    
    initialized_ = false;
    std::cout << "Test environment cleaned up" << std::endl;
}

void CaptureTestEnvironment::EnumerateMonitors() {
    monitors_.clear();
    EnumDisplayMonitors(nullptr, nullptr, MonitorEnumProc, reinterpret_cast<LPARAM>(this));
}

BOOL CALLBACK CaptureTestEnvironment::MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, 
                                                      LPRECT lprcMonitor, LPARAM dwData) {
    CaptureTestEnvironment* env = reinterpret_cast<CaptureTestEnvironment*>(dwData);
    
    MONITORINFOEX mi;
    mi.cbSize = sizeof(MONITORINFOEX);
    if (GetMonitorInfo(hMonitor, &mi)) {
        MonitorInfo info;
        info.handle = hMonitor;
        info.rect = mi.rcMonitor;
        info.isPrimary = (mi.dwFlags & MONITORINFOF_PRIMARY) != 0;
        info.deviceName = mi.szDevice;
        
        env->monitors_.push_back(info);
    }
    
    return TRUE;
}

void CaptureTestEnvironment::MoveWindowToMonitor(TestWindow* window, int monitorIndex) {
    if (!window || monitorIndex < 0 || monitorIndex >= static_cast<int>(monitors_.size())) {
        return;
    }
    
    const auto& monitor = monitors_[monitorIndex];
    int x = monitor.rect.left + 50;
    int y = monitor.rect.top + 50;
    
    window->MoveTo(x, y);
    Sleep(100); // Give time for move
}

void CaptureTestEnvironment::MinimizeWindow(TestWindow* window) {
    if (window && window->GetHandle()) {
        ShowWindow(window->GetHandle(), SW_MINIMIZE);
        Sleep(100);
    }
}

void CaptureTestEnvironment::RestoreWindow(TestWindow* window) {
    if (window && window->GetHandle()) {
        ShowWindow(window->GetHandle(), SW_RESTORE);
        Sleep(100);
    }
}

void CaptureTestEnvironment::CloseWindow(TestWindow* window) {
    if (window && window->GetHandle()) {
        ShowWindow(window->GetHandle(), SW_HIDE);
        Sleep(100);
    }
}

RECT CaptureTestEnvironment::GetPrimaryDesktopRect() const {
    return GetMonitors().empty() ? RECT{} : GetMonitors()[0].rect;
}

RECT CaptureTestEnvironment::GetVirtualDesktopRect() const {
    if (monitors_.empty()) {
        return {};
    }
    
    RECT combined = monitors_[0].rect;
    for (size_t i = 1; i < monitors_.size(); ++i) {
        const auto& rect = monitors_[i].rect;
        combined.left = (std::min)(combined.left, rect.left);
        combined.top = (std::min)(combined.top, rect.top);
        combined.right = (std::max)(combined.right, rect.right);
        combined.bottom = (std::max)(combined.bottom, rect.bottom);
    }
    return combined;
}
