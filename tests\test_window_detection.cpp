#include <gtest/gtest.h>
#include <windows.h>
#include <string>
#include <vector>

// Test window detection functionality
class WindowDetectionTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup any test fixtures
    }
    
    void TearDown() override {
        // Cleanup
    }
};

// Helper function to enumerate windows
std::vector<HWND> GetAllWindows() {
    std::vector<HWND> windows;
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        auto* windows = reinterpret_cast<std::vector<HWND>*>(lParam);
        windows->push_back(hwnd);
        return TRUE;
    }, reinterpret_cast<LPARAM>(&windows));
    return windows;
}

// Helper function to get window title
std::string GetWindowTitle(HWND hwnd) {
    char title[256];
    GetWindowTextA(hwnd, title, sizeof(title));
    return std::string(title);
}

// Helper function to get window rect
RECT GetWindowRect(HWND hwnd) {
    RECT rect;
    ::GetWindowRect(hwnd, &rect);
    return rect;
}

TEST_F(WindowDetectionTest, CanEnumerateWindows) {
    auto windows = GetAllWindows();
    
    // Should have at least some windows (desktop, taskbar, etc.)
    EXPECT_GT(windows.size(), 0);
    
    // Verify we can get properties of each window
    int validWindows = 0;
    for (HWND hwnd : windows) {
        if (IsWindow(hwnd)) {
            validWindows++;
            
            // Should be able to get window rect
            RECT rect = GetWindowRect(hwnd);
            EXPECT_LE(rect.left, rect.right);
            EXPECT_LE(rect.top, rect.bottom);
        }
    }
    
    EXPECT_GT(validWindows, 0);
}

TEST_F(WindowDetectionTest, CanFindVisibleWindows) {
    auto windows = GetAllWindows();
    
    int visibleWindows = 0;
    for (HWND hwnd : windows) {
        if (IsWindowVisible(hwnd) && !IsIconic(hwnd)) {
            visibleWindows++;
            
            // Visible window should have non-zero dimensions
            RECT rect = GetWindowRect(hwnd);
            int width = rect.right - rect.left;
            int height = rect.bottom - rect.top;
            
            // Most visible windows should have reasonable size
            // (though some system windows might be very small)
            if (width > 10 && height > 10) {
                EXPECT_GT(width, 0);
                EXPECT_GT(height, 0);
            }
        }
    }
    
    // Should have at least a few visible windows
    EXPECT_GT(visibleWindows, 0);
}

TEST_F(WindowDetectionTest, CanGetWindowProperties) {
    auto windows = GetAllWindows();
    
    for (HWND hwnd : windows) {
        if (IsWindowVisible(hwnd)) {
            // Test getting window title
            std::string title = GetWindowTitle(hwnd);
            // Title can be empty, that's valid
            
            // Test getting window class
            char className[256];
            int result = GetClassNameA(hwnd, className, sizeof(className));
            EXPECT_GT(result, 0); // Should get some class name
            
            // Test window state
            BOOL isVisible = IsWindowVisible(hwnd);
            BOOL isMinimized = IsIconic(hwnd);
            
            // If window is minimized, it should still be visible
            if (isMinimized) {
                EXPECT_TRUE(isVisible);
            }
            
            break; // Just test one visible window
        }
    }
}

TEST_F(WindowDetectionTest, WindowRectConsistency) {
    auto windows = GetAllWindows();
    
    for (HWND hwnd : windows) {
        if (IsWindowVisible(hwnd) && !IsIconic(hwnd)) {
            RECT rect1 = GetWindowRect(hwnd);
            
            // Get rect again - should be the same (unless window moved)
            RECT rect2 = GetWindowRect(hwnd);
            
            // For non-animated windows, should be very close
            EXPECT_NEAR(rect1.left, rect2.left, 5);
            EXPECT_NEAR(rect1.top, rect2.top, 5);
            EXPECT_NEAR(rect1.right, rect2.right, 5);
            EXPECT_NEAR(rect1.bottom, rect2.bottom, 5);
            
            break; // Just test one window
        }
    }
}
