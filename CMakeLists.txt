cmake_minimum_required(VERSION 3.10)
project(capscr VERSION 0.1 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Options
option(CAPSCR_BUILD_EXAMPLES "Build examples" ON)
option(CAPSCR_BUILD_TESTS "Build unit tests" ON)

add_library(capscr INTERFACE)

target_include_directories(capscr INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>)

if(CAPSCR_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Add Google Test and testing
if(CAPSCR_BUILD_TESTS)
    include(FetchContent)
    FetchContent_Declare(
        googletest
        URL https://github.com/google/googletest/archive/refs/tags/v1.14.0.zip
        DOWNLOAD_EXTRACT_TIMESTAMP TRUE
    )
    # For Windows: Prevent overriding the parent project's compiler/linker settings
    set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
    FetchContent_MakeAvailable(googletest)
    
    enable_testing()
    add_subdirectory(tests)
endif()

# Add spdlog for logging
include(FetchContent)
FetchContent_Declare(
    spdlog
    GIT_REPOSITORY https://github.com/gabime/spdlog.git
    GIT_TAG v1.12.0
)
FetchContent_MakeAvailable(spdlog)

if(WIN32)
    # DXGI backend + GDI fallback + logging + modular DXGI components
    add_library(capscr_win STATIC
        src/windows/capturer_gdi.cpp
        src/windows/capturer_dxgi.cpp
        src/windows/dxgi_device.cpp
        src/windows/dxgi_texture.cpp
    src/common/process_frame.cpp
    src/common/image_utils.cpp
        src/windows/dxgi_compute.cpp
        src/common/logging.cpp
    )
    target_include_directories(capscr_win PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include)
    target_compile_definitions(capscr_win PRIVATE "WIN32_LEAN_AND_MEAN")
    target_link_libraries(capscr_win PRIVATE user32 gdi32 dxgi d3d11 d3dcompiler spdlog::spdlog)
    target_link_libraries(capscr INTERFACE capscr_win)
    # common helpers removed; examples will link third-party image libs directly
elseif(UNIX)
    # stubs for other platforms will be added later
endif()
