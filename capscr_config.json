{"logging": {"level": "info", "console_enabled": true, "file_enabled": true, "file_path": "logs/capscr.log", "max_file_size": 5242880, "max_files": 3, "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%t] [%^%l%$] %v", "auto_flush": true, "module_levels": {"capscr.gpu": "debug", "capscr.capture": "info", "capscr.config": "warn"}}, "capture": {"gpu_texture_enabled": true, "performance_logging": true, "capture_timeout_ms": 1000, "preferred_adapter": ""}, "config_file_path": "capscr_config.json", "auto_reload": false, "reload_interval_ms": 5000}