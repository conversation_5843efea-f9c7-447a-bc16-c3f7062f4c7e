// 编码器集成示例
#include "include/capscr/capture.hpp"

// 模拟的编码器接口
class VideoEncoder {
public:
    // 编码器期望的输入：共享句柄
    virtual bool EncodeFrame(HANDLE yHandle, HANDLE uvHandle, 
                           int width, int height) = 0;
};

class NVENCEncoder : public VideoEncoder {
public:
    bool EncodeFrame(HANDLE yHandle, HANDLE uvHandle, int width, int height) override {
        // NVENC直接使用共享句柄创建CUDA纹理
        // 这是编码器的标准输入格式
        return true;
    }
};

// 方案1：使用captureZeroCopy (需要额外转换)
void encodeWithZeroCopy(ICapturer* capturer, VideoEncoder* encoder) {
    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    ZeroCopyFrame frame;
    Result result = capturer->captureZeroCopy(frame, opts);
    
    if (result == Result::Ok && frame.is_nv12()) {
        // 【额外转换步骤开始】
        // 1. 从纹理指针创建资源接口
        ComPtr<IDXGIResource1> yResource, uvResource;
        HRESULT hr1 = frame.d3d11_texture->QueryInterface(
            __uuidof(IDXGIResource1), (void**)&yResource);
        HRESULT hr2 = frame.d3d11_texture_uv->QueryInterface(
            __uuidof(IDXGIResource1), (void**)&uvResource);
        
        if (SUCCEEDED(hr1) && SUCCEEDED(hr2)) {
            // 2. 创建共享句柄
            HANDLE yHandle = nullptr, uvHandle = nullptr;
            HRESULT hr3 = yResource->CreateSharedHandle(
                nullptr, GENERIC_ALL, nullptr, &yHandle);
            HRESULT hr4 = uvResource->CreateSharedHandle(
                nullptr, GENERIC_ALL, nullptr, &uvHandle);
            
            if (SUCCEEDED(hr3) && SUCCEEDED(hr4)) {
                // 3. 现在才能传递给编码器
                encoder->EncodeFrame(yHandle, uvHandle, frame.width, frame.height);
                
                // 4. 手动清理句柄
                CloseHandle(yHandle);
                CloseHandle(uvHandle);
            }
        }
        // 【额外转换步骤结束】
    }
}

// 方案2：使用captureSharedTexture (无需额外转换)
void encodeWithSharedTexture(ICapturer* capturer, VideoEncoder* encoder) {
    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    SharedTextureInfo info;
    Result result = capturer->captureSharedTexture(info, opts);
    
    if (result == Result::Ok && info.is_nv12()) {
        // 【无需转换】直接传递给编码器
        encoder->EncodeFrame(info.shared_handle, info.shared_handle_uv, 
                           info.width, info.height);
        
        // 清理（编码器使用完毕后）
        CloseHandle(info.shared_handle);
        CloseHandle(info.shared_handle_uv);
    }
}
