#include <gtest/gtest.h>
#include "capscr/capture.hpp"
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include "capture_test_environment.hpp"
#include <memory>
#ifdef _WIN32
#include <dxgi.h>
#include <windows.h>
#endif

#ifdef _WIN32
// (edge-case tests moved below after the test fixture)
#endif

using namespace capscr;

class DXGIZeroCopyTest : public ::testing::Test {
protected:
    void SetUp() override {
#ifdef _WIN32
        capturer = createDXGICapturer();
        if (capturer) {
            auto r = capturer->init(CaptureTargetType::FullScreen, "", nullptr, "");
            initialized = (r == Result::Ok);
        }
#endif
    }

    void TearDown() override {
        capturer.reset();
    }

    std::unique_ptr<ICapturer> capturer;
    bool initialized = false;
};

#ifdef _WIN32

// Basic zero-copy smoke test
TEST_F(DXGIZeroCopyTest, CaptureZeroCopyBasic) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyFrame frame;
    ZeroCopyOptions opts;
    // default opts: LocalBorrow
    auto res = capturer->captureZeroCopy(frame, opts);
    if (res != Result::Ok) {
        // It's acceptable that no new frame is available in CI; treat as skip
        GTEST_SKIP() << "captureZeroCopy did not return Ok: " << static_cast<int>(res);
    }

    // Validate returned frame
    EXPECT_NE(frame.d3d11_texture, nullptr);
    EXPECT_GT(frame.width, 0);
    EXPECT_GT(frame.height, 0);

    // Release frame explicitly
    frame.release();
}

// New: request GPU processing via captureZeroCopy overload
TEST_F(DXGIZeroCopyTest, CaptureZeroCopyProcessed) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyFrame frame;
    ZeroCopyOptions opts;
    CaptureParams params;
    params.use_gpu_processing = true;
    params.output_width = 320;
    params.output_height = 240;
    params.output_format = PixelFormat::BGRA32;

    auto res = capturer->captureZeroCopy(frame, opts, &params);
    if (res != Result::Ok) {
        GTEST_SKIP() << "captureZeroCopy(processing) not supported on this host: " << static_cast<int>(res);
    }

    // Validate processed frame
    EXPECT_GT(frame.width, 0);
    EXPECT_GT(frame.height, 0);
    EXPECT_EQ(frame.width, params.output_width);
    EXPECT_EQ(frame.height, params.output_height);
    EXPECT_EQ(frame.format, params.output_format);

    frame.release();
}

// Resize + zero-copy (GPU processing)
TEST_F(DXGIZeroCopyTest, CaptureZeroCopyResize) {
    if (!capturer || !initialized || !capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "Skip: no GPU capture";
    // Request smaller output
    CaptureParams params;
    params.output_width = 320;
    params.output_height = 240;
    params.output_format = PixelFormat::BGRA32;
    params.use_gpu_processing = true;

    // The API doesn't accept params directly via captureZeroCopy (depending on header); some implementations
    // read global capture params. Fall back to calling capture with GPU processing and validate.
    Frame out;
    out.width = params.output_width; // best-effort check if API fills

    // Try capture via ordinary capture with GPU processing to exercise resize pipeline
    CaptureParams cp = params;
    cp.use_gpu_processing = true;
    Result r = capturer->capture(out, cp);
    if (r != Result::Ok) {
        GTEST_SKIP() << "capture with GPU processing not Ok: " << static_cast<int>(r);
    }

    EXPECT_EQ(out.width, 320);
    EXPECT_EQ(out.height, 240);
    EXPECT_EQ(out.format, PixelFormat::BGRA32);
}

// Pixel format conversion test
TEST_F(DXGIZeroCopyTest, CaptureZeroCopyFormatConvert) {
    if (!capturer || !initialized) GTEST_SKIP() << "Skip: no capturer";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";
    // Test format conversion to RGBA32 via GPU processing
    CaptureParams params;
    params.use_gpu_processing = true;
    params.output_format = PixelFormat::RGBA32;
    params.output_width = 320;
    params.output_height = 240;

    Frame out;
    auto r = capturer->capture(out, params);
    if (r != Result::Ok) {
        GTEST_SKIP() << "capture with format conversion not Ok: " << static_cast<int>(r);
    }

    EXPECT_EQ(out.width, params.output_width);
    EXPECT_EQ(out.height, params.output_height);
    EXPECT_EQ(out.format, PixelFormat::RGBA32);
    EXPECT_GT(out.data.size(), 0);
}
// NV12 zero-copy test: use captureZeroCopy with NV12 format
TEST_F(DXGIZeroCopyTest, CaptureZeroCopyNV12) {
    if (!capturer || !initialized || !capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "Skip: no GPU capture";

    ZeroCopyFrame frame;
    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    auto r = capturer->captureZeroCopy(frame, opts);
    if (r != Result::Ok) {
        GTEST_SKIP() << "captureZeroCopy (NV12) not supported: " << static_cast<int>(r);
    }

    EXPECT_TRUE(frame.format == PixelFormat::NV12);
    EXPECT_TRUE(frame.is_nv12());
    EXPECT_NE(frame.d3d11_texture, nullptr);
    EXPECT_NE(frame.d3d11_texture_uv, nullptr);
}

// Error cases: Before init or when GPU not available
TEST(DXGIZeroCopyErrorCases, CaptureZeroCopyBeforeInit) {
    auto fresh = createDXGICapturer();
    if (!fresh) GTEST_SKIP() << "DXGI capturer not available";

    ZeroCopyFrame frame;
    ZeroCopyOptions opts;
    auto r = fresh->captureZeroCopy(frame, opts);
    // Expect non-Ok (graceful failure) when not initialized
    EXPECT_NE(r, Result::Ok);
}

#else

TEST_F(DXGIZeroCopyTest, PlatformUnsupported) {
    GTEST_SKIP() << "DXGI tests only supported on Windows";
}

#endif

// Insert edge-case tests here (Windows-only)
#ifdef _WIN32
// Edge-case: when shaders are missing (GPU compute unavailable), capture with use_gpu_processing=true
TEST_F(DXGIZeroCopyTest, CaptureShaderFallbackToCPU) {
    if (!capturer || !initialized) GTEST_SKIP() << "Skip: no capturer";

    // Force request GPU processing; implementation may fall back to CPU when shaders missing
    CaptureParams params;
    params.use_gpu_processing = true;
    params.output_width = 320;
    params.output_height = 240;
    params.output_format = PixelFormat::BGRA32;

    Frame out;
    auto r = capturer->capture(out, params);
    // Either Ok (GPU used) or Ok with CPU fallback; both are acceptable for this environment
    EXPECT_TRUE(r == Result::Ok);
    if (r == Result::Ok) {
        EXPECT_EQ(out.width, params.output_width);
        EXPECT_EQ(out.height, params.output_height);
    }
}

// Edge-case: zero-dimension output requested
TEST_F(DXGIZeroCopyTest, CaptureZeroDimensionParams) {
    if (!capturer || !initialized) GTEST_SKIP() << "Skip: no capturer";

    CaptureParams params;
    params.use_gpu_processing = false;
    params.output_width = 0;
    params.output_height = 0;
    params.output_format = PixelFormat::BGRA32;

    Frame out;
    auto r = capturer->capture(out, params);
    // Implementation should handle gracefully: either return non-Ok or sanitize to valid dims
    if (r == Result::Ok) {
        EXPECT_GT(out.width, 0);
        EXPECT_GT(out.height, 0);
    } else {
        SUCCEED() << "capture returned non-Ok for zero-dimension params as expected: " << static_cast<int>(r);
    }
}

// Edge-case: unsupported/invalid pixel format request
TEST_F(DXGIZeroCopyTest, CaptureUnsupportedFormat) {
    if (!capturer || !initialized) GTEST_SKIP() << "Skip: no capturer";

    CaptureParams params;
    params.use_gpu_processing = false;
    // Pick an obviously invalid enum value by casting an out-of-range int
    params.output_format = static_cast<PixelFormat>(0xdeadbeef);
    params.output_width = 320;
    params.output_height = 240;

    Frame out;
    auto r = capturer->capture(out, params);
    // Expect the capturer to return an error or at least not crash
    EXPECT_NE(r, Result::Ok);
}
#endif


