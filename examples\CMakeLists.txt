add_executable(example_capture 
    example_capture.cpp
    ${CMAKE_SOURCE_DIR}/third_party/lodepng.cpp
)

add_executable(test_window_only 
    test_window_only.cpp
    ${CMAKE_SOURCE_DIR}/third_party/lodepng.cpp
)

add_executable(test_gpu_capture 
    test_gpu_capture.cpp
)

add_executable(simple_gpu_test 
    simple_gpu_test.cpp
)

add_executable(list_windows 
    list_windows.cpp
)

add_executable(test_flutter_capture 
    test_flutter_capture.cpp
)

# New NV12 examples
add_executable(simple_nv12_example
    simple_nv12_example.cpp
)

add_executable(nv12_zerocopy_demo
    nv12_zerocopy_demo.cpp
)

add_executable(encoder_integration_example
    encoder_integration_example.cpp
)

# Include lodepng from third_party directory
target_include_directories(example_capture PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/third_party
)
target_compile_definitions(example_capture PRIVATE HAVE_LODEPNG=1)
target_link_libraries(example_capture PRIVATE capscr)

target_include_directories(test_window_only PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/third_party
)
target_compile_definitions(test_window_only PRIVATE HAVE_LODEPNG=1)
target_link_libraries(test_window_only PRIVATE capscr)

target_link_libraries(list_windows PRIVATE user32)

target_link_libraries(test_flutter_capture PRIVATE user32 gdi32)

# Test GPU capture and logging
target_include_directories(test_gpu_capture PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
)
target_link_libraries(test_gpu_capture PRIVATE capscr)

# Simple GPU test without logging
target_include_directories(simple_gpu_test PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
)
target_link_libraries(simple_gpu_test PRIVATE capscr)

# NV12 examples
target_include_directories(simple_nv12_example PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
)
target_link_libraries(simple_nv12_example PRIVATE capscr)

target_include_directories(nv12_zerocopy_demo PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
)
target_link_libraries(nv12_zerocopy_demo PRIVATE capscr d3d11 dxgi)

target_include_directories(encoder_integration_example PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
)
target_link_libraries(encoder_integration_example PRIVATE capscr d3d11 dxgi)

# Test PrintWindow debugging
add_executable(test_printwindow_debug test_printwindow_debug.cpp ${LODEPNG_SOURCES})
target_include_directories(test_printwindow_debug PRIVATE ${CMAKE_SOURCE_DIR}/third_party)
if(HAVE_LODEPNG)
    target_compile_definitions(test_printwindow_debug PRIVATE HAVE_LODEPNG)
endif()
target_link_libraries(test_printwindow_debug PRIVATE user32 gdi32)

## removed unused example targets: producer_consumer_test, single_process_shared_test,
## single_process_shared_test_reduced, zero_copy_repro, in_process_zero_copy_demo

add_executable(dxgi_zero_copy_demo
    dxgi_zero_copy_demo.cpp
)
target_include_directories(dxgi_zero_copy_demo PRIVATE ${CMAKE_SOURCE_DIR}/include)
target_link_libraries(dxgi_zero_copy_demo PRIVATE capscr)

# Ensure runtime shaders are available to examples. Copy HLSL shader sources to the examples runtime dir.
file(GLOB CAPSCR_HLSL_SOURCES "${CMAKE_SOURCE_DIR}/src/windows/shaders/*.hlsl")

# Find DXC or FXC for offline HLSL compilation to .cso
find_program(DXC_EXE dxc HINTS ENV PATH)
find_program(FXC_EXE fxc HINTS ENV PATH)

# Map of hlsl source -> required entry points. Keep one list per .hlsl file
set(HLSL_COMPILE_LIST
    "${CMAKE_SOURCE_DIR}/src/windows/shaders/resize.hlsl;CSHorizontalResize;CSVerticalResize;CSSinglePassResize"
    "${CMAKE_SOURCE_DIR}/src/windows/shaders/convert.hlsl;CSConvertPixelFormat;CSConvertBGRAToRGBA;CSConvertRGBAToBGRA"
    "${CMAKE_SOURCE_DIR}/src/windows/shaders/convert_nv12.hlsl;CSNV12_Y_Plane;CSNV12_UV_Plane"
)

if(DXC_EXE OR FXC_EXE)
    message(STATUS "Found HLSL compiler: ${DXC_EXE}${FXC_EXE}")

    # Create output dir then compile each entry point to a .cso
    foreach(item IN LISTS HLSL_COMPILE_LIST)
        string(REPLACE ";" ";" parts "${item}")
        list(GET parts 0 HLSL_FILE)
        # remaining are entry points
        list(LENGTH parts parts_len)
        math(EXPR entry_count "${parts_len} - 1")
        foreach(i RANGE 1 ${entry_count})
            list(GET parts ${i} ENTRY_POINT)
            get_filename_component(HLSL_BASENAME ${HLSL_FILE} NAME_WE)
            set(CSO_OUT "${CMAKE_CURRENT_BINARY_DIR}/shaders/${HLSL_BASENAME}_${ENTRY_POINT}.cso")
            if(DXC_EXE)
                add_custom_command(
                    OUTPUT ${CSO_OUT}
                    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/shaders"
                    COMMAND ${DXC_EXE} -T cs_5_0 -E ${ENTRY_POINT} -Fo ${CSO_OUT} ${HLSL_FILE}
                    DEPENDS ${HLSL_FILE}
                    COMMENT "Compiling ${HLSL_FILE} entry ${ENTRY_POINT} -> ${CSO_OUT} (dxc)"
                    VERBATIM
                )
            elseif(FXC_EXE)
                add_custom_command(
                    OUTPUT ${CSO_OUT}
                    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/shaders"
                    COMMAND ${FXC_EXE} /T cs_5_0 /E ${ENTRY_POINT} /Fo ${CSO_OUT} ${HLSL_FILE}
                    DEPENDS ${HLSL_FILE}
                    COMMENT "Compiling ${HLSL_FILE} entry ${ENTRY_POINT} -> ${CSO_OUT} (fxc)"
                    VERBATIM
                )
            else()
                # Should not reach here because outer if checks existence, but keep fallback
                add_custom_command(
                    OUTPUT ${CSO_OUT}
                    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/shaders"
                    COMMENT "No HLSL compiler found for ${HLSL_FILE}; no .cso will be generated"
                    VERBATIM
                )
            endif()
            list(APPEND GENERATED_CSOS ${CSO_OUT})
        endforeach()
    endforeach()

    # Add a custom target to build all CSOs and ensure dxgi example depends on it
    add_custom_target(capscr_shaders ALL DEPENDS ${GENERATED_CSOS})
    add_dependencies(dxgi_zero_copy_demo capscr_shaders)
    add_dependencies(nv12_zerocopy_demo capscr_shaders)
    add_dependencies(encoder_integration_example capscr_shaders)

    # After build, copy compiled .cso files to the runtime shaders dir
    add_custom_command(TARGET dxgi_zero_copy_demo POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:dxgi_zero_copy_demo>/shaders"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different ${GENERATED_CSOS} "$<TARGET_FILE_DIR:dxgi_zero_copy_demo>/shaders"
    )
    
    add_custom_command(TARGET nv12_zerocopy_demo POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:nv12_zerocopy_demo>/shaders"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different ${GENERATED_CSOS} "$<TARGET_FILE_DIR:nv12_zerocopy_demo>/shaders"
    )
    
    add_custom_command(TARGET encoder_integration_example POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:encoder_integration_example>/shaders"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different ${GENERATED_CSOS} "$<TARGET_FILE_DIR:encoder_integration_example>/shaders"
    )
else()
    # Fallback: copy .hlsl sources into runtime shaders dir so runtime compile can occur
    file(GLOB CAPSCR_SHADERS "${CMAKE_SOURCE_DIR}/src/windows/shaders/*.hlsl")
    if(CAPSCR_SHADERS)
        add_custom_command(TARGET dxgi_zero_copy_demo POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:dxgi_zero_copy_demo>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_SHADERS} "$<TARGET_FILE_DIR:dxgi_zero_copy_demo>/shaders"
        )
        
        add_custom_command(TARGET nv12_zerocopy_demo POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:nv12_zerocopy_demo>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_SHADERS} "$<TARGET_FILE_DIR:nv12_zerocopy_demo>/shaders"
        )
        
        add_custom_command(TARGET encoder_integration_example POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:encoder_integration_example>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_SHADERS} "$<TARGET_FILE_DIR:encoder_integration_example>/shaders"
        )
    endif()
endif()
