#include <iostream>
#include <iostream>
#include <memory>
#include "capscr/capture.hpp"
#ifdef _WIN32
#include "capscr/platform/windows/capturer_dxgi.hpp"
#endif
#include <fstream>
#include <vector>
#ifdef HAVE_LODEPNG
#include <lodepng.h>
#endif
#include <thread>
#include <chrono>

using namespace capscr;

int main(int argc, char** argv) {
    using namespace capscr;
    bool forceDXGI = false;
    std::string requestedDisplayId;
    std::string requestedWindow;
    std::vector<std::string> positional;
    for (int i = 1; i < argc; ++i) {
        std::string a = argv[i];
        if (a == "--dxgi") {
            forceDXGI = true;
        } else if (a == "--display" && i + 1 < argc) {
            requestedDisplayId = argv[++i];
        } else if (a == "--window" && i + 1 < argc) {
            requestedWindow = argv[++i];
        } else if (!a.empty() && a[0] == '-') {
            // unknown flag - ignore
        } else {
            positional.push_back(a);
        }
    }
    if (requestedWindow.empty() && !positional.empty()) {
        // Defensive: only treat a positional argument as window title if it doesn't start with '-'
        if (!positional[0].empty() && positional[0][0] != '-') requestedWindow = positional[0];
    }

    auto capturer = createBestCapturer();
#ifdef _WIN32
    if (forceDXGI) {
        try {
            auto dx = capscr::createDXGICapturer();
            if (dx) capturer = std::move(dx);
            else std::cout << "--dxgi requested but DXGI backend not available, continuing with default.\n";
        } catch(...) { std::cout << "--dxgi requested but failed to create DXGI backend, continuing.\n"; }
    }
#endif
    if (!capturer) {
        std::cerr << "No capturer available on this platform.\n";
        return 1;
    }
    if (!capturer) {
        std::cerr << "No capturer available on this platform.\n";
        return 1;
    }

    // list displays
    auto displays = capturer->listDisplays();
    std::cout << "Displays found: " << displays.size() << "\n";
    for (size_t i = 0; i < displays.size(); ++i) {
        std::cout << i << ": id=" << displays[i].id << " name=" << displays[i].name << " bounds=" << displays[i].bounds.width << "x" << displays[i].bounds.height << "\n";
    }

    // Capture a region on primary display (use first display id if available)
    std::string primaryId;
    if (!displays.empty()) primaryId = displays[0].id;
    if (!requestedDisplayId.empty()) primaryId = requestedDisplayId;
    Rect r1{100,100,400,300};
    Result r = capturer->init(CaptureTargetType::Region, primaryId, &r1);
    if (r != Result::Ok) {
        std::cerr << "Init failed or unsupported\n";
        return 2;
    }

    Frame f;
    r = capturer->capture(f);
    if (r == Result::Ok) {
        std::cout << "Captured region frame: " << f.width << "x" << f.height << "\n";
    // Write PNG using lodepng; if not available, skip writing
#ifdef HAVE_LODEPNG
    // Convert BGRA -> RGBA
    std::vector<uint8_t> rgba;
    rgba.resize(f.width * f.height * 4);
    for (int y = 0; y < f.height; ++y) {
        const uint8_t* src = f.data.data() + y * f.stride;
        for (int x = 0; x < f.width; ++x) {
        int si = x * 4;
        int di = (y * f.width + x) * 4;
        rgba[di+0] = src[si+2];
        rgba[di+1] = src[si+1];
        rgba[di+2] = src[si+0];
        rgba[di+3] = src[si+3];
        }
    }
    unsigned err = lodepng::encode("capture_region1.png", rgba, f.width, f.height);
    if (err == 0) std::cout << "Saved capture_region1 image\n";
#else
    std::cout << "HAVE_LODEPNG not defined - skipping image write for region capture\n";
#endif
    } else {
        std::cout << "Capture result: " << (int)r << "\n";
    }

    // change region
    Rect r2{200,150,300,200};
    capturer->setTarget(CaptureTargetType::Region, primaryId, &r2);
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    r = capturer->capture(f);
    if (r == Result::Ok) {
        std::cout << "Captured region frame 2: " << f.width << "x" << f.height << "\n";
        bool wrote = false;
#ifdef HAVE_LODEPNG
        std::vector<uint8_t> rgba;
        rgba.resize(f.width * f.height * 4);
        for (int y = 0; y < f.height; ++y) {
            const uint8_t* src = f.data.data() + y * f.stride;
            for (int x = 0; x < f.width; ++x) {
                int si = x * 4;
                int di = (y * f.width + x) * 4;
                rgba[di+0] = src[si+2];
                rgba[di+1] = src[si+1];
                rgba[di+2] = src[si+0];
                rgba[di+3] = src[si+3];
            }
        }
        unsigned err = lodepng::encode("capture_region2.png", rgba, f.width, f.height);
        wrote = (err == 0);
#else
        auto writeBmp = [&](const std::string& path)->bool{
            std::ofstream of(path, std::ios::binary);
            if (!of) return false;
            
            // BMP file header (14 bytes)
            const int headersize = 14 + 40;
            int imgsize = f.stride * f.height;
            int filesize = headersize + imgsize;
            
            unsigned char file[14] = {'B','M'};
            file[2] = (unsigned char)(filesize);
            file[3] = (unsigned char)(filesize>>8);
            file[4] = (unsigned char)(filesize>>16);
            file[5] = (unsigned char)(filesize>>24);
            // reserved fields (6-9) = 0
            file[10] = headersize; // offset to pixel data
            file[11] = 0;
            file[12] = 0;
            file[13] = 0;
            
            // DIB header (40 bytes)
            unsigned char info[40] = {};
            info[0] = 40; // DIB header size
            info[1] = 0; info[2] = 0; info[3] = 0;
            
            int w = f.width, h = f.height;
            info[4] = (unsigned char)w;
            info[5] = (unsigned char)(w>>8);
            info[6] = (unsigned char)(w>>16);
            info[7] = (unsigned char)(w>>24);
            info[8] = (unsigned char)h;
            info[9] = (unsigned char)(h>>8);
            info[10] = (unsigned char)(h>>16);
            info[11] = (unsigned char)(h>>24);
            
            info[12] = 1; // planes
            info[13] = 0;
            info[14] = 32; // bits per pixel
            info[15] = 0;
            // compression = 0 (BI_RGB)
            // image size can be 0 for BI_RGB
            // XPelsPerMeter, YPelsPerMeter = 0
            // colors used = 0
            // colors important = 0
            
            of.write(reinterpret_cast<char*>(file), 14);
            of.write(reinterpret_cast<char*>(info), 40);
            for (int y = f.height-1; y >= 0; --y) {
                of.write(reinterpret_cast<const char*>(f.data.data() + y*f.stride), f.stride);
            }
            return true;
        };
        wrote = writeBmp("capture_region2.bmp");
#endif
    std::cout << "capture_region2: write step omitted when HAVE_LODEPNG not defined\n";
    } else {
        std::cout << "Capture result: " << (int)r << "\n";
    }

    // Example program focuses on region captures and PNG output only.
    capturer->shutdown();
    return 0;
}
