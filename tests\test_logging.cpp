#include <gtest/gtest.h>
#include <memory>
#include "capscr/logging.hpp"

// Simple logging tests that work with existing initialized logging system
class LoggingSystemTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// Test that logger exists and is usable
TEST_F(LoggingSystemTest, LoggerExists) {
    auto logger = capscr::logging::get_logger();
    EXPECT_NE(logger, nullptr);
}

// Test that logging macros work without throwing
TEST_F(LoggingSystemTest, LoggingMacrosWork) {
    EXPECT_NO_THROW(CAPSCR_LOG_TRACE("Trace test message"));
    EXPECT_NO_THROW(CAPSCR_LOG_DEBUG("Debug test message"));
    EXPECT_NO_THROW(CAPSCR_LOG_INFO("Info test message"));
    EXPECT_NO_THROW(CAPSCR_LOG_WARN("Warning test message"));
    EXPECT_NO_THROW(CAPSCR_LOG_ERROR("Error test message"));
    EXPECT_NO_THROW(CAPSCR_LOG_CRITICAL("Critical test message"));
}

// Test log level setting
TEST_F(LoggingSystemTest, LogLevelSetting) {
    auto logger = capscr::logging::get_logger();
    ASSERT_NE(logger, nullptr);
    
    // Test setting different log levels
    capscr::logging::set_level(capscr::LogLevel::Info);
    EXPECT_EQ(logger->level(), capscr::LogLevel::Info);
    
    capscr::logging::set_level(capscr::LogLevel::Debug);
    EXPECT_EQ(logger->level(), capscr::LogLevel::Debug);
    
    capscr::logging::set_level(capscr::LogLevel::Error);
    EXPECT_EQ(logger->level(), capscr::LogLevel::Error);
}

// Test custom logger injection capability
TEST_F(LoggingSystemTest, CustomLoggerSupport) {
    // Create a test logger
    class SimpleTestLogger : public capscr::ILogger {
    public:
        void log(capscr::LogLevel level, const std::string& message) override {
            last_level = level;
            last_message = message;
        }
        
        void set_level(capscr::LogLevel level) override {
            current_level = level;
        }
        
        capscr::LogLevel level() const override {
            return current_level;
        }
        
        capscr::LogLevel last_level = capscr::LogLevel::Info;
        std::string last_message;
        capscr::LogLevel current_level = capscr::LogLevel::Info;
    };
    
    auto test_logger = std::make_shared<SimpleTestLogger>();
    
    // Save current logger to restore later
    auto original_logger = capscr::logging::get_logger();
    
    // Set our test logger
    capscr::logging::set_logger(test_logger);
    
    // Verify it's active
    auto active_logger = capscr::logging::get_logger();
    EXPECT_EQ(active_logger, test_logger);
    
    // Test logging through it
    test_logger->warn("Test warning");
    EXPECT_EQ(test_logger->last_level, capscr::LogLevel::Warn);
    EXPECT_EQ(test_logger->last_message, "Test warning");
    
    // Restore original logger
    capscr::logging::set_logger(original_logger);
}
