#ifdef _WIN32
#include <windows.h>
#include <dxgi1_2.h>
#include <d3d11.h>
#include <wrl.h>
#include <vector>
#include <string>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <iostream>

#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/platform/windows/capturer_dxgi.hpp"
#include "../../include/capscr/logging.hpp"
#include "dxgi_compute.hpp"
using Microsoft::WRL::ComPtr;

namespace capscr {

// Helper: bytes per pixel for common DXGI formats (fallback to 4)
static int dxgi_bytes_per_pixel(DXGI_FORMAT fmt) {
    switch (fmt) {
        case DXGI_FORMAT_R8G8B8A8_UNORM: return 4;
        case DXGI_FORMAT_B8G8R8A8_UNORM: return 4;
        case DXGI_FORMAT_B8G8R8X8_UNORM: return 4;
        case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB: return 4;
        default: return 4;
    }
}

// Helper: count non-zero bytes in a buffer
static size_t count_nonzero_bytes(const void* ptr, size_t n) {
    const uint8_t* p = static_cast<const uint8_t*>(ptr);
    size_t c = 0;
    for (size_t i = 0; i < n; ++i) if (p[i] != 0) ++c;
    return c;
}

// Helper: check if frame is black/empty based on threshold
static bool is_frame_black(const Frame& frame, float threshold = 0.01f) {
    if (frame.data.empty()) return true;
    
    size_t nonzero = count_nonzero_bytes(frame.data.data(), frame.data.size());
    float ratio = static_cast<float>(nonzero) / static_cast<float>(frame.data.size());
    return ratio < threshold;
}

// Helper: check if mapped buffer is black/empty
static bool is_mapped_buffer_black(const void* data, size_t size, float threshold = 0.01f) {
    if (!data || size == 0) return true;
    
    size_t nonzero = count_nonzero_bytes(data, size);
    float ratio = static_cast<float>(nonzero) / static_cast<float>(size);
    return ratio < threshold;
}

// Helper to convert HRESULT to readable string for diagnostics
static std::string hr_to_string(HRESULT hr) {
    char buf[512] = {0};
    DWORD flags = FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS;
    DWORD len = FormatMessageA(flags, NULL, static_cast<DWORD>(hr), MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), buf, (DWORD)sizeof(buf), NULL);
    if (len == 0) {
        // Fallback to hex string if FormatMessage failed
        char tmp[64];
        sprintf_s(tmp, sizeof(tmp), "0x%08x", hr);
        return std::string(tmp);
    }
    // trim trailing newlines
    while (len > 0 && (buf[len-1] == '\n' || buf[len-1] == '\r')) { buf[--len] = '\0'; }
    return std::string(buf);
}

// Minimal GDI fallback used when DXGI duplication is unavailable at capture time.
static bool gdi_capture_rect(const capscr::Rect &r, capscr::Frame &outFrame, HWND hwnd = nullptr) {
    HDC hScreenDC = GetDC(nullptr);
    HDC hMemDC = CreateCompatibleDC(hScreenDC);
    if (!hMemDC) return false;
    int w = r.width;
    int h = r.height;
    HBITMAP hBitmap = CreateCompatibleBitmap(hScreenDC, w, h);
    if (!hBitmap) { DeleteDC(hMemDC); ReleaseDC(nullptr, hScreenDC); return false; }
    SelectObject(hMemDC, hBitmap);
    
    BOOL success = FALSE;
    
    // For window capture, try PrintWindow first (better for Flutter/DirectX apps)
    if (hwnd && IsWindow(hwnd)) {
        CAPSCR_LOG_DEBUG("GDI fallback attempting PrintWindow");
        success = PrintWindow(hwnd, hMemDC, PW_RENDERFULLCONTENT);
        if (success) {
            CAPSCR_LOG_DEBUG("GDI fallback PrintWindow succeeded");
        } else {
            DWORD error = GetLastError();
            CAPSCR_LOG_WARN("GDI fallback PrintWindow failed, trying BitBlt");
        }
    }
    
    // If PrintWindow failed or not applicable, use BitBlt
    if (!success) {
        success = BitBlt(hMemDC, 0, 0, w, h, hScreenDC, r.x, r.y, SRCCOPY | CAPTUREBLT);
        if (success) {
            CAPSCR_LOG_DEBUG("GDI fallback BitBlt succeeded");
        } else {
            CAPSCR_LOG_ERROR("GDI fallback BitBlt failed");
        }
    }
    
    if (!success) {
        DeleteObject(hBitmap); DeleteDC(hMemDC); ReleaseDC(nullptr, hScreenDC); 
        CAPSCR_LOG_ERROR("GDI fallback failed - both PrintWindow and BitBlt unsuccessful");
        return false;
    }
    BITMAPINFO bmi = {};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = w;
    bmi.bmiHeader.biHeight = -h;
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32;
    bmi.bmiHeader.biCompression = BI_RGB;
    CAPSCR_LOG_DEBUG("GDI fallback attempting capture");
    outFrame.width = w; outFrame.height = h; outFrame.format = capscr::PixelFormat::BGRA32; outFrame.stride = w*4;
    outFrame.data.resize(outFrame.stride * h);
    
    int scanlines = GetDIBits(hMemDC, hBitmap, 0, h, outFrame.data.data(), &bmi, DIB_RGB_COLORS);
    if (scanlines <= 0) {
        DWORD error = GetLastError();
        DeleteObject(hBitmap); DeleteDC(hMemDC); ReleaseDC(nullptr, hScreenDC); 
        CAPSCR_LOG_ERROR("GDI fallback GetDIBits failed");
        return false;
    }
    CAPSCR_LOG_DEBUG("GDI fallback GetDIBits succeeded");
    DeleteObject(hBitmap); DeleteDC(hMemDC); ReleaseDC(nullptr, hScreenDC);
    CAPSCR_LOG_DEBUG("GDI fallback capture completed successfully");
    return true;
}


// Helper: try to (re)create duplication using a D3D11 device and optional displayId.
// Returns true and fills outDup/outAdapter/outOutput/outMatchedId on success.
static bool recreate_duplication_for_display(ComPtr<ID3D11Device> device, const std::string& displayId, ComPtr<IDXGIOutputDuplication>& outDup, int& outAdapter, int& outOutput, std::string& outMatchedId) {
    outAdapter = -1; outOutput = -1; outMatchedId.clear(); outDup.Reset();
    if (!device) return false;
    ComPtr<IDXGIDevice> dxgiDevice;
    if (FAILED(device.As(&dxgiDevice))) return false;

    ComPtr<IDXGIAdapter> adapter;
    if (FAILED(dxgiDevice->GetAdapter(&adapter))) return false;

    ComPtr<IDXGIOutput> output;
    ComPtr<IDXGIOutput1> output1;
    for (UINT oi = 0; ; ++oi) {
        output.Reset();
        if (FAILED(adapter->EnumOutputs(oi, &output))) break;
        output1.Reset();
        if (FAILED(output.As(&output1))) continue;

        DXGI_OUTPUT_DESC desc;
        if (FAILED(output->GetDesc(&desc))) continue;
        int needed = WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, nullptr, 0, nullptr, nullptr);
        std::string devName;
        if (needed > 0) {
            devName.resize(needed);
            WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, &devName[0], needed, nullptr, nullptr);
            if (!devName.empty() && devName.back() == '\0') devName.pop_back();
        }

        std::string candidateId = std::string("dxgi:0:") + std::to_string(oi);
        bool match = displayId.empty() || displayId == devName || displayId == candidateId;
        if (!match) continue;
        CAPSCR_LOG_DEBUG("Recreate trying candidate");
        ComPtr<IDXGIOutputDuplication> dup;
        HRESULT dhr = output1->DuplicateOutput(device.Get(), &dup);
        if (FAILED(dhr)) {
            CAPSCR_LOG_WARN("Recreate DuplicateOutput failed");
        }
        if (SUCCEEDED(dhr)) {
            outDup = dup;
            outAdapter = 0;
            outOutput = static_cast<int>(oi);
            outMatchedId = match ? (displayId.empty() ? candidateId : displayId) : candidateId;
            CAPSCR_LOG_INFO("Recreate succeeded");
            return true;
        }
        if (!displayId.empty()) return false;
    }
    return false;
}


struct DXGICapturer::Impl {
    // Basic members - a real implementation would manage D3D device, IDXGIOutputDuplication etc.
    ComPtr<ID3D11Device> device;
    ComPtr<ID3D11DeviceContext> context;
    ComPtr<IDXGIOutputDuplication> duplication;
    Rect targetRect{};
    CaptureTargetType targetType = CaptureTargetType::FullScreen;
    std::mutex mtx;
    std::string displayId;
    int adapterIndex = -1;
    int outputIndex = -1;
    HWND targetHwnd = nullptr; // For window capture
    HWINEVENTHOOK winEventHook = nullptr;
    std::mutex targetMutex; // protects targetRect
    int winEventCount = 0;
    
    // Black frame detection and retry configuration
    BlackFrameConfig blackFrameConfig;
    int consecutiveBlackFrames = 0;
    
    // GPU compute processing
    std::unique_ptr<DXGICompute> compute;
};

// Global mapping from HWND -> DXGICapturer* for WinEvent callbacks
static std::unordered_map<HWND, DXGICapturer*> g_winHookMap;
static std::mutex g_winHookMapMutex;

static void CALLBACK GlobalWinEventProc(HWINEVENTHOOK hWinEventHook, DWORD event, HWND hwnd,
                                        LONG idObject, LONG idChild, DWORD dwEventThread, DWORD dwmsEventTime) {
    if (idObject != OBJID_WINDOW) return;
    std::lock_guard<std::mutex> lk(g_winHookMapMutex);
    auto it = g_winHookMap.find(hwnd);
    if (it == g_winHookMap.end()) return;
    DXGICapturer* inst = it->second;
    if (!inst) return;
    // delegate to instance method which will safely update pimpl
    inst->handleWinEventUpdate(hwnd);
}

// (removed try_recreate_device_and_dup free function because it referenced private nested Impl)

void DXGICapturer::handleWinEventUpdate(HWND hwnd) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    if (!pimpl) return;
    if (pimpl->targetHwnd != hwnd) return;
    RECT winRect = {0,0,0,0};
    if (!GetWindowRect(hwnd, &winRect)) return;
    HMONITOR hm = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONULL);
    RECT monRect = {0,0,0,0};
    if (hm) { MONITORINFO mi; mi.cbSize = sizeof(mi); if (GetMonitorInfoA(hm, &mi)) monRect = mi.rcMonitor; }
    Rect local;
    local.x = winRect.left - monRect.left;
    local.y = winRect.top - monRect.top;
    local.width = winRect.right - winRect.left;
    local.height = winRect.bottom - winRect.top;
    if (local.width < 0) local.width = 0;
    if (local.height < 0) local.height = 0;
    {
        std::lock_guard<std::mutex> tlock(pimpl->targetMutex);
        pimpl->targetRect = local;
    }
    int cnt = ++pimpl->winEventCount;
    CAPSCR_LOG_DEBUG("handleWinEventUpdate updated targetRect");
}

DXGICapturer::DXGICapturer() : pimpl(new Impl()) {
    // Initialize logging system if not already initialized
    capscr::logging::init();
    CAPSCR_LOG_INFO("DXGICapturer initialized");
}

DXGICapturer::~DXGICapturer() { 
    CAPSCR_LOG_DEBUG("DXGICapturer shutting down");
    shutdown(); 
    delete pimpl; 
    pimpl = nullptr; 
}

Result DXGICapturer::init(CaptureTargetType type, const std::string& displayId, const Rect* optTarget, const std::string& optWindowId) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    pimpl->targetType = type;
    pimpl->displayId = displayId;
    
    // Reset targetRect for all capture types - it will be set appropriately below
    pimpl->targetRect = {};
    
    if (type == CaptureTargetType::Region && optTarget) pimpl->targetRect = *optTarget;
    
    // If targeting a window, resolve window and find which monitor/output it's on
    HWND targetHwnd = nullptr;
    RECT winRect = {0,0,0,0};
    HMONITOR targetMonitor = nullptr;
    MONITORINFO monitorInfo = {};
    std::string preferredDisplayId;
    
    if (type == CaptureTargetType::Window && !optWindowId.empty()) {
        // Parse hex (0x...), decimal, or treat as exact title
        const std::string &s = optWindowId;
        if (s.size() > 2 && s[0] == '0' && (s[1] == 'x' || s[1] == 'X')) {
            unsigned long long v = strtoull(s.c_str(), nullptr, 16);
            targetHwnd = (HWND)(uintptr_t)v;
        } else {
            char *end = nullptr;
            unsigned long long v = strtoull(s.c_str(), &end, 0);
            if (end && *end == '\0') {
                targetHwnd = (HWND)(uintptr_t)v;
            } else {
                // Try FindWindowA with exact title
                targetHwnd = FindWindowA(nullptr, s.c_str());
                
                // If exact match failed, try enumeration to find windows containing the title
                if (!targetHwnd) {
                    CAPSCR_LOG_DEBUG("Exact FindWindowA failed, trying enumeration...");
                    
                    struct EnumData {
                        std::string searchTitle;
                        HWND exactMatchHwnd;
                        HWND substringMatchHwnd;
                        EnumData(const std::string& title) : searchTitle(title), exactMatchHwnd(nullptr), substringMatchHwnd(nullptr) {}
                    };
                    
                    EnumData enumData(s);
                    
                    auto enumProc = [](HWND hwnd, LPARAM lParam) -> BOOL {
                        EnumData* data = reinterpret_cast<EnumData*>(lParam);
                        char title[512] = {0};
                        if (GetWindowTextA(hwnd, title, sizeof(title)) > 0) {
                            std::string titleStr(title);
                            // Check if window is visible and not minimized
                            if (IsWindowVisible(hwnd) && !IsIconic(hwnd)) {
                                CAPSCR_LOG_TRACE("Enum checking window");
                                // Try exact match first
                                if (titleStr == data->searchTitle) {
                                    CAPSCR_LOG_DEBUG("Enum found exact match");
                                    data->exactMatchHwnd = hwnd;
                                    return FALSE; // stop enumeration, exact match wins
                                }
                                // Store substring match but continue looking for exact match
                                if (!data->substringMatchHwnd && titleStr.find(data->searchTitle) != std::string::npos) {
                                    CAPSCR_LOG_DEBUG("Enum found substring match");
                                    data->substringMatchHwnd = hwnd;
                                }
                            }
                        }
                        return TRUE; // continue enumeration
                    };
                    
                    EnumWindows(enumProc, reinterpret_cast<LPARAM>(&enumData));
                    
                    // Prefer exact match over substring match
                    if (enumData.exactMatchHwnd) {
                        targetHwnd = enumData.exactMatchHwnd;
                        CAPSCR_LOG_INFO("Using exact match window");
                    } else if (enumData.substringMatchHwnd) {
                        targetHwnd = enumData.substringMatchHwnd;
                        CAPSCR_LOG_INFO("Using substring match window");
                    }
                    
                    if (!targetHwnd) {
                        CAPSCR_LOG_WARN("Enumeration also failed to find window");
                    }
                }
            }
        }
        
        if (targetHwnd && IsWindow(targetHwnd) && GetWindowRect(targetHwnd, &winRect)) {
            // Save the window handle for later use in GDI fallback
            pimpl->targetHwnd = targetHwnd;
            
            // Get window title to verify we got the right window
            char actualTitle[256] = {0};
            GetWindowTextA(targetHwnd, actualTitle, sizeof(actualTitle));
            fprintf(stderr, "DXGI:init found window HWND=0x%p title='%s' rect=(%ld,%ld,%ld,%ld) size=%ldx%ld\n", 
                targetHwnd, actualTitle,
                winRect.left, winRect.top, winRect.right, winRect.bottom,
                winRect.right - winRect.left, winRect.bottom - winRect.top);
            
            // Find which monitor this window is on
            targetMonitor = MonitorFromWindow(targetHwnd, MONITOR_DEFAULTTONULL);
            if (targetMonitor) {
                monitorInfo.cbSize = sizeof(MONITORINFO);
                if (GetMonitorInfoA(targetMonitor, &monitorInfo)) {
                    fprintf(stderr, "DXGI:init window '%s' found on monitor rect %ld,%ld %ldx%ld\n", 
                        s.c_str(), 
                        monitorInfo.rcMonitor.left, monitorInfo.rcMonitor.top,
                        monitorInfo.rcMonitor.right - monitorInfo.rcMonitor.left,
                        monitorInfo.rcMonitor.bottom - monitorInfo.rcMonitor.top);
                } else {
                    targetMonitor = nullptr;
                }
            }
            if (!targetMonitor) {
                fprintf(stderr, "DXGI:init window '%s' not found on any monitor\n", s.c_str());
                targetHwnd = nullptr;
            }
        } else {
            fprintf(stderr, "DXGI:init failed to resolve window '%s' (FindWindowA returned 0x%p)\n", s.c_str(), targetHwnd);
            targetHwnd = nullptr;
        }
    }

    // Minimal feature detection: try to create D3D11 device and query DXGI output duplication support.
    D3D_FEATURE_LEVEL levels[] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0 };
    UINT creationFlags = D3D11_CREATE_DEVICE_BGRA_SUPPORT;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, creationFlags, levels, ARRAYSIZE(levels), D3D11_SDK_VERSION, &pimpl->device, nullptr, &pimpl->context);
    if (FAILED(hr)) CAPSCR_LOG_ERROR("D3D11CreateDevice failed");
    if (FAILED(hr) || !pimpl->device) {
        return capscr::Result::Unsupported;
    }

    // Query for IDXGIDevice -> adapter -> output -> duplication
    ComPtr<IDXGIDevice> dxgiDevice;
    if (FAILED(pimpl->device.As(&dxgiDevice))) return Result::Unsupported;

    ComPtr<IDXGIAdapter> adapter;
    if (FAILED(dxgiDevice->GetAdapter(&adapter))) return Result::Unsupported;

    // Try to find an output that matches the requested displayId or the window's monitor
    ComPtr<IDXGIOutput> output;
    ComPtr<IDXGIOutput1> output1;
    ComPtr<IDXGIOutputDuplication> duplication;
    bool found = false;
    for (UINT oi = 0; ; ++oi) {
        output.Reset();
        if (FAILED(adapter->EnumOutputs(oi, &output))) break; // no more outputs
        output1.Reset();
        if (FAILED(output.As(&output1))) continue;

        // Get output description to obtain the device name (e.g. \\.\DISPLAY1)
        DXGI_OUTPUT_DESC desc;
        if (FAILED(output->GetDesc(&desc))) continue;
        // convert wide device name to utf8
        int needed = WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, nullptr, 0, nullptr, nullptr);
        std::string devName;
        if (needed > 0) {
            devName.resize(needed);
            WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, &devName[0], needed, nullptr, nullptr);
            // remove trailing NUL from std::string
            if (!devName.empty() && devName.back() == '\0') devName.pop_back();
        }

        std::string candidateId = std::string("dxgi:0:") + std::to_string(oi);

        bool match = false;
        // Check displayId match
        if (!displayId.empty()) {
            if (displayId == devName || displayId == candidateId) match = true;
        }
        // Check if this output matches the window's monitor
        else if (targetMonitor && type == CaptureTargetType::Window) {
            RECT outputRect = desc.DesktopCoordinates;
            RECT monRect = monitorInfo.rcMonitor;
            if (outputRect.left == monRect.left && outputRect.top == monRect.top &&
                outputRect.right == monRect.right && outputRect.bottom == monRect.bottom) {
                match = true;
                preferredDisplayId = candidateId;
                fprintf(stderr, "DXGI:init found matching output %s for window monitor\n", candidateId.c_str());
            }
        }
        // If no specific match criteria, try first available
        else if (displayId.empty() && type != CaptureTargetType::Window) {
            match = true;
        }

        if (match) fprintf(stderr, "DXGI:init matching displayId '%s' -> devName='%s' candidate='%s'\n", displayId.c_str(), devName.c_str(), candidateId.c_str());

        if (match) {
            ComPtr<IDXGIOutputDuplication> dup;
            HRESULT dhr = output1->DuplicateOutput(pimpl->device.Get(), &dup);
            if (FAILED(dhr)) fprintf(stderr, "DXGI:init DuplicateOutput failed on candidate %s hr=0x%08x\n", candidateId.c_str(), dhr);
            if (SUCCEEDED(dhr)) {
                duplication = dup;
                found = true;
                // store adapter/output indices for diagnostics
                pimpl->adapterIndex = 0;
                pimpl->outputIndex = static_cast<int>(oi);
                pimpl->displayId = !preferredDisplayId.empty() ? preferredDisplayId : 
                                   (!displayId.empty() ? displayId : candidateId);
                
                // If we have a window target, compute targetRect relative to this output
                if (type == CaptureTargetType::Window && targetHwnd) {
                    fprintf(stderr, "DXGI:init raw winRect L=%ld T=%ld R=%ld B=%ld; output desc L=%ld T=%ld R=%ld B=%ld\n",
                        winRect.left, winRect.top, winRect.right, winRect.bottom,
                        desc.DesktopCoordinates.left, desc.DesktopCoordinates.top, desc.DesktopCoordinates.right, desc.DesktopCoordinates.bottom);
                        
                    int ox = desc.DesktopCoordinates.left;
                    int oy = desc.DesktopCoordinates.top;
                    Rect local;
                    local.x = winRect.left - ox;
                    local.y = winRect.top - oy;
                    local.width = winRect.right - winRect.left;
                    local.height = winRect.bottom - winRect.top;
                    
                    // Clamp to output bounds (should not be necessary if monitor matched correctly)
                    int outputW = desc.DesktopCoordinates.right - desc.DesktopCoordinates.left;
                    int outputH = desc.DesktopCoordinates.bottom - desc.DesktopCoordinates.top;
                    if (local.x < 0) { local.width += local.x; local.x = 0; }
                    if (local.y < 0) { local.height += local.y; local.y = 0; }
                    if (local.x + local.width > outputW) local.width = outputW - local.x;
                    if (local.y + local.height > outputH) local.height = outputH - local.y;
                    
                    if (local.width <= 0 || local.height <= 0) {
                        fprintf(stderr, "DXGI:init window rect invalid after clamping, w=%d h=%d\n", local.width, local.height);
                        continue; // try next output
                    }
                    
                            pimpl->targetRect = local;
                            fprintf(stderr, "DXGI:init window target rect set to %d,%d %dx%d on output %s\n", local.x, local.y, local.width, local.height, candidateId.c_str());
                            // register WinEvent hook to track window moves/resizes
                            {
                                std::lock_guard<std::mutex> lk(g_winHookMapMutex);
                                g_winHookMap[targetHwnd] = this;
                            }
                            pimpl->winEventHook = SetWinEventHook(
                                EVENT_OBJECT_LOCATIONCHANGE, EVENT_OBJECT_LOCATIONCHANGE,
                                nullptr, GlobalWinEventProc,
                                0, 0, WINEVENT_OUTOFCONTEXT | WINEVENT_SKIPOWNPROCESS);
                            if (!pimpl->winEventHook) {
                                fprintf(stderr, "DXGI: SetWinEventHook failed for hwnd=0x%p\n", targetHwnd);
                            } else {
                                fprintf(stderr, "DXGI: WinEventHook registered for hwnd=0x%p\n", targetHwnd);
                            }
                }
                pimpl->duplication = duplication;
                break;
            }
            // if user asked for a specific display and duplication failed, report error
            if (!displayId.empty()) return Result::Error;
        }
    }

    if (!pimpl->duplication) {
        fprintf(stderr, "DXGI:init no duplication available for device (displayId='%s')\n", pimpl->displayId.c_str());
        // If we were targeting a window and couldn't find the window or matching output, return WindowNotFound
        if (type == CaptureTargetType::Window) {
            if (targetHwnd == nullptr) return Result::WindowNotFound;
            // If we found the window but DXGI failed, set up targetRect for GDI fallback
            // Convert window coordinates to capture-friendly format
            fprintf(stderr, "DXGI:init DXGI failed but window found, setting up targetRect for GDI fallback\n");
            pimpl->targetRect.x = winRect.left;
            pimpl->targetRect.y = winRect.top;
            pimpl->targetRect.width = winRect.right - winRect.left;
            pimpl->targetRect.height = winRect.bottom - winRect.top;
            fprintf(stderr, "DXGI:init set targetRect for GDI fallback: %d,%d %dx%d\n", 
                pimpl->targetRect.x, pimpl->targetRect.y, pimpl->targetRect.width, pimpl->targetRect.height);
            return Result::Ok; // Allow fallback to work
        }
        return Result::Unsupported;
    }
    
    // Initialize GPU compute processing if device is available
    if (pimpl->device && pimpl->context) {
        pimpl->compute = std::make_unique<DXGICompute>(pimpl->device, pimpl->context);
        Result computeResult = pimpl->compute->initialize();
        if (computeResult == Result::Ok) {
            CAPSCR_LOG_INFO("GPU compute processing enabled");
        } else {
            CAPSCR_LOG_WARN("GPU compute processing unavailable, falling back to CPU");
            pimpl->compute.reset(); // Clear compute if initialization failed
        }
    }
    
    return Result::Ok;
}

std::vector<DisplayInfo> DXGICapturer::listDisplays() {
    std::vector<DisplayInfo> out;
    // Enumerate DXGI adapters and outputs and return ids in the form "dxgi:<adapter>:<output>"
    ComPtr<IDXGIFactory1> factory;
    if (FAILED(CreateDXGIFactory1(__uuidof(IDXGIFactory1), reinterpret_cast<void**>(factory.GetAddressOf())))) {
        fprintf(stderr, "DXGI:listDisplays CreateDXGIFactory1 failed\n");
        return out;
    }

    ComPtr<IDXGIAdapter1> adapter;
    for (UINT ai = 0; ; ++ai) {
        adapter.Reset();
        if (FAILED(factory->EnumAdapters1(ai, &adapter))) break;
        ComPtr<IDXGIOutput> output;
        for (UINT oi = 0; ; ++oi) {
            output.Reset();
            if (FAILED(adapter->EnumOutputs(oi, &output))) break;
            DXGI_OUTPUT_DESC desc;
            if (FAILED(output->GetDesc(&desc))) continue;
            // convert device name
            int needed = WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, nullptr, 0, nullptr, nullptr);
            std::string devName;
            if (needed > 0) {
                devName.resize(needed);
                WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, &devName[0], needed, nullptr, nullptr);
                if (!devName.empty() && devName.back() == '\0') devName.pop_back();
            }
            DisplayInfo d;
            d.id = std::string("dxgi:") + std::to_string(ai) + ":" + std::to_string(oi);
            d.name = devName.empty() ? d.id : devName;
            d.bounds.x = desc.DesktopCoordinates.left;
            d.bounds.y = desc.DesktopCoordinates.top;
            d.bounds.width = desc.DesktopCoordinates.right - desc.DesktopCoordinates.left;
            d.bounds.height = desc.DesktopCoordinates.bottom - desc.DesktopCoordinates.top;
            d.scale = 1.0f;
            fprintf(stderr, "DXGI:listDisplays found adapter=%u output=%u name=%s bounds=%d,%d %dx%d\n", ai, oi, devName.c_str(), d.bounds.x, d.bounds.y, d.bounds.width, d.bounds.height);
            out.push_back(d);
        }
    }
    return out;
}

BackendCapability DXGICapturer::capabilities() const {
    BackendCapability caps = BackendCapability::SupportFullScreen | BackendCapability::SupportRegion | BackendCapability::SupportWindow | BackendCapability::HighPerformance;
    
    // Add GPU capabilities if available
    if (pimpl && pimpl->device && pimpl->duplication) {
        caps |= BackendCapability::ZeroCopyGpu;
    }
    
    return caps;
}

Result DXGICapturer::setTarget(CaptureTargetType type, const std::string& displayId, const Rect* optTarget, const std::string& optWindowId) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    pimpl->targetType = type;
    pimpl->displayId = displayId;
    
    // Reset targetRect for all capture types - it will be set appropriately below  
    pimpl->targetRect = {};
    
    if (type == CaptureTargetType::Region && optTarget) pimpl->targetRect = *optTarget;
    return Result::Ok;
}

Rect DXGICapturer::getTarget() const {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    return pimpl->targetRect;
}

Result DXGICapturer::capture(Frame& outFrame) {
    // Retry logic for black frame handling
    if (pimpl->blackFrameConfig.enable_auto_retry) {
        for (int attempt = 0; attempt <= pimpl->blackFrameConfig.max_retries; ++attempt) {
            Result result = captureInternal(outFrame);
            
            // If capture succeeded, check for black frame
            if (result == Result::Ok && pimpl->blackFrameConfig.enable_detection) {
                if (is_frame_black(outFrame, pimpl->blackFrameConfig.black_threshold)) {
                    pimpl->consecutiveBlackFrames++;
                    
                    if (pimpl->blackFrameConfig.log_black_frames) {
                        fprintf(stderr, "Black frame detected (attempt %d/%d), consecutive=%d\n", 
                                attempt + 1, pimpl->blackFrameConfig.max_retries + 1, 
                                pimpl->consecutiveBlackFrames);
                    }
                    
                    // If we've hit max retries, return black frame result
                    if (attempt >= pimpl->blackFrameConfig.max_retries) {
                        return Result::BlackFrameDetected;
                    }
                    
                    // Wait before retry
                    if (pimpl->blackFrameConfig.retry_delay_ms > 0) {
                        Sleep(pimpl->blackFrameConfig.retry_delay_ms);
                    }
                    continue; // Retry
                } else {
                    // Frame is good, reset consecutive counter
                    pimpl->consecutiveBlackFrames = 0;
                    return Result::Ok;
                }
            }
            
            // Handle other results
            if (result != Result::Ok) {
                return result; // Don't retry on hard errors
            }
            
            return result;
        }
        return Result::RetryExhausted;
    } else {
        // No retry, just do single capture
        return captureInternal(outFrame);
    }
}

Result DXGICapturer::capture(Frame& outFrame, const CaptureParams& params) {
    // Validate parameters
    if (params.output_width < 0 || params.output_height < 0) {
        CAPSCR_LOG_ERROR("Invalid negative dimensions: output_width={}, output_height={}", 
                         params.output_width, params.output_height);
        return Result::Error;
    }
    
    // Check for reasonable dimension limits (prevent extremely large allocations)
    constexpr int MAX_DIMENSION = 16384; // 16K resolution limit
    if (params.output_width > MAX_DIMENSION || params.output_height > MAX_DIMENSION) {
        CAPSCR_LOG_ERROR("Dimensions exceed maximum: output_width={}, output_height={} (max={})", 
                         params.output_width, params.output_height, MAX_DIMENSION);
        return Result::Error;
    }
    
    // If only one dimension is specified, it's invalid
    if ((params.output_width > 0 && params.output_height == 0) ||
        (params.output_width == 0 && params.output_height > 0)) {
        CAPSCR_LOG_ERROR("Invalid partial dimensions: output_width={}, output_height={}", 
                         params.output_width, params.output_height);
        return Result::Error;
    }
    
    CAPSCR_LOG_DEBUG("Processing capture with GPU compute pipeline");
    
    // Check if we need any processing (resize or format conversion)
    bool needsProcessing = (params.output_width > 0 && params.output_height > 0) ||
                          (params.output_format != PixelFormat::BGRA32);
    
    if (!needsProcessing) {
        // No processing needed, use standard capture
        return capture(outFrame);
    }
    
    // Check if GPU processing is available and requested
    if (params.use_gpu_processing && pimpl->compute && pimpl->compute->isAvailable()) {
        CAPSCR_LOG_INFO("DXGICapturer::capture: choosing GPU processing path (use_gpu=%d)", params.use_gpu_processing);
        fprintf(stderr, "DXGICapturer::capture: choosing GPU processing path use_gpu=%d\n", params.use_gpu_processing);
        return captureWithGpuProcessing(outFrame, params);
    } else {
        CAPSCR_LOG_INFO("DXGICapturer::capture: choosing CPU processing path (use_gpu=%d)", params.use_gpu_processing);
        fprintf(stderr, "DXGICapturer::capture: choosing CPU processing path use_gpu=%d\n", params.use_gpu_processing);
        return captureWithCpuProcessing(outFrame, params);
    }
}

Result DXGICapturer::captureWithGpuProcessing(Frame& outFrame, const CaptureParams& params) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    
    CAPSCR_LOG_INFO("Starting GPU processing with texture compatibility handling");
    
    // First capture to GPU texture
    GpuTexture gpuTexture;
    Result result = captureGpuUnlocked(gpuTexture);
    CAPSCR_LOG_INFO("GPU capture completed, checking result");
    if (result != Result::Ok) {
        CAPSCR_LOG_WARN("GPU capture failed, falling back to CPU processing");
        return captureWithCpuProcessing(outFrame, params);
    }
    
    // Get the acquired texture from DXGI
    ComPtr<ID3D11Texture2D> acquiredTexture = static_cast<ID3D11Texture2D*>(gpuTexture.d3d11_texture);
    
    // Get texture description
    D3D11_TEXTURE2D_DESC acquiredDesc;
    acquiredTexture->GetDesc(&acquiredDesc);
    
    CAPSCR_LOG_DEBUG("Checking acquired texture compatibility for compute shader");
    
    // Check if we need to copy the texture for compute shader compatibility
    const UINT requiredBindFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
    ComPtr<ID3D11Texture2D> inputTexture;
    
    if ((acquiredDesc.BindFlags & requiredBindFlags) != requiredBindFlags) {
        CAPSCR_LOG_DEBUG("DXGI texture doesn't support compute shader, creating compatible copy");
        
        // Create a compute-shader-compatible texture
        D3D11_TEXTURE2D_DESC compatibleDesc = acquiredDesc;
        compatibleDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
        compatibleDesc.Usage = D3D11_USAGE_DEFAULT;
        compatibleDesc.CPUAccessFlags = 0;
        compatibleDesc.MiscFlags = 0;
        
        HRESULT hr = pimpl->device->CreateTexture2D(&compatibleDesc, nullptr, &inputTexture);
        if (FAILED(hr)) {
            CAPSCR_LOG_ERROR("Failed to create compute-compatible texture: HRESULT=0x{:08X}", hr);
            pimpl->duplication->ReleaseFrame();
            return captureWithCpuProcessing(outFrame, params);
        }
        
        // Copy the DXGI texture to our compute-compatible texture
        pimpl->context->CopyResource(inputTexture.Get(), acquiredTexture.Get());
    } else {
        // Use the acquired texture directly
        inputTexture = acquiredTexture;
    }
    
    // Process the GPU texture
    ComPtr<ID3D11Texture2D> outputTexture;
    result = pimpl->compute->processTexture(inputTexture, params, outputTexture);
    if (result != Result::Ok) {
        CAPSCR_LOG_WARN("GPU processing failed, falling back to CPU processing");
        // Release the frame before falling back
        pimpl->duplication->ReleaseFrame();
        return captureWithCpuProcessing(outFrame, params);
    }
    
    // Copy processed texture back to CPU Frame
    result = copyProcessedTextureToFrame(outputTexture, params, outFrame);
    
    // Release the original frame
    pimpl->duplication->ReleaseFrame();
    
    return result;
}

Result DXGICapturer::captureWithCpuProcessing(Frame& outFrame, const CaptureParams& params) {
    // Capture to intermediate frame
    Frame sourceFrame;
    Result result = capture(sourceFrame);
    if (result != Result::Ok) {
        return result;
    }
    
    // Apply CPU processing
    return processFrame(sourceFrame, outFrame, params);
}

Result DXGICapturer::copyProcessedTextureToFrame(ComPtr<ID3D11Texture2D> texture, 
                                                const CaptureParams& params, 
                                                Frame& outFrame) {
    if (!texture) {
        return Result::Error;
    }
    
    // Get texture description
    D3D11_TEXTURE2D_DESC textureDesc;
    texture->GetDesc(&textureDesc);
    
    // Create staging texture
    D3D11_TEXTURE2D_DESC stagingDesc = textureDesc;
    stagingDesc.Usage = D3D11_USAGE_STAGING;
    stagingDesc.BindFlags = 0;
    stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stagingDesc.MiscFlags = 0;
    
    ComPtr<ID3D11Texture2D> stagingTexture;
    HRESULT hr = pimpl->device->CreateTexture2D(&stagingDesc, nullptr, &stagingTexture);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to create staging texture for processed result");
        return Result::Error;
    }
    
    // Copy processed texture to staging
    pimpl->context->CopyResource(stagingTexture.Get(), texture.Get());
    pimpl->context->Flush();
    
    // Map and copy to Frame
    D3D11_MAPPED_SUBRESOURCE mapped;
    hr = pimpl->context->Map(stagingTexture.Get(), 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to map processed staging texture");
        return Result::Error;
    }
    
    // Set up output frame
    int width = (params.output_width > 0) ? params.output_width : textureDesc.Width;
    int height = (params.output_height > 0) ? params.output_height : textureDesc.Height;
    PixelFormat format = params.output_format;

    outFrame.width = width;
    outFrame.height = height;
    outFrame.format = format;
    // Use correct bytes-per-pixel for requested format
    int bpp = capscr::ImageUtils::getBytesPerPixel(format);
    outFrame.stride = width * bpp;
    outFrame.data.resize(static_cast<size_t>(outFrame.stride) * static_cast<size_t>(height));

    // Copy pixel data safely: don't read past mapped.RowPitch and pad if destination wider
    uint8_t* srcBase = static_cast<uint8_t*>(mapped.pData);
    for (int y = 0; y < height; ++y) {
        uint8_t* srcRow = srcBase + static_cast<size_t>(y) * mapped.RowPitch;
        size_t expected = static_cast<size_t>(outFrame.stride);
        size_t available = static_cast<size_t>(mapped.RowPitch);
        size_t copyBytes = (available < expected) ? available : expected;
        memcpy(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride, srcRow, copyBytes);
        if (expected > copyBytes) memset(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride + copyBytes, 0, expected - copyBytes);
    }
    
    pimpl->context->Unmap(stagingTexture.Get(), 0);
    
    return Result::Ok;
}

Result DXGICapturer::captureInternal(Frame& outFrame) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    if (!pimpl->duplication) return Result::Error;

    // Check if window is still valid and visible (for window capture)
    if (pimpl->targetType == CaptureTargetType::Window && pimpl->targetHwnd) {
        if (!IsWindow(pimpl->targetHwnd)) {
            return Result::WindowNotFound;
        }
        if (!IsWindowVisible(pimpl->targetHwnd)) {
            return Result::WindowClosed;
        }
        if (IsIconic(pimpl->targetHwnd)) {
            // Window is minimized/iconsized - signal WindowMinimized to caller
            return Result::WindowMinimized;
        }
    }

    // Try to acquire next frame (short timeout)
    DXGI_OUTDUPL_FRAME_INFO frameInfo;
    ComPtr<IDXGIResource> desktopResource;
    HRESULT hr = pimpl->duplication->AcquireNextFrame(500, &frameInfo, &desktopResource);
    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        // transient, treat as non-fatal error to caller
        return capscr::Result::Error;
    }

    int attempts = 0;
    const int maxRecreateAttempts = 3;
    while ((hr == DXGI_ERROR_ACCESS_LOST || FAILED(hr)) && attempts < maxRecreateAttempts) {
        // Try to recreate duplication and retry acquire
        attempts++;
        pimpl->duplication.Reset();
    fprintf(stderr, "DXGI: AcquireNextFrame failed (hr=0x%08x), attempt %d to recreate duplication\n", hr, attempts);
        ComPtr<IDXGIOutputDuplication> newDup;
        int newAdapter = -1, newOutput = -1;
        std::string newMatchedId;
        if (!recreate_duplication_for_display(pimpl->device, pimpl->displayId, newDup, newAdapter, newOutput, newMatchedId)) {
            // Try to recreate the D3D11 device/context and then attempt duplication again
            fprintf(stderr, "DXGI: duplication recreate failed, attempting full device/context recreate\n");
            pimpl->duplication.Reset();
            pimpl->context.Reset();
            pimpl->device.Reset();
            D3D_FEATURE_LEVEL levels[] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0 };
            UINT creationFlags = D3D11_CREATE_DEVICE_BGRA_SUPPORT;
            ComPtr<ID3D11Device> newDevice;
            ComPtr<ID3D11DeviceContext> newContext;
            HRESULT chr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, creationFlags, levels, ARRAYSIZE(levels), D3D11_SDK_VERSION, &newDevice, nullptr, &newContext);
            if (SUCCEEDED(chr) && newDevice) {
                fprintf(stderr, "DXGI: recreated D3D11 device successfully (hr=0x%08x)\n", chr);
                // try recreate duplication on new device
                ComPtr<IDXGIOutputDuplication> tryDup;
                int tryAdapter=-1, tryOutput=-1; std::string tryId;
                if (recreate_duplication_for_display(newDevice, pimpl->displayId, tryDup, tryAdapter, tryOutput, tryId)) {
                    // success: install new device/context/duplication
                    pimpl->device = newDevice;
                    pimpl->context = newContext;
                    pimpl->duplication = tryDup;
                    pimpl->adapterIndex = tryAdapter;
                    pimpl->outputIndex = tryOutput;
                    if (!tryId.empty()) pimpl->displayId = tryId;
                    newDup = tryDup; newAdapter = tryAdapter; newOutput = tryOutput; newMatchedId = tryId;
                    } else {
                    // failed to recreate duplication on new device; continue retry loop
                    fprintf(stderr, "DXGI: device recreate succeeded but duplication still failed (hr=0x%08x)\n", chr);
                    Sleep(200);
                    continue;
                }
            } else {
                fprintf(stderr, "DXGI: D3D11CreateDevice failed while attempting recreate (hr=0x%08x)\n", chr);
                Sleep(200);
                continue;
            }
        }
        pimpl->duplication = newDup;
        pimpl->adapterIndex = newAdapter;
        pimpl->outputIndex = newOutput;
        if (!newMatchedId.empty()) pimpl->displayId = newMatchedId;
        hr = pimpl->duplication->AcquireNextFrame(500, &frameInfo, &desktopResource);
        if (hr == S_OK) break;
        Sleep(100);
    }

    if (FAILED(hr) || !desktopResource) {
        fprintf(stderr, "DXGI: AcquireNextFrame final failure hr=0x%08x - attempting GDI fallback\n", hr);
        // Try a GDI fallback for the requested target area
        fprintf(stderr, "DXGI: GDI fallback targetType=%d\n", (int)pimpl->targetType);
        Rect r = {};  // Initialize to zero
        if (pimpl->targetType == CaptureTargetType::FullScreen) {
            r.x = 0; r.y = 0;
            // Try to use listDisplays to find bounds for displayId - fallback to system metrics
            r.width = GetSystemMetrics(SM_CXSCREEN);
            r.height = GetSystemMetrics(SM_CYSCREEN);
        } else if (pimpl->targetType == CaptureTargetType::Window) {
            // For window capture, use window rect but capture from (0,0)
            fprintf(stderr, "DXGI: Window fallback - targetRect = (%d,%d,%d,%d)\n", 
                pimpl->targetRect.x, pimpl->targetRect.y, pimpl->targetRect.width, pimpl->targetRect.height);
            r.x = 0; r.y = 0; 
            r.width = pimpl->targetRect.width;
            r.height = pimpl->targetRect.height;
        } else {
            r = pimpl->targetRect;
        }
        if (gdi_capture_rect(r, outFrame, pimpl->targetHwnd)) return Result::Ok;
        return Result::Error;
    }

    ComPtr<ID3D11Texture2D> acquiredTex;
    if (FAILED(desktopResource.As(&acquiredTex))) {
        pimpl->duplication->ReleaseFrame();
        return Result::Error;
    }

    // --- GPU-side crop then small staging readback (reduce GPU->CPU copy) ---
    D3D11_TEXTURE2D_DESC fullDesc;
    acquiredTex->GetDesc(&fullDesc);

    int texW = fullDesc.Width;
    int texH = fullDesc.Height;
    // Log texture description for diagnostics
    // fprintf(stderr, "DXGI: acquiredTex desc W=%u H=%u Format=%u Sample=%u,%u MipLevels=%u ArraySize=%u BindFlags=0x%08x MiscFlags=0x%08x\n",
    //     fullDesc.Width, fullDesc.Height, (unsigned)fullDesc.Format, fullDesc.SampleDesc.Count, fullDesc.SampleDesc.Quality, fullDesc.MipLevels, fullDesc.ArraySize, fullDesc.BindFlags, fullDesc.MiscFlags);
    Rect desired;
    if (pimpl->targetType == CaptureTargetType::Window && pimpl->targetHwnd) {
        // Recompute window rect at capture time to avoid stale values
        RECT winRect = {0,0,0,0};
        if (GetWindowRect(pimpl->targetHwnd, &winRect)) {
            // Determine monitor origin for this window
            HMONITOR hm = MonitorFromWindow(pimpl->targetHwnd, MONITOR_DEFAULTTONULL);
            RECT monRect = {0,0,0,0};
            if (hm) {
                MONITORINFO mi; mi.cbSize = sizeof(mi);
                if (GetMonitorInfoA(hm, &mi)) monRect = mi.rcMonitor;
            }
            Rect local;
            local.x = winRect.left - monRect.left;
            local.y = winRect.top - monRect.top;
            local.width = winRect.right - winRect.left;
            local.height = winRect.bottom - winRect.top;
            fprintf(stderr, "DXGI: runtime window rect L=%ld T=%ld R=%ld B=%ld; monitor L=%ld T=%ld\n",
                winRect.left, winRect.top, winRect.right, winRect.bottom, monRect.left, monRect.top);
            fprintf(stderr, "DXGI: runtime local rect before clamp=(%d,%d %dx%d) tex=(%d,%d)\n", local.x, local.y, local.width, local.height, texW, texH);
            // Clamp to texture bounds
            if (local.x < 0) local.x = 0;
            if (local.y < 0) local.y = 0;
            if (local.x > texW) local.x = texW;
            if (local.y > texH) local.y = texH;
            if (local.width < 0) local.width = 0;
            if (local.height < 0) local.height = 0;
            if (local.x + local.width > texW) local.width = texW - local.x;
            if (local.y + local.height > texH) local.height = texH - local.y;
            fprintf(stderr, "DXGI: runtime local rect after clamp=(%d,%d %dx%d)\n", local.x, local.y, local.width, local.height);
            desired = local;
        } else {
            // Failed to query window rect - fall back to stored targetRect
            Rect orig = pimpl->targetRect;
            fprintf(stderr, "DXGI: GetWindowRect failed, falling back to stored targetRect=(%d,%d %dx%d)\n", orig.x, orig.y, orig.width, orig.height);
            desired = orig;
        }
    } else if (pimpl->targetType == CaptureTargetType::Region) {
        // Copy original requested rect for logging
        Rect orig = pimpl->targetRect;
        fprintf(stderr, "DXGI: desired pre-clamp orig=(%d,%d %dx%d) tex=(%d,%d)\n", orig.x, orig.y, orig.width, orig.height, texW, texH);
        desired = orig;
        // clamp to texture bounds
        if (desired.x < 0) desired.x = 0;
        if (desired.y < 0) desired.y = 0;
        if (desired.x > texW) desired.x = texW;
        if (desired.y > texH) desired.y = texH;
        if (desired.width < 0) desired.width = 0;
        if (desired.height < 0) desired.height = 0;
        if (desired.x + desired.width > texW) desired.width = texW - desired.x;
        if (desired.y + desired.height > texH) desired.height = texH - desired.y;
        fprintf(stderr, "DXGI: desired post-clamp=(%d,%d %dx%d)\n", desired.x, desired.y, desired.width, desired.height);
    } else {
        desired.x = 0; desired.y = 0; desired.width = texW; desired.height = texH;
    }

    // If desired region is empty (window minimized or invalid), bail out early
    if (desired.width <= 0 || desired.height <= 0) {
        fprintf(stderr, "DXGI: desired region empty (w=%d h=%d) - window may be minimized or offscreen\n", desired.width, desired.height);
        pimpl->duplication->ReleaseFrame();
        return Result::WindowMinimized;
    }

    // If desired equals full texture, do the full staging path
    if (desired.x == 0 && desired.y == 0 && desired.width == texW && desired.height == texH) {
        D3D11_TEXTURE2D_DESC stagingDesc = fullDesc;
        stagingDesc.Usage = D3D11_USAGE_STAGING;
        stagingDesc.BindFlags = 0;
        stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        stagingDesc.MiscFlags = 0;

        ComPtr<ID3D11Texture2D> staging;
        HRESULT chr = pimpl->device->CreateTexture2D(&stagingDesc, nullptr, &staging);
        if (FAILED(chr) || !staging) {
            std::string s = hr_to_string(chr);
            fprintf(stderr, "DXGI: CreateTexture2D failed hr=0x%08x (%s) (w=%u h=%u)\n", chr, s.c_str(), stagingDesc.Width, stagingDesc.Height);
            pimpl->duplication->ReleaseFrame();
            if (chr == DXGI_ERROR_DEVICE_REMOVED || chr == DXGI_ERROR_DEVICE_RESET) {
                fprintf(stderr, "DXGI: Device removed/reset detected, using GDI fallback\n");
                Rect r = {};
                if (pimpl->targetType == CaptureTargetType::FullScreen) { r.x = 0; r.y = 0; r.width = stagingDesc.Width; r.height = stagingDesc.Height; }
                else if (pimpl->targetType == CaptureTargetType::Window) { r.x = 0; r.y = 0; r.width = pimpl->targetRect.width; r.height = pimpl->targetRect.height; }
                else r = pimpl->targetRect;
                if (gdi_capture_rect(r, outFrame, pimpl->targetHwnd)) return Result::Ok;
            }
            return Result::Error;
        }

        pimpl->context->CopyResource(staging.Get(), acquiredTex.Get());
        pimpl->context->Flush();

        D3D11_MAPPED_SUBRESOURCE mapped;
        HRESULT mhr = pimpl->context->Map(staging.Get(), 0, D3D11_MAP_READ, 0, &mapped);
        if (FAILED(mhr)) {
            std::string s = hr_to_string(mhr);
            fprintf(stderr, "DXGI: Map staging texture failed hr=0x%08x (%s)\n", mhr, s.c_str());
            pimpl->duplication->ReleaseFrame();
            Rect r = {};
            if (pimpl->targetType == CaptureTargetType::FullScreen) { r.x = 0; r.y = 0; r.width = stagingDesc.Width; r.height = stagingDesc.Height; }
            else if (pimpl->targetType == CaptureTargetType::Window) { r.x = 0; r.y = 0; r.width = pimpl->targetRect.width; r.height = pimpl->targetRect.height; }
            else r = pimpl->targetRect;
            if (gdi_capture_rect(r, outFrame, pimpl->targetHwnd)) return Result::Ok;
            return Result::Error;
        }

        int w = stagingDesc.Width;
        int h = stagingDesc.Height;
        outFrame.width = w; outFrame.height = h; outFrame.format = capscr::PixelFormat::BGRA32;
        outFrame.stride = w*4;
        outFrame.data.resize(static_cast<size_t>(outFrame.stride) * static_cast<size_t>(h));
        uint8_t* srcBase = static_cast<uint8_t*>(mapped.pData);
        for (int y=0; y<h; ++y) {
            uint8_t* srcRow = srcBase + static_cast<size_t>(y) * mapped.RowPitch;
            size_t expected = static_cast<size_t>(outFrame.stride);
            size_t available = static_cast<size_t>(mapped.RowPitch);
            size_t copyBytes = (available < expected) ? available : expected;
            memcpy(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride, srcRow, copyBytes);
            if (expected > copyBytes) memset(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride + copyBytes, 0, expected - copyBytes);
        }
        pimpl->context->Unmap(staging.Get(), 0);
        pimpl->context->Flush();
        pimpl->duplication->ReleaseFrame();
        return Result::Ok;
    }

    // Region case: create small GPU texture, copy subregion into it, then read back only small texture
    D3D11_TEXTURE2D_DESC smallDesc = fullDesc;
    smallDesc.Width = desired.width;
    smallDesc.Height = desired.height;
    smallDesc.Usage = D3D11_USAGE_DEFAULT;
    smallDesc.BindFlags = 0;
    smallDesc.CPUAccessFlags = 0;
    smallDesc.MiscFlags = 0;

    // Defensive check before creating small texture
    fprintf(stderr, "DXGI: creating small GPU texture w=%u h=%u (texW=%d texH=%d)\n", smallDesc.Width, smallDesc.Height, texW, texH);
    ComPtr<ID3D11Texture2D> smallTex;
    HRESULT chr = pimpl->device->CreateTexture2D(&smallDesc, nullptr, &smallTex);
    std::cout << "DXGI: Create small GPU texture result hr=0x" << std::hex << chr << std::endl;
    std::cout << "DXGI: smallTex pointer: " << (void*)smallTex.Get() << std::endl;
    std::cout << "DXGI: FAILED(chr)=" << FAILED(chr) << " !smallTex=" << (!smallTex) << std::endl;
    if (FAILED(chr) || !smallTex) {
        std::cout << "DXGI: Taking GPU texture fallback path" << std::endl;
        std::string s = hr_to_string(chr);
        fprintf(stderr, "DXGI: Create small GPU texture failed hr=0x%08x (%s) (w=%u h=%u)\n", chr, s.c_str(), smallDesc.Width, smallDesc.Height);
        // If device removed/reset or transient, try to recreate device+duplication once and retry
        bool retried = false;
        if (chr == DXGI_ERROR_DEVICE_REMOVED || chr == DXGI_ERROR_DEVICE_RESET) {
            // Attempt inline device/context + duplication recreate
            fprintf(stderr, "DXGI: detected device removed/reset, attempting inline recreate\n");
            D3D_FEATURE_LEVEL levels[] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0 };
            UINT creationFlags = D3D11_CREATE_DEVICE_BGRA_SUPPORT;
            ComPtr<ID3D11Device> newDevice;
            ComPtr<ID3D11DeviceContext> newContext;
            HRESULT chr2 = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, creationFlags, levels, ARRAYSIZE(levels), D3D11_SDK_VERSION, &newDevice, nullptr, &newContext);
            if (SUCCEEDED(chr2) && newDevice) {
                ComPtr<IDXGIOutputDuplication> newDup;
                int newAdapter=-1, newOutput=-1; std::string newId;
                if (recreate_duplication_for_display(newDevice, pimpl->displayId, newDup, newAdapter, newOutput, newId)) {
                    pimpl->device = newDevice; pimpl->context = newContext; pimpl->duplication = newDup; pimpl->adapterIndex = newAdapter; pimpl->outputIndex = newOutput;
                    if (!newId.empty()) pimpl->displayId = newId;
                    retried = true;
                    fprintf(stderr, "DXGI: inline recreate succeeded, retrying CreateTexture2D\n");
                    chr = pimpl->device->CreateTexture2D(&smallDesc, nullptr, &smallTex);
                    if (SUCCEEDED(chr) && smallTex) fprintf(stderr, "DXGI: Create small GPU texture succeeded after inline recreate\n");
                } else {
                    fprintf(stderr, "DXGI: inline recreate failed to get duplication for displayId='%s'\n", pimpl->displayId.c_str());
                }
            } else {
                fprintf(stderr, "DXGI: inline device recreate D3D11CreateDevice failed hr=0x%08x\n", chr2);
            }
        }
        if (FAILED(chr) || !smallTex) {
            std::string s2 = hr_to_string(chr);
            fprintf(stderr, "DXGI: Create small GPU texture final failure hr=0x%08x (%s) retried=%d\n", chr, s2.c_str(), retried ? 1 : 0);
            pimpl->duplication->ReleaseFrame();
            Rect r = {};
            // Use desired dims computed at capture time for fallback
            r.x = 0; r.y = 0; r.width = desired.width; r.height = desired.height;
            if (r.width <= 0 || r.height <= 0) {
                fprintf(stderr, "DXGI: fallback desired is empty w=%d h=%d - aborting fallback\n", r.width, r.height);
                return Result::Error;
            }
            if (gdi_capture_rect(r, outFrame, pimpl->targetHwnd)) return Result::Ok;
            return Result::Error;
        }
    }

    D3D11_BOX srcBox;
    srcBox.left = static_cast<UINT>(desired.x);
    srcBox.top = static_cast<UINT>(desired.y);
    srcBox.front = 0;
    srcBox.right = static_cast<UINT>(desired.x + desired.width);
    srcBox.bottom = static_cast<UINT>(desired.y + desired.height);
    srcBox.back = 1;

    pimpl->context->CopySubresourceRegion(smallTex.Get(), 0, 0, 0, 0, acquiredTex.Get(), 0, &srcBox);
    pimpl->context->Flush();

    D3D11_TEXTURE2D_DESC stagingDescSmall = smallDesc;
    stagingDescSmall.Usage = D3D11_USAGE_STAGING;
    stagingDescSmall.BindFlags = 0;
    stagingDescSmall.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stagingDescSmall.MiscFlags = 0;
    ComPtr<ID3D11Texture2D> stagingSmall;
    HRESULT schr = pimpl->device->CreateTexture2D(&stagingDescSmall, nullptr, &stagingSmall);
    std::cout << "DXGI: CreateTexture2D for staging result hr=0x" << std::hex << schr << std::endl;
    std::cout << "DXGI: stagingSmall pointer: " << (void*)stagingSmall.Get() << std::endl;
    std::cout << "DXGI: FAILED(schr)=" << FAILED(schr) << " !stagingSmall=" << (!stagingSmall) << std::endl;
    if (FAILED(schr) || !stagingSmall) {
        std::cout << "DXGI: Taking fallback path because FAILED(schr)=" << FAILED(schr) << " or !stagingSmall=" << (!stagingSmall) << std::endl;
        std::string ss = hr_to_string(schr);
        fprintf(stderr, "DXGI: Create small staging failed hr=0x%08x (%s)\n", schr, ss.c_str());
            // If device removed/reset, try to recreate and retry once
            bool stagedRetried = false;
            if (schr == DXGI_ERROR_DEVICE_REMOVED || schr == DXGI_ERROR_DEVICE_RESET) {
                // Attempt inline device/context + duplication recreate
                fprintf(stderr, "DXGI: detected device removed/reset while creating staging, attempting inline recreate\n");
                D3D_FEATURE_LEVEL levels[] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0 };
                UINT creationFlags = D3D11_CREATE_DEVICE_BGRA_SUPPORT;
                ComPtr<ID3D11Device> newDevice;
                ComPtr<ID3D11DeviceContext> newContext;
                HRESULT chr2 = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, creationFlags, levels, ARRAYSIZE(levels), D3D11_SDK_VERSION, &newDevice, nullptr, &newContext);
                if (SUCCEEDED(chr2) && newDevice) {
                    ComPtr<IDXGIOutputDuplication> newDup;
                    int newAdapter=-1, newOutput=-1; std::string newId;
                    if (recreate_duplication_for_display(newDevice, pimpl->displayId, newDup, newAdapter, newOutput, newId)) {
                        pimpl->device = newDevice; pimpl->context = newContext; pimpl->duplication = newDup; pimpl->adapterIndex = newAdapter; pimpl->outputIndex = newOutput;
                        if (!newId.empty()) pimpl->displayId = newId;
                        stagedRetried = true;
                        fprintf(stderr, "DXGI: inline recreate succeeded, retrying CreateTexture2D(staging)\n");
                        schr = pimpl->device->CreateTexture2D(&stagingDescSmall, nullptr, &stagingSmall);
                        if (SUCCEEDED(schr) && stagingSmall) fprintf(stderr, "DXGI: Create small staging succeeded after inline recreate\n");
                    } else {
                        fprintf(stderr, "DXGI: inline recreate failed to get duplication for displayId='%s'\n", pimpl->displayId.c_str());
                    }
                } else {
                    fprintf(stderr, "DXGI: inline device recreate D3D11CreateDevice failed hr=0x%08x\n", chr2);
                }
            }
            if (FAILED(schr) || !stagingSmall) {
                if (stagedRetried) {
                    std::string ss2 = hr_to_string(schr);
                    fprintf(stderr, "DXGI: Create small staging final failure hr=0x%08x (%s) retried=%d\n", schr, ss2.c_str(), stagedRetried ? 1 : 0);
                }
            
            // Try alternative: create a staging texture sized to desired and copy directly into it
            D3D11_TEXTURE2D_DESC altDesc = fullDesc;
            altDesc.Width = desired.width; altDesc.Height = desired.height;
            altDesc.Usage = D3D11_USAGE_STAGING;
            altDesc.BindFlags = 0;
            altDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
            altDesc.MiscFlags = 0;
            ComPtr<ID3D11Texture2D> altStaging;
            HRESULT ahr = pimpl->device->CreateTexture2D(&altDesc, nullptr, &altStaging);
            if (SUCCEEDED(ahr) && altStaging) {
                fprintf(stderr, "DXGI: created alt staging w=%u h=%u, attempting CopySubresourceRegion into staging\n", altDesc.Width, altDesc.Height);
                pimpl->context->CopySubresourceRegion(altStaging.Get(), 0, 0, 0, 0, acquiredTex.Get(), 0, &srcBox);
                pimpl->context->Flush();
                D3D11_MAPPED_SUBRESOURCE mappedAlt;
                HRESULT amhr = pimpl->context->Map(altStaging.Get(), 0, D3D11_MAP_READ, 0, &mappedAlt);
                if (SUCCEEDED(amhr)) {
                    int w = desired.width; int h = desired.height;
                    outFrame.width = w; outFrame.height = h; outFrame.format = capscr::PixelFormat::BGRA32; outFrame.stride = w*4;
                    outFrame.data.resize(static_cast<size_t>(outFrame.stride) * static_cast<size_t>(h));
                    uint8_t* srcBaseAlt = static_cast<uint8_t*>(mappedAlt.pData);
                    for (int y = 0; y < h; ++y) {
                        uint8_t* srcRow = srcBaseAlt + static_cast<size_t>(y) * mappedAlt.RowPitch;
                        size_t expected = static_cast<size_t>(w * 4);
                        size_t available = static_cast<size_t>(mappedAlt.RowPitch);
                        size_t copyBytes = (available < expected) ? available : expected;
                        memcpy(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride, srcRow, copyBytes);
                        if (expected > copyBytes) memset(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride + copyBytes, 0, expected - copyBytes);
                    }
                    pimpl->context->Unmap(altStaging.Get(), 0);
                    pimpl->duplication->ReleaseFrame();
                    fprintf(stderr, "DXGI: alt staging CopySubresourceRegion+Map succeeded\n");
                    return Result::Ok;
                } else {
                    std::string sss = hr_to_string(amhr);
                    fprintf(stderr, "DXGI: Map alt staging failed hr=0x%08x (%s)\n", amhr, sss.c_str());
                    // fall through to GDI fallback
                }
            } else {
                std::string ssss = hr_to_string(ahr);
                fprintf(stderr, "DXGI: Create alt staging failed hr=0x%08x (%s)\n", ahr, ssss.c_str());
            }

            pimpl->duplication->ReleaseFrame();
            Rect r = {}; r.x=0; r.y=0; r.width=desired.width; r.height=desired.height;
            if (r.width <= 0 || r.height <= 0) {
                fprintf(stderr, "DXGI: fallback desired is empty w=%d h=%d - aborting fallback\n", r.width, r.height);
                return Result::Error;
            }
            if (gdi_capture_rect(r, outFrame, pimpl->targetHwnd)) return Result::Ok;
            return Result::Error;
        }
    } else {
        std::cout << "DXGI: staging texture creation was successful, proceeding with normal GPU path..." << std::endl;
    }

        std::cout << "DXGI: staging creation successful, proceeding with normal path..." << std::endl;
        std::cout << "DXGI: about to copy resource to staging..." << std::endl;
        pimpl->context->CopyResource(stagingSmall.Get(), smallTex.Get());
        pimpl->context->Flush();

        D3D11_MAPPED_SUBRESOURCE mapped;
        HRESULT mhr = pimpl->context->Map(stagingSmall.Get(), 0, D3D11_MAP_READ, 0, &mapped);
        if (FAILED(mhr)) {
            fprintf(stderr, "DXGI: Map small staging failed hr=0x%08x\n", mhr);
            pimpl->duplication->ReleaseFrame();
            Rect r = {}; r.x=0; r.y=0; r.width=desired.width; r.height=desired.height;
            if (r.width <= 0 || r.height <= 0) {
                fprintf(stderr, "DXGI: fallback desired is empty w=%d h=%d - aborting fallback\n", r.width, r.height);
                return Result::Error;
            }
            if (gdi_capture_rect(r, outFrame, pimpl->targetHwnd)) return Result::Ok;
            return Result::Error;
        }

        int w = desired.width; int h = desired.height;
        outFrame.width = w; outFrame.height = h; outFrame.format = capscr::PixelFormat::BGRA32; outFrame.stride = w*4;
        outFrame.data.resize(static_cast<size_t>(outFrame.stride) * static_cast<size_t>(h));
        uint8_t* srcBase = static_cast<uint8_t*>(mapped.pData);
        for (int y = 0; y < h; ++y) {
            uint8_t* srcRow = srcBase + static_cast<size_t>(y) * mapped.RowPitch;
            size_t expected = static_cast<size_t>(w * 4);
            size_t available = static_cast<size_t>(mapped.RowPitch);
            size_t copyBytes = (available < expected) ? available : expected;
            memcpy(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride, srcRow, copyBytes);
            if (expected > copyBytes) memset(outFrame.data.data() + static_cast<size_t>(y) * outFrame.stride + copyBytes, 0, expected - copyBytes);
        }

        pimpl->context->Unmap(stagingSmall.Get(), 0);
        pimpl->context->Flush();
        pimpl->duplication->ReleaseFrame();
        return Result::Ok;
    }

Result DXGICapturer::captureGpu(GpuTexture& outTexture) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    return captureGpuUnlocked(outTexture);
}

Result DXGICapturer::captureZeroCopy(ZeroCopyFrame& outFrame, const ZeroCopyOptions& opts, const CaptureParams* processingParams) {
    std::lock_guard<std::mutex> g(pimpl->mtx);

    if (!pimpl->duplication || !pimpl->device) {
        CAPSCR_LOG_ERROR("captureZeroCopy: GPU capture not available");
        return Result::Unsupported;
    }

    DXGI_OUTDUPL_FRAME_INFO frameInfo{};
    ComPtr<IDXGIResource> dxgiResource;
    HRESULT hr = pimpl->duplication->AcquireNextFrame(500, &frameInfo, &dxgiResource);
    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        CAPSCR_LOG_TRACE("captureZeroCopy: no new frame available");
        return Result::Error;
    }
    if (FAILED(hr) || !dxgiResource) {
        CAPSCR_LOG_ERROR("captureZeroCopy: AcquireNextFrame failed");
        return Result::Error;
    }

    ComPtr<ID3D11Texture2D> acquiredTexture;
    hr = dxgiResource.As(&acquiredTexture);
    if (FAILED(hr) || !acquiredTexture) {
        pimpl->duplication->ReleaseFrame();
        CAPSCR_LOG_ERROR("captureZeroCopy: QI to ID3D11Texture2D failed");
        return Result::Error;
    }

    // If processingParams is provided and GPU compute is available, perform
    // GPU processing on the acquired texture and return a processed texture
    // wrapped in the ZeroCopyFrame. This may allocate an intermediate
    // texture (breaking strict zero-copy) but is convenient for callers.
    if (processingParams != nullptr && pimpl->compute && pimpl->compute->isAvailable()) {
        CAPSCR_LOG_INFO("captureZeroCopy: processingParams provided, attempting GPU processing");

        // Ensure acquired texture is compatible for compute; create compatible copy if needed
        ComPtr<ID3D11Texture2D> inputTexture = acquiredTexture;
        D3D11_TEXTURE2D_DESC acquiredDesc; acquiredTexture->GetDesc(&acquiredDesc);
        const UINT requiredBindFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
        ComPtr<ID3D11Texture2D> computeInput;
        if ((acquiredDesc.BindFlags & requiredBindFlags) != requiredBindFlags) {
            D3D11_TEXTURE2D_DESC compat = acquiredDesc;
            compat.BindFlags = requiredBindFlags;
            compat.Usage = D3D11_USAGE_DEFAULT;
            compat.CPUAccessFlags = 0;
            compat.MiscFlags = 0;
            HRESULT chr = pimpl->device->CreateTexture2D(&compat, nullptr, &computeInput);
            if (FAILED(chr) || !computeInput) {
                CAPSCR_LOG_WARN("captureZeroCopy: failed to create compute-compatible texture, falling back to local-borrow");
                // Fall through to normal local-borrow path below
                computeInput.Reset();
            } else {
                pimpl->context->CopyResource(computeInput.Get(), acquiredTexture.Get());
                inputTexture = computeInput;
            }
        }

        if (pimpl->compute && pimpl->compute->isAvailable()) {
            ComPtr<ID3D11Texture2D> processed;
            CaptureParams params = *processingParams;
            // If output dimensions not specified, use acquired texture dims
            if (params.output_width == 0) params.output_width = static_cast<int>(acquiredDesc.Width);
            if (params.output_height == 0) params.output_height = static_cast<int>(acquiredDesc.Height);
            Result pres = pimpl->compute->processTexture(inputTexture, params, processed);
            if (pres == Result::Ok && processed) {
                // Return processed texture as LocalBorrow: keep lifecycle and release semantics
                D3D11_TEXTURE2D_DESC pdesc; processed->GetDesc(&pdesc);

#ifdef _WIN32
                outFrame.d3d11_device = pimpl->device.Get();
                outFrame.d3d11_context = pimpl->context.Get();
                outFrame.d3d11_texture = processed.Get();
#endif
                outFrame.width = static_cast<int>(pdesc.Width);
                outFrame.height = static_cast<int>(pdesc.Height);
                outFrame.format = params.output_format;
                outFrame.mode = ZeroCopyFrame::Mode::LocalBorrow;

                // AddRef to keep alive until release()
                pimpl->device.Get()->AddRef();
                pimpl->context.Get()->AddRef();
                processed.Get()->AddRef();

                ComPtr<IDXGIOutputDuplication> dupRef = pimpl->duplication;
                ComPtr<ID3D11Texture2D> texRef = processed;
                ComPtr<ID3D11Device> devRef = pimpl->device;
                ComPtr<ID3D11DeviceContext> ctxRef = pimpl->context;

                outFrame.release_callback = [dupRef, texRef, devRef, ctxRef]() mutable {
                    if (dupRef) dupRef->ReleaseFrame();
                    try { texRef.Reset(); devRef.Reset(); ctxRef.Reset(); } catch(...) {}
                };

                return Result::Ok;
            } else {
                CAPSCR_LOG_WARN("captureZeroCopy: GPU processing failed, falling back to local-borrow result");
                // Fallthrough to return original acquired texture below
            }
        }
    }

    // NV12 conversion support in DXGICapturer::captureZeroCopy
    if (opts.prefer_nv12 && pimpl->compute && pimpl->compute->isAvailable()) {
        CAPSCR_LOG_INFO("captureZeroCopy: attempting NV12 conversion via DXGICompute");
        
        D3D11_TEXTURE2D_DESC acquiredDesc;
        acquiredTexture->GetDesc(&acquiredDesc);
        
        // Ensure the texture has proper bind flags for compute shader usage
        ComPtr<ID3D11Texture2D> inputTexture = acquiredTexture;
        const UINT requiredBind = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
        ComPtr<ID3D11Texture2D> compatTexture;
        
        if ((acquiredDesc.BindFlags & requiredBind) != requiredBind) {
            D3D11_TEXTURE2D_DESC compatDesc = acquiredDesc;
            compatDesc.BindFlags = requiredBind;
            compatDesc.Usage = D3D11_USAGE_DEFAULT;
            compatDesc.CPUAccessFlags = 0;
            compatDesc.MiscFlags = 0;
            
            HRESULT hr = pimpl->device->CreateTexture2D(&compatDesc, nullptr, &compatTexture);
            if (SUCCEEDED(hr) && compatTexture) {
                pimpl->context->CopyResource(compatTexture.Get(), acquiredTexture.Get());
                inputTexture = compatTexture;
                CAPSCR_LOG_DEBUG("captureZeroCopy: created compute-compatible texture for NV12 conversion");
            } else {
                CAPSCR_LOG_WARN("captureZeroCopy: failed to create compute-compatible texture, skipping NV12");
                // Skip NV12 conversion and fallthrough to regular texture path
                goto regular_texture_path;
            }
        }
        
        ComPtr<ID3D11Texture2D> yPlane, uvPlane;
        Result cret = pimpl->compute->convertToNV12(inputTexture, 
            static_cast<UINT>(acquiredDesc.Width), static_cast<UINT>(acquiredDesc.Height), 
            yPlane, uvPlane, true);  // Enable sharing for encoder compatibility
            
        if (cret == Result::Ok && yPlane && uvPlane) {
            CAPSCR_LOG_INFO("captureZeroCopy: NV12 conversion successful");
            
            // Fill ZeroCopyFrame with NV12 dual-texture semantic
#ifdef _WIN32
            outFrame.d3d11_device = pimpl->device.Get();
            outFrame.d3d11_context = pimpl->context.Get();
            outFrame.d3d11_texture = yPlane.Get();        // Y plane
            outFrame.d3d11_texture_uv = uvPlane.Get();    // UV plane
#endif
            outFrame.width = static_cast<int>(acquiredDesc.Width);
            outFrame.height = static_cast<int>(acquiredDesc.Height);
            outFrame.format = PixelFormat::NV12;
            outFrame.mode = ZeroCopyFrame::Mode::LocalBorrow;

            // AddRef to keep alive until release
            pimpl->device.Get()->AddRef();
            pimpl->context.Get()->AddRef();
            yPlane.Get()->AddRef();
            uvPlane.Get()->AddRef();

            // Capture references for release callback
            ComPtr<IDXGIOutputDuplication> dupRef = pimpl->duplication;
            ComPtr<ID3D11Texture2D> yRef = yPlane;
            ComPtr<ID3D11Texture2D> uvRef = uvPlane;
            ComPtr<ID3D11Device> devRef = pimpl->device;
            ComPtr<ID3D11DeviceContext> ctxRef = pimpl->context;

            outFrame.release_callback = [dupRef, yRef, uvRef, devRef, ctxRef]() mutable {
                if (dupRef) dupRef->ReleaseFrame();
                try { yRef.Reset(); uvRef.Reset(); devRef.Reset(); ctxRef.Reset(); } catch(...) {}
            };

            return Result::Ok;
        } else {
            CAPSCR_LOG_WARN("captureZeroCopy: NV12 conversion failed, falling back to regular texture");
            // fallthrough to regular path below
        }
    }

regular_texture_path:
    // Fill ZeroCopyFrame (original local-borrow behavior)
    D3D11_TEXTURE2D_DESC desc{};
    acquiredTexture->GetDesc(&desc);

#ifdef _WIN32
    outFrame.d3d11_device = pimpl->device.Get();
    outFrame.d3d11_context = pimpl->context.Get();
    outFrame.d3d11_texture = acquiredTexture.Get();
#endif
    outFrame.width = static_cast<int>(desc.Width);
    outFrame.height = static_cast<int>(desc.Height);
    outFrame.format = PixelFormat::BGRA32;
    outFrame.mode = ZeroCopyFrame::Mode::LocalBorrow;

    // AddRef to keep alive until release()
    pimpl->device.Get()->AddRef();
    pimpl->context.Get()->AddRef();
    acquiredTexture.Get()->AddRef();

    // Capture references for release callback
    ComPtr<IDXGIOutputDuplication> dupRef = pimpl->duplication;
    ComPtr<ID3D11Device> devRef = pimpl->device;
    ComPtr<ID3D11DeviceContext> ctxRef = pimpl->context;
    ComPtr<ID3D11Texture2D> texRef = acquiredTexture;

    outFrame.release_callback = [dupRef, texRef, devRef, ctxRef]() mutable {
        if (dupRef) dupRef->ReleaseFrame();
        try { texRef.Reset(); devRef.Reset(); ctxRef.Reset(); } catch(...) {}
    };

    return Result::Ok;
}

Result DXGICapturer::captureGpuUnlocked(GpuTexture& outTexture) {
    // Note: mutex must already be locked by caller
    
    if (!pimpl->device || !pimpl->duplication) {
        CAPSCR_LOG_ERROR("GPU capture called but device or duplication not available");
        return capscr::Result::Unsupported;
    }

    DXGI_OUTDUPL_FRAME_INFO frameInfo{};
    ComPtr<IDXGIResource> dxgiResource;
    HRESULT hr = pimpl->duplication->AcquireNextFrame(0, &frameInfo, &dxgiResource);
    
    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        CAPSCR_LOG_TRACE("GPU capture: no new frame available");
        return capscr::Result::Error; // Change to Error for now
    }
    
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("GPU capture: AcquireNextFrame failed");
        return capscr::Result::Error;
    }

    // Get the D3D11 texture from DXGI resource
    ComPtr<ID3D11Texture2D> acquiredTexture;
    hr = dxgiResource->QueryInterface(__uuidof(ID3D11Texture2D), 
                                     reinterpret_cast<void**>(acquiredTexture.GetAddressOf()));
    if (FAILED(hr)) {
        pimpl->duplication->ReleaseFrame();
        CAPSCR_LOG_ERROR("GPU capture: QueryInterface for ID3D11Texture2D failed");
        return capscr::Result::Error;
    }

    // Get texture description for validation
    D3D11_TEXTURE2D_DESC desc{};
    acquiredTexture->GetDesc(&desc);
    
    // Fill the GPU texture structure
    outTexture.d3d11_device = pimpl->device.Get();
    outTexture.d3d11_context = pimpl->context.Get();
    outTexture.d3d11_texture = acquiredTexture.Get();
    outTexture.width = static_cast<int>(desc.Width);
    outTexture.height = static_cast<int>(desc.Height);
    outTexture.format = capscr::PixelFormat::BGRA32; // DXGI Desktop Duplication uses BGRA format
    
    // Keep references to prevent COM objects from being destroyed
    pimpl->device.Get()->AddRef();
    pimpl->context.Get()->AddRef();
    acquiredTexture.Get()->AddRef();
    
    CAPSCR_LOG_DEBUG("GPU capture successful");
    
    // Note: Frame must be released by caller using ReleaseFrame() through context
    // or when the next captureGpu() call is made
    return capscr::Result::Ok;
}

bool DXGICapturer::isGpuCaptureAvailable() const {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    
    // GPU capture is available if we have both D3D11 device and DXGI duplication
    bool available = (pimpl->device != nullptr) && (pimpl->duplication != nullptr);
    
    CAPSCR_LOG_DEBUG("GPU capture availability checked");
    return available;
}

void DXGICapturer::setBlackFrameConfig(const BlackFrameConfig& config) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    pimpl->blackFrameConfig = config;
}

BlackFrameConfig DXGICapturer::getBlackFrameConfig() const {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    return pimpl->blackFrameConfig;
}

void DXGICapturer::shutdown() {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    pimpl->duplication.Reset();
    pimpl->context.Reset();
    pimpl->device.Reset();
    // unregister WinEventHook if present
    if (pimpl->winEventHook) {
        UnhookWinEvent(pimpl->winEventHook);
        pimpl->winEventHook = nullptr;
    }
    if (pimpl->targetHwnd) {
        std::lock_guard<std::mutex> lk(g_winHookMapMutex);
        g_winHookMap.erase(pimpl->targetHwnd);
    }
}


std::unique_ptr<ICapturer> createDXGICapturer() { return std::make_unique<DXGICapturer>(); }
std::unique_ptr<ICapturer> createDxgiCapturer() { return createDXGICapturer(); }

} // namespace capscr
#endif // _WIN32
