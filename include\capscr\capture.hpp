// capscr/capture.hpp - public API
// C++17
#pragma once

#include <cstdint>
#include <vector>
#include <string>
#include <memory>
#include <functional>

#ifdef _WIN32
#include <d3d11.h>
#endif

namespace capscr {

// Pixel format
enum class PixelFormat {
    Unknown,
    BGRA32,  // 4 bytes per pixel: Blue, Green, Red, Alpha
    RGBA32,  // 4 bytes per pixel: Red, Green, Blue, Alpha
    BGR24,   // 3 bytes per pixel: Blue, Green, Red
    RGB24,   // 3 bytes per pixel: Red, Green, Blue
    BGRX32,  // 4 bytes per pixel: Blue, Green, Red, X (unused)
    RGBX32,  // 4 bytes per pixel: Red, Green, Blue, X (unused)
    NV12     // YUV 4:2:0 planar format (Y plane + interleaved UV plane)
};

// Validate that a PixelFormat value is one of the recognized formats (not Unknown)
inline bool isValidPixelFormat(PixelFormat f) {
    switch (f) {
        case PixelFormat::BGRA32:
        case PixelFormat::RGBA32:
        case PixelFormat::BGR24:
        case PixelFormat::RGB24:
        case PixelFormat::BGRX32:
        case PixelFormat::RGBX32:
        case PixelFormat::NV12:
            return true;
        default:
            return false;
    }
}

// Image processing parameters for capture with resize/format conversion
struct CaptureParams {
    int output_width = 0;   // Target width (0 = no resize, use source size)
    int output_height = 0;  // Target height (0 = no resize, use source size)
    PixelFormat output_format = PixelFormat::BGRA32; // Target pixel format
    
    // Quality and performance settings
    enum class ResizeQuality {
        Fastest,    // Nearest neighbor or simple linear
        Balanced,   // Bilinear interpolation
        Highest     // Mitchell-Netravali or cubic
    } resize_quality = ResizeQuality::Balanced;
    
    enum class ColorSpace {
        sRGB,       // Standard sRGB color space
        Linear      // Linear color space (for better quality)
    } color_space = ColorSpace::sRGB;
    
    bool use_gpu_processing = true; // Use GPU compute shaders when available
};

// Options for zero-copy capture
struct ZeroCopyOptions {
    bool prefer_shared_handle = false; // try to produce a cross-process HANDLE
    bool require_same_adapter = true;  // fail if producers/consumers are on different adapters
    bool prefer_nv12 = false;          // attempt to produce NV12 for encoder if supported
    unsigned timeout_ms = 500;         // timeout for AcquireNextFrame / sync
};

// RAII wrapper for a zero-copy acquired GPU frame
class ZeroCopyFrame {
public:
    enum class Mode { LocalBorrow, SharedHandle, Unsupported };
    ZeroCopyFrame() = default;
    ZeroCopyFrame(const ZeroCopyFrame&) = delete;
    ZeroCopyFrame& operator=(const ZeroCopyFrame&) = delete;
    ZeroCopyFrame(ZeroCopyFrame&&) = default;
    ZeroCopyFrame& operator=(ZeroCopyFrame&&) = default;

#ifdef _WIN32
    // Non-owning pointer valid while frame is held (LocalBorrow). Use release()
    // to relinquish the DXGI duplication frame; destructor calls release().
    ID3D11Texture2D* d3d11_texture = nullptr;
    ID3D11Device* d3d11_device = nullptr;
    ID3D11DeviceContext* d3d11_context = nullptr;
    HANDLE shared_handle = nullptr; // If SharedHandle mode
    
    // NV12 dual-plane support
    ID3D11Texture2D* d3d11_texture_uv = nullptr; // UV plane for NV12 LocalBorrow
    HANDLE shared_handle_uv = nullptr;            // UV plane handle for NV12 SharedHandle
#endif
    int width = 0;
    int height = 0;
    PixelFormat format = PixelFormat::Unknown;
    Mode mode = Mode::Unsupported;

    // Internal release callback set by backend
    std::function<void()> release_callback;

    // Explicit release (idempotent)
    void release() {
        if (release_callback) {
            try { release_callback(); } catch(...) {}
            release_callback = nullptr;
        }
#ifdef _WIN32
        d3d11_texture = nullptr;
        d3d11_texture_uv = nullptr;
        d3d11_device = nullptr;
        d3d11_context = nullptr;
        if (shared_handle) { CloseHandle(shared_handle); shared_handle = nullptr; }
        if (shared_handle_uv) { CloseHandle(shared_handle_uv); shared_handle_uv = nullptr; }
#endif
        mode = Mode::Unsupported;
    }

    ~ZeroCopyFrame() { release(); }
    
    // Helper methods
    bool is_nv12() const { 
#ifdef _WIN32
        return (format == PixelFormat::NV12) && 
               ((mode == Mode::LocalBorrow && d3d11_texture_uv != nullptr) ||
                (mode == Mode::SharedHandle && shared_handle_uv != nullptr));
#else
        return false;
#endif
    }
};

// Information for a shared texture (cross-process)
struct SharedTextureInfo {
#ifdef _WIN32
    // For NV12 we may return two shared handles: Y plane and combined UV plane.
    HANDLE shared_handle = nullptr; // CloseHandle when done (for single-plane textures)
    HANDLE shared_handle_uv = nullptr; // Optional: UV plane handle for NV12 (CloseHandle when done)
#endif
    DXGI_FORMAT dxgi_format = DXGI_FORMAT_UNKNOWN;
    int width = 0;
    int height = 0;
    std::string adapter_luid; // adapter identifier for validation
    // Cross-process synchronization info: when true the producer will set up
    // a keyed mutex on the shared resource(s) and release the key after the
    // frame is ready. Consumers should QI the opened resource for
    // `IDXGIKeyedMutex` and call `AcquireSync(keyed_mutex_key, timeout)` to
    // obtain access before reading. Default key is 0.
    bool use_keyed_mutex = false;
    uint64_t keyed_mutex_key = 0;
    // Helper to indicate NV12 returned by producer
    bool is_nv12() const { return dxgi_format == DXGI_FORMAT_NV12 && shared_handle_uv != nullptr; }
};

// GPU texture handle for zero-copy operations
struct GpuTexture {
#ifdef _WIN32
    ID3D11Texture2D* d3d11_texture = nullptr;
    ID3D11Device* d3d11_device = nullptr;
    ID3D11DeviceContext* d3d11_context = nullptr;
#endif
    int width = 0;
    int height = 0;
    PixelFormat format = PixelFormat::Unknown;
    
    // Platform-specific texture information
    std::string backend_info; // For debugging/identification
    
    // Optional release callback for zero-copy lifetimes. If set, callers should
    // call release() when they are done with the GPU texture. The callback
    // should perform any necessary ReleaseFrame()/CloseHandle() and COM Release
    // operations. It is safe to call release() multiple times.
    std::function<void()> release_callback;

    // Release helper (idempotent). This will call the release_callback if present
    // and then clear internal pointers so the struct is no longer valid.
    void release() {
        if (release_callback) {
            try { release_callback(); } catch(...) {}
            release_callback = nullptr;
        }
#ifdef _WIN32
        d3d11_texture = nullptr;
        d3d11_device = nullptr;
        d3d11_context = nullptr;
#endif
    }

    // Destructor ensures release() is called to avoid leaving duplication frames
    // held unintentionally.
    ~GpuTexture() { release(); }
    
    bool is_valid() const {
#ifdef _WIN32
        return d3d11_texture != nullptr && d3d11_device != nullptr;
#else
        return false;
#endif
    }
};

// Represents a captured frame
struct Frame {
    int width = 0;
    int height = 0;
    PixelFormat format = PixelFormat::Unknown;
    std::vector<uint8_t> data; // contiguous pixel data
    int stride = 0;
};

// Capture target selection
struct Rect {
    int x = 0;
    int y = 0;
    int width = 0;
    int height = 0;
};

enum class CaptureTargetType {
    FullScreen,
    Region,
    Window
};

// Display information (for systems with multiple displays)
struct DisplayInfo {
    std::string id; // platform specific id (eg. \"\\.\\DISPLAY1\" on Windows or name on X11)
    std::string name; // friendly name if available
    Rect bounds; // full display bounds in global coordinates
    float scale = 1.0f; // device pixel ratio
};

// Backend capabilities - bitmask
enum class BackendCapability : uint32_t {
    None = 0,
    SupportWindow = 1 << 0,
    SupportRegion = 1 << 1,
    SupportFullScreen = 1 << 2,
    HighPerformance = 1 << 3, // e.g., DXGI, pipewire
    ZeroCopyGpu = 1 << 4, // Support GPU texture access without CPU copy
};

inline BackendCapability operator|(BackendCapability a, BackendCapability b) {
    return static_cast<BackendCapability>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

inline BackendCapability& operator|=(BackendCapability& a, BackendCapability b) {
    a = a | b;
    return a;
}

inline bool backendHas(BackendCapability mask, BackendCapability flag) {
    return (static_cast<uint32_t>(mask) & static_cast<uint32_t>(flag)) != 0;
}

// Result codes
enum class Result {
    Ok,
    Unsupported,
    Error,
    WindowMinimized,
    WindowClosed,
    WindowNotFound,
    BlackFrameDetected,  // Black/empty frame detected (temporary issue)
    RetryExhausted      // All retry attempts failed
};

// Black frame detection and retry configuration
struct BlackFrameConfig {
    bool enable_detection = true;           // Enable black frame detection
    bool enable_auto_retry = true;          // Automatically retry on black frame
    int max_retries = 3;                   // Maximum retry attempts
    int retry_delay_ms = 50;               // Delay between retries (milliseconds)
    float black_threshold = 0.01f;         // Threshold for considering frame "black" (0.0-1.0)
    bool log_black_frames = false;         // Log black frame events for debugging
    
    // Advanced options
    bool validate_tiny_probe = true;       // Use 1x1 probe for early detection
    bool validate_full_frame = true;       // Validate complete frame content
    int consecutive_failures_reset = 5;    // Reset capture after N consecutive failures
};

// Abstract capturer interface
class ICapturer {
public:
    virtual ~ICapturer() = default;
    // Initialize capturer.
    // - `displayId` can be empty to use primary display or system-default. When capturing a region,
    //   coordinates are interpreted in the chosen display's coordinate space.
    // - `optTarget` for Region specifies the area relative to the chosen display origin.
    // - `optWindowId` when targeting a Window should be platform-specific window identifier (stringified handle).
    virtual Result init(CaptureTargetType type, const std::string& displayId = "", const Rect* optTarget = nullptr, const std::string& optWindowId = "") = 0;
    // Query displays available on the system
    virtual std::vector<DisplayInfo> listDisplays() = 0;
    // Query backend capabilities
    virtual BackendCapability capabilities() const = 0;
    // Update capture target at runtime. Thread-safe implementations should allow calling this
    // while another thread is calling `capture()`.
    virtual Result setTarget(CaptureTargetType type, const std::string& displayId = "", const Rect* optTarget = nullptr, const std::string& optWindowId = "") = 0;
    virtual Rect getTarget() const = 0;
    // Capture a single frame into outFrame. Blocking call. (CPU copy)
    virtual Result capture(Frame& outFrame) = 0;
    
    // Enhanced capture with image processing (resize + format conversion)
    // Performs resize and/or format conversion according to params
    // - If output dimensions are 0, uses source dimensions
    // - GPU processing used when available and params.use_gpu_processing is true
    // - Falls back to CPU processing for unsupported combinations
    virtual Result capture(Frame& outFrame, const CaptureParams& params) {
        // Default implementation: capture at source size/format, then process
        Frame sourceFrame;
        Result result = capture(sourceFrame);
        if (result != Result::Ok) {
            return result;
        }
        
        // Apply processing if needed
        return processFrame(sourceFrame, outFrame, params);
    }
    
    // Zero-copy GPU texture capture (if supported)
    // Returns GPU texture handle that can be used directly for hardware encoding
    // The texture remains valid until the next capture() or captureGpu() call
    virtual Result captureGpu(GpuTexture& outTexture) {
        return Result::Unsupported; // Default implementation
    }
    // New zero-copy RAII capture API. Backends that support zero-copy should
    // override this to return a held ZeroCopyFrame. Default returns Unsupported.
    //
    // NOTE: The plain `captureZeroCopy(outFrame, opts)` is intended to provide
    // a minimal-overhead GPU texture reference (LocalBorrow or SharedHandle)
    // with no additional processing. Many consumers that need resize/format
    // conversion should use `capture(outFrame, params)` or `captureGpu(..., params)`.
    //
    // For convenience, a new overload allows requesting backend GPU processing
    // to be applied to the captured texture before returning the ZeroCopyFrame.
    // When `processingParams` is non-null, the backend MAY perform GPU resize/
    // format-conversion and return a processed texture (which may be a new
    // intermediate GPU texture, or a new shared handle). Callers must respect
    // the returned frame's `mode` and `release()` semantics. This overload
    // explicitly allows implementations to trade zero-copy purity for the
    // convenience of returning a processed GPU texture.
    virtual Result captureZeroCopy(ZeroCopyFrame& outFrame, const ZeroCopyOptions& opts = {}, const CaptureParams* processingParams = nullptr) {
        (void)outFrame; (void)opts; (void)processingParams; return Result::Unsupported;
    }
    
    // Enhanced GPU capture with processing parameters
    // GPU processing is always used when available for this method
    // The returned texture contains processed data according to params
    virtual Result captureGpu(GpuTexture& outTexture, const CaptureParams& params) {
        return Result::Unsupported; // Default implementation
    }
    
    // Check if GPU capture is currently available
    virtual bool isGpuCaptureAvailable() const {
        return backendHas(capabilities(), BackendCapability::ZeroCopyGpu);
    }
    
    // Black frame handling configuration
    virtual void setBlackFrameConfig(const BlackFrameConfig& config) {
        // Default implementation - subclasses should override
    }
    
    virtual BlackFrameConfig getBlackFrameConfig() const {
        // Return default configuration
        return BlackFrameConfig{};
    }
    
    // Release resources
    virtual void shutdown() = 0;

protected:
    // Image processing helper - processes frame according to parameters
    // This can be overridden by backends for optimal implementation
    virtual Result processFrame(const Frame& source, Frame& target, const CaptureParams& params);
};

// Factory: create platform-specific capturer. If not supported, returns nullptr.
std::unique_ptr<ICapturer> createBestCapturer();

// Platform-specific capturer factories
#ifdef _WIN32
std::unique_ptr<ICapturer> createGdiCapturer();
std::unique_ptr<ICapturer> createDxgiCapturer();
#endif

#ifdef __linux__
std::unique_ptr<ICapturer> createX11Capturer();
std::unique_ptr<ICapturer> createWaylandCapturer();
#endif

#ifdef __APPLE__
std::unique_ptr<ICapturer> createQuartzCapturer();
#endif

// Utility functions
namespace ImageUtils {
    // Get bytes per pixel for a given format
    int getBytesPerPixel(PixelFormat format);
    
    // Get the stride (bytes per row) for given width and format
    int getStride(int width, PixelFormat format);
    
    // Convert between pixel formats (CPU implementation)
    bool convertPixelFormat(const uint8_t* source, uint8_t* target, 
                           int width, int height, int sourceStride, int targetStride,
                           PixelFormat sourceFormat, PixelFormat targetFormat);
    // Save image as PNG to CAPSCR_DUMP_DIR or fallback path (returns true if written)
    bool savePng(const uint8_t* data, int width, int height, int stride, int bytesPerPixel, const std::string& name);
    // Save image as BMP to CAPSCR_DUMP_DIR or fallback path (returns true if written)
    bool saveBmp(const uint8_t* data, int width, int height, int stride, int bytesPerPixel, const std::string& name);
    
    // Resize image (CPU implementation) 
    bool resizeImage(const uint8_t* source, uint8_t* target,
                    int sourceWidth, int sourceHeight, int sourceStride,
                    int targetWidth, int targetHeight, int targetStride,
                    PixelFormat format, CaptureParams::ResizeQuality quality = CaptureParams::ResizeQuality::Balanced);
                    
    // Internal resize functions (for specialized use)
    bool resizeNearestNeighbor(const uint8_t* source, uint8_t* target,
                              int sourceWidth, int sourceHeight, int sourceStride,
                              int targetWidth, int targetHeight, int targetStride, int bpp);
                              
    bool resizeBilinear(const uint8_t* source, uint8_t* target,
                       int sourceWidth, int sourceHeight, int sourceStride,
                       int targetWidth, int targetHeight, int targetStride, int bpp);
                       
    bool resizeMitchellNetravali(const uint8_t* source, uint8_t* target,
                                int sourceWidth, int sourceHeight, int sourceStride,
                                int targetWidth, int targetHeight, int targetStride, int bpp);
                                
    // Mitchell-Netravali filter function
    float mitchellNetravali(float x, float B = 1.0f/3.0f, float C = 1.0f/3.0f);
}

} // namespace capscr
