#include <gtest/gtest.h>
#include "capscr/capture.hpp"
#include <vector>
#include <cstring>

using namespace capscr;

class FrameUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a test frame
        testFrame.width = 100;
        testFrame.height = 50;
        testFrame.format = PixelFormat::BGRA32;
        testFrame.stride = testFrame.width * 4;
        testFrame.data.resize(testFrame.stride * testFrame.height);
        
        // Fill with test pattern
        FillTestPattern(testFrame);
    }
    
    void FillTestPattern(Frame& frame) {
        for (int y = 0; y < frame.height; ++y) {
            for (int x = 0; x < frame.width; ++x) {
                size_t offset = y * frame.stride + x * 4;
                frame.data[offset + 0] = static_cast<uint8_t>(x); // B
                frame.data[offset + 1] = static_cast<uint8_t>(y); // G
                frame.data[offset + 2] = static_cast<uint8_t>((x + y) % 256); // R
                frame.data[offset + 3] = 255; // A
            }
        }
    }
    
    Frame testFrame;
};

TEST_F(FrameUtilsTest, FrameBasicProperties) {
    EXPECT_EQ(testFrame.width, 100);
    EXPECT_EQ(testFrame.height, 50);
    EXPECT_EQ(testFrame.format, PixelFormat::BGRA32);
    EXPECT_EQ(testFrame.stride, 400); // 100 * 4 bytes per pixel
    EXPECT_EQ(testFrame.data.size(), 20000); // 400 * 50
}

TEST_F(FrameUtilsTest, PixelAccess) {
    // Test accessing specific pixels
    for (int y = 0; y < testFrame.height; ++y) {
        for (int x = 0; x < testFrame.width; ++x) {
            size_t offset = y * testFrame.stride + x * 4;
            
            EXPECT_EQ(testFrame.data[offset + 0], static_cast<uint8_t>(x)); // B
            EXPECT_EQ(testFrame.data[offset + 1], static_cast<uint8_t>(y)); // G
            EXPECT_EQ(testFrame.data[offset + 2], static_cast<uint8_t>((x + y) % 256)); // R
            EXPECT_EQ(testFrame.data[offset + 3], 255); // A
            
            // Only test a few pixels to avoid slow test
            if (x > 5 && y > 5) break;
        }
        if (y > 5) break;
    }
}

TEST_F(FrameUtilsTest, FrameCopy) {
    Frame copyFrame;
    copyFrame.width = testFrame.width;
    copyFrame.height = testFrame.height;
    copyFrame.format = testFrame.format;
    copyFrame.stride = testFrame.stride;
    copyFrame.data = testFrame.data; // Copy data
    
    // Verify copy is identical
    EXPECT_EQ(copyFrame.width, testFrame.width);
    EXPECT_EQ(copyFrame.height, testFrame.height);
    EXPECT_EQ(copyFrame.format, testFrame.format);
    EXPECT_EQ(copyFrame.stride, testFrame.stride);
    EXPECT_EQ(copyFrame.data.size(), testFrame.data.size());
    
    // Verify data is identical
    EXPECT_EQ(memcmp(copyFrame.data.data(), testFrame.data.data(), testFrame.data.size()), 0);
}

TEST_F(FrameUtilsTest, FrameValidation) {
    // Valid frame
    EXPECT_GT(testFrame.width, 0);
    EXPECT_GT(testFrame.height, 0);
    EXPECT_GT(testFrame.stride, 0);
    EXPECT_FALSE(testFrame.data.empty());
    EXPECT_EQ(testFrame.data.size(), testFrame.stride * testFrame.height);
    
    // Test invalid frames
    Frame invalidFrame1;
    invalidFrame1.width = 0;
    invalidFrame1.height = 100;
    EXPECT_EQ(invalidFrame1.width, 0); // Invalid
    
    Frame invalidFrame2;
    invalidFrame2.width = 100;
    invalidFrame2.height = 0;
    EXPECT_EQ(invalidFrame2.height, 0); // Invalid
    
    Frame invalidFrame3;
    invalidFrame3.width = 100;
    invalidFrame3.height = 100;
    invalidFrame3.stride = 50; // Too small for width
    EXPECT_LT(invalidFrame3.stride, invalidFrame3.width * 4); // Invalid
}

TEST_F(FrameUtilsTest, PixelFormatProperties) {
    EXPECT_EQ(testFrame.format, PixelFormat::BGRA32);
    
    // BGRA32 should have 4 bytes per pixel
    int bytesPerPixel = 4;
    EXPECT_EQ(testFrame.stride, testFrame.width * bytesPerPixel);
}

TEST_F(FrameUtilsTest, DataIntegrity) {
    // Verify data hasn't been corrupted
    std::vector<uint8_t> originalData = testFrame.data;
    
    // Simulate some operations
    Frame tempFrame = testFrame;
    tempFrame.data[0] = 255; // Modify copy
    
    // Original should be unchanged
    EXPECT_EQ(testFrame.data, originalData);
    EXPECT_NE(tempFrame.data[0], testFrame.data[0]);
}

TEST_F(FrameUtilsTest, LargeFrameHandling) {
    // Test with a larger frame
    Frame largeFrame;
    largeFrame.width = 1920;
    largeFrame.height = 1080;
    largeFrame.format = PixelFormat::BGRA32;
    largeFrame.stride = largeFrame.width * 4;
    largeFrame.data.resize(largeFrame.stride * largeFrame.height);
    
    EXPECT_EQ(largeFrame.width, 1920);
    EXPECT_EQ(largeFrame.height, 1080);
    EXPECT_EQ(largeFrame.stride, 7680); // 1920 * 4
    EXPECT_EQ(largeFrame.data.size(), 8294400); // 7680 * 1080
    
    // Fill with pattern to ensure memory allocation worked
    for (size_t i = 0; i < largeFrame.data.size(); i += 4) {
        largeFrame.data[i] = static_cast<uint8_t>(i % 256);
        largeFrame.data[i + 1] = static_cast<uint8_t>((i / 4) % 256);
        largeFrame.data[i + 2] = static_cast<uint8_t>((i / 8) % 256);
        largeFrame.data[i + 3] = 255;
    }
    
    // Verify some pixels
    EXPECT_EQ(largeFrame.data[0], 0);
    EXPECT_EQ(largeFrame.data[3], 255);
    EXPECT_NE(largeFrame.data[1000], largeFrame.data[2000]);
}
