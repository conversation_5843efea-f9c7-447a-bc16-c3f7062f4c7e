#include "../../include/capscr/capture.hpp"
#include <cstring>
#include <algorithm>
#include <cstdlib>
#include <fstream>
#include <sstream>
#include <string>
#include <cstdint>
#include <iomanip>
#include "../../include/capscr/logging.hpp"
// tiny png writer
#define STB_IMAGE_WRITE_IMPLEMENTATION
#include "../third_party/stb_image_write.h"

namespace capscr {
namespace ImageUtils {

bool saveBmp(const uint8_t* data, int width, int height, int stride, int bytesPerPixel, const std::string& name) {
    const uint32_t fileHeaderSize = 14;
    const uint32_t infoHeaderSize = 40;
    uint16_t bfType = 0x4D42; // 'BM'
    uint32_t rowStride = ((width * bytesPerPixel + 3) & ~3);
    uint32_t dataSize = rowStride * static_cast<uint32_t>(height);
    uint32_t fileSize = fileHeaderSize + infoHeaderSize + dataSize;

    const char* dumpDirEnv = std::getenv("CAPSCR_DUMP_DIR");
    std::string outPath;
    if (dumpDirEnv && dumpDirEnv[0] != '\0') {
        outPath = std::string(dumpDirEnv) + "/" + name;
    } else {
        outPath = std::string("build/tests/Debug/") + name;
    }

    std::ofstream ofs(outPath, std::ios::binary);
    if (!ofs) return false;

    // BITMAPFILEHEADER
    ofs.write(reinterpret_cast<const char*>(&bfType), sizeof(bfType));
    ofs.write(reinterpret_cast<const char*>(&fileSize), sizeof(fileSize));
    uint32_t reserved = 0;
    ofs.write(reinterpret_cast<const char*>(&reserved), sizeof(reserved));
    uint32_t offsetBits = fileHeaderSize + infoHeaderSize;
    ofs.write(reinterpret_cast<const char*>(&offsetBits), sizeof(offsetBits));

    // BITMAPINFOHEADER
    uint32_t biSize = infoHeaderSize;
    int32_t biWidth = static_cast<int32_t>(width);
    int32_t biHeight = static_cast<int32_t>(height);
    uint16_t biPlanes = 1;
    uint16_t biBitCount = static_cast<uint16_t>(bytesPerPixel * 8);
    uint32_t biCompression = 0; // BI_RGB
    uint32_t biSizeImage = dataSize;
    int32_t biXPelsPerMeter = 0;
    int32_t biYPelsPerMeter = 0;
    uint32_t biClrUsed = 0;
    uint32_t biClrImportant = 0;

    ofs.write(reinterpret_cast<const char*>(&biSize), sizeof(biSize));
    ofs.write(reinterpret_cast<const char*>(&biWidth), sizeof(biWidth));
    ofs.write(reinterpret_cast<const char*>(&biHeight), sizeof(biHeight));
    ofs.write(reinterpret_cast<const char*>(&biPlanes), sizeof(biPlanes));
    ofs.write(reinterpret_cast<const char*>(&biBitCount), sizeof(biBitCount));
    ofs.write(reinterpret_cast<const char*>(&biCompression), sizeof(biCompression));
    ofs.write(reinterpret_cast<const char*>(&biSizeImage), sizeof(biSizeImage));
    ofs.write(reinterpret_cast<const char*>(&biXPelsPerMeter), sizeof(biXPelsPerMeter));
    ofs.write(reinterpret_cast<const char*>(&biYPelsPerMeter), sizeof(biYPelsPerMeter));
    ofs.write(reinterpret_cast<const char*>(&biClrUsed), sizeof(biClrUsed));
    ofs.write(reinterpret_cast<const char*>(&biClrImportant), sizeof(biClrImportant));

    // Pixel data: BMP stores rows bottom-up
    for (int y = 0; y < height; ++y) {
        const uint8_t* row = data + static_cast<size_t>(height - 1 - y) * static_cast<size_t>(stride);
        ofs.write(reinterpret_cast<const char*>(row), static_cast<std::streamsize>(width * bytesPerPixel));
        uint32_t padding = rowStride - (width * bytesPerPixel);
        if (padding) {
            std::string pad(padding, '\0');
            ofs.write(pad.data(), static_cast<std::streamsize>(pad.size()));
        }
    }

    // write a small marker
    std::string marker = outPath + ".marker";
    std::ofstream mom(marker);
    if (mom) mom << "dumped";

    return true;
}


int getBytesPerPixel(PixelFormat format) {
    switch (format) {
        case PixelFormat::BGRA32:
        case PixelFormat::RGBA32:
        case PixelFormat::BGRX32:
        case PixelFormat::RGBX32:
            return 4;
        case PixelFormat::BGR24:
        case PixelFormat::RGB24:
            return 3;
        default:
            return 4;
    }
}

int getStride(int width, PixelFormat format) {
    return width * getBytesPerPixel(format);
}

bool convertPixelFormat(const uint8_t* source, uint8_t* target,
                                   int width, int height, int sourceStride, int targetStride,
                                   PixelFormat sourceFormat, PixelFormat targetFormat) {
    // CAPSCR_LOG_INFO("convertPixelFormat called: srcFmt=%d tgtFmt=%d width=%d height=%d srcStride=%d tgtStride=%d",
    //                (int)sourceFormat, (int)targetFormat, width, height, sourceStride, targetStride);
    fprintf(stderr, "convertPixelFormat: srcFmt=%d tgtFmt=%d width=%d height=%d srcStride=%d tgtStride=%d\n",
            (int)sourceFormat, (int)targetFormat, width, height, sourceStride, targetStride);
    // Validate formats first
    if (!isValidPixelFormat(sourceFormat) || !isValidPixelFormat(targetFormat)) {
        CAPSCR_LOG_ERROR("convertPixelFormat: invalid pixel format srcFmt=%d tgtFmt=%d", (int)sourceFormat, (int)targetFormat);
        return false;
    }

    // If identical formats, do a straight copy with safe row handling
    if (sourceFormat == targetFormat) {
        for (int y = 0; y < height; ++y) {
            size_t s = static_cast<size_t>(sourceStride);
            size_t t = static_cast<size_t>(targetStride);
            size_t copyBytes = (s < t) ? s : t;
            memcpy(target + y * targetStride, source + y * sourceStride, copyBytes);
            if (targetStride > sourceStride) memset(target + y * targetStride + sourceStride, 0, targetStride - sourceStride);
        }
        // optional debug hex prefixes
        const char* dumpEnv = std::getenv("CAPSCR_DUMP_FRAMES");
        if (dumpEnv && dumpEnv[0] != '\0') {
            auto printHex = [&](const uint8_t* ptr, int stride, int h, const char* label) {
                int prod = stride * h;
                int toShow = (64 < prod) ? 64 : prod;
                std::ostringstream hss;
                hss << label << ": ";
                for (int i = 0; i < toShow; ++i) {
                    unsigned v = static_cast<unsigned>(ptr[i]);
                    hss << std::hex << std::setw(2) << std::setfill('0') << v;
                }
                CAPSCR_LOG_INFO(hss.str().c_str());
            };
            printHex(source, sourceStride, height, "DUMP_SRC_PREFIX");
            printHex(target, targetStride, height, "DUMP_TGT_PREFIX");
        }
        return true;
    }

    // Support common interchange: RGBA32 <-> BGRA32 by swapping R and B channels per pixel
    int srcBpp = getBytesPerPixel(sourceFormat);
    int tgtBpp = getBytesPerPixel(targetFormat);
    if (srcBpp == 4 && tgtBpp == 4) {
        // Only handle 4-byte formats here (e.g., RGBA32 <-> BGRA32, BGRX32, RGBX32)
        for (int y = 0; y < height; ++y) {
            const uint8_t* srow = source + static_cast<size_t>(y) * sourceStride;
            uint8_t* trow = target + static_cast<size_t>(y) * targetStride;
            for (int x = 0; x < width; ++x) {
                const uint8_t* sp = srow + x * srcBpp;
                uint8_t* tp = trow + x * tgtBpp;
                // Map source to target assuming orderings may be RGBA or BGRA
                // We'll try to detect common cases by checking known formats
                if (sourceFormat == PixelFormat::RGBA32 && targetFormat == PixelFormat::BGRA32) {
                    // R G B A -> B G R A
                    tp[0] = sp[2];
                    tp[1] = sp[1];
                    tp[2] = sp[0];
                    tp[3] = sp[3];
                } else if (sourceFormat == PixelFormat::BGRA32 && targetFormat == PixelFormat::RGBA32) {
                    tp[0] = sp[2];
                    tp[1] = sp[1];
                    tp[2] = sp[0];
                    tp[3] = sp[3];
                } else if (sourceFormat == PixelFormat::BGRX32 && targetFormat == PixelFormat::BGRA32) {
                    // B G R X -> B G R A (set A=255)
                    tp[0] = sp[0];
                    tp[1] = sp[1];
                    tp[2] = sp[2];
                    tp[3] = 0xFF;
                } else if (sourceFormat == PixelFormat::RGBX32 && targetFormat == PixelFormat::BGRA32) {
                    // R G B X -> B G R A
                    tp[0] = sp[2];
                    tp[1] = sp[1];
                    tp[2] = sp[0];
                    tp[3] = 0xFF;
                } else {
                    // Fallback: copy min bytes and zero-fill
                    int copy = (srcBpp < tgtBpp) ? srcBpp : tgtBpp;
                    memcpy(tp, sp, copy);
                    if (tgtBpp > srcBpp) memset(tp + srcBpp, 0, tgtBpp - srcBpp);
                }
            }
        }
        // optional debug hex prefixes for 4-byte conversion
        const char* dumpEnv = std::getenv("CAPSCR_DUMP_FRAMES");
        if (dumpEnv && dumpEnv[0] != '\0') {
            auto printHex = [&](const uint8_t* ptr, int stride, int h, const char* label) {
                int prod = stride * h;
                int toShow = (64 < prod) ? 64 : prod;
                std::ostringstream hss;
                hss << label << ": ";
                for (int i = 0; i < toShow; ++i) {
                    unsigned v = static_cast<unsigned>(ptr[i]);
                    hss << std::hex << std::setw(2) << std::setfill('0') << v;
                }
                CAPSCR_LOG_INFO(hss.str().c_str());
            };
            printHex(source, sourceStride, height, "DUMP_SRC_PREFIX");
            printHex(target, targetStride, height, "DUMP_TGT_PREFIX");
        }
        return true;
    }

    // Support conversions between 4-byte and 3-byte formats (drop/add alpha)
    if (srcBpp == 4 && tgtBpp == 3) {
        fprintf(stderr, "convertPixelFormat: taking 4->3 path srcFmt=%d tgtFmt=%d width=%d height=%d\n", (int)sourceFormat, (int)targetFormat, width, height);
        fprintf(stderr, "convertPixelFormat: 4->3 strides: src=%d tgt=%d expected_tgt=%d\n", sourceStride, targetStride, width * 3);
        for (int y = 0; y < height; ++y) {
            const uint8_t* srow = source + static_cast<size_t>(y) * sourceStride;
            uint8_t* trow = target + static_cast<size_t>(y) * targetStride;
            for (int x = 0; x < width; ++x) {
                const uint8_t* sp = srow + x * srcBpp;
                uint8_t* tp = trow + x * tgtBpp;
                
                // CRITICAL: More robust bounds checking
                // Check if we're within the current row boundaries
                if (x * tgtBpp + tgtBpp > targetStride) {
                    fprintf(stderr, "convertPixelFormat: 4->3 row bounds violation y=%d x=%d position=%d targetStride=%d\n", y, x, x * tgtBpp + tgtBpp, targetStride);
                    break; // Skip remaining pixels in this row
                }
                
                // Check if we're within the total buffer boundaries
                size_t required_bytes = static_cast<size_t>(y) * targetStride + x * tgtBpp + tgtBpp;
                size_t total_buffer_size = static_cast<size_t>(height) * targetStride;
                if (required_bytes > total_buffer_size) {
                    fprintf(stderr, "convertPixelFormat: 4->3 global bounds violation y=%d x=%d required=%zu total=%zu\n", y, x, required_bytes, total_buffer_size);
                    break; // Skip remaining pixels in this row
                }
                // Handle common cases explicitly
                if (sourceFormat == PixelFormat::BGRA32 && targetFormat == PixelFormat::BGR24) {
                    tp[0] = sp[0]; tp[1] = sp[1]; tp[2] = sp[2];
                } else if (sourceFormat == PixelFormat::RGBA32 && targetFormat == PixelFormat::RGB24) {
                    tp[0] = sp[0]; tp[1] = sp[1]; tp[2] = sp[2];
                } else if (sourceFormat == PixelFormat::BGRA32 && targetFormat == PixelFormat::RGB24) {
                    // B G R A -> R G B (critical path for test case)
                    tp[0] = sp[2]; tp[1] = sp[1]; tp[2] = sp[0];
                } else if (sourceFormat == PixelFormat::RGBA32 && targetFormat == PixelFormat::BGR24) {
                    // R G B A -> B G R
                    tp[0] = sp[2]; tp[1] = sp[1]; tp[2] = sp[0];
                } else {
                    // Generic: copy first three channels
                    tp[0] = sp[0]; tp[1] = sp[1]; tp[2] = sp[2];
                }
            }
        }
    fprintf(stderr, "convertPixelFormat: 4->3 conversion done\n");
    return true;
    }

    // Support conversions from 3-byte -> 4-byte (add opaque alpha)
    if (srcBpp == 3 && tgtBpp == 4) {
        fprintf(stderr, "convertPixelFormat: taking 3->4 path srcFmt=%d tgtFmt=%d\n", (int)sourceFormat, (int)targetFormat);
        for (int y = 0; y < height; ++y) {
            const uint8_t* srow = source + static_cast<size_t>(y) * sourceStride;
            uint8_t* trow = target + static_cast<size_t>(y) * targetStride;
            for (int x = 0; x < width; ++x) {
                const uint8_t* sp = srow + x * srcBpp;
                uint8_t* tp = trow + x * tgtBpp;
                if (sourceFormat == PixelFormat::BGR24 && targetFormat == PixelFormat::BGRA32) {
                    tp[0] = sp[0]; tp[1] = sp[1]; tp[2] = sp[2]; tp[3] = 0xFF;
                } else if (sourceFormat == PixelFormat::RGB24 && targetFormat == PixelFormat::RGBA32) {
                    tp[0] = sp[0]; tp[1] = sp[1]; tp[2] = sp[2]; tp[3] = 0xFF;
                } else if (sourceFormat == PixelFormat::BGR24 && targetFormat == PixelFormat::RGBA32) {
                    // B G R -> R G B A
                    tp[0] = sp[2]; tp[1] = sp[1]; tp[2] = sp[0]; tp[3] = 0xFF;
                } else if (sourceFormat == PixelFormat::RGB24 && targetFormat == PixelFormat::BGRA32) {
                    // R G B -> B G R A
                    tp[0] = sp[2]; tp[1] = sp[1]; tp[2] = sp[0]; tp[3] = 0xFF;
                } else {
                    // Generic: copy three channels and set alpha opaque
                    tp[0] = sp[0]; tp[1] = sp[1]; tp[2] = sp[2]; tp[3] = 0xFF;
                }
            }
        }
    fprintf(stderr, "convertPixelFormat: 3->4 conversion done\n");
    return true;
    }

    // Support 3-byte <-> 3-byte conversions (e.g., BGR24 <-> RGB24)
    if (srcBpp == 3 && tgtBpp == 3) {
        fprintf(stderr, "convertPixelFormat: taking 3->3 path srcFmt=%d tgtFmt=%d\n", (int)sourceFormat, (int)targetFormat);
        for (int y = 0; y < height; ++y) {
            const uint8_t* srow = source + static_cast<size_t>(y) * sourceStride;
            uint8_t* trow = target + static_cast<size_t>(y) * targetStride;
            for (int x = 0; x < width; ++x) {
                const uint8_t* sp = srow + x * 3;
                uint8_t* tp = trow + x * 3;
                if (sourceFormat == PixelFormat::BGR24 && targetFormat == PixelFormat::RGB24) {
                    tp[0] = sp[2]; tp[1] = sp[1]; tp[2] = sp[0];
                } else if (sourceFormat == PixelFormat::RGB24 && targetFormat == PixelFormat::BGR24) {
                    tp[0] = sp[2]; tp[1] = sp[1]; tp[2] = sp[0];
                } else {
                    // Generic copy
                    memcpy(tp, sp, 3);
                }
            }
        }
    fprintf(stderr, "convertPixelFormat: 3->3 conversion done\n");
    return true;
    }

    // Unsupported conversion for other bpp combinations
    // Provide debug dump of prefixes to help diagnosis
    CAPSCR_LOG_ERROR("convertPixelFormat: unsupported conversion srcFmt=%d tgtFmt=%d srcBpp=%d tgtBpp=%d srcStride=%d tgtStride=%d", (int)sourceFormat, (int)targetFormat, srcBpp, tgtBpp, sourceStride, targetStride);
    
    const char* dumpEnv = std::getenv("CAPSCR_DUMP_FRAMES");
    if (dumpEnv && dumpEnv[0] != '\0') {
        auto printHex = [&](const uint8_t* ptr, int stride, int h, const char* label) {
            int prod = stride * h;
            int toShow = (64 < prod) ? 64 : prod;
            std::ostringstream hss;
            hss << label << ": ";
            for (int i = 0; i < toShow; ++i) {
                unsigned v = static_cast<unsigned>(ptr[i]);
                hss << std::hex << std::setw(2) << std::setfill('0') << v;
            }
            CAPSCR_LOG_INFO(hss.str().c_str());
        };
        // best-effort prints (source/target may be null or not match sizes)
        printHex(source, sourceStride, height, "DUMP_SRC_PREFIX");
        printHex(target, targetStride, height, "DUMP_TGT_PREFIX");
    }
    return false;
}

bool resizeImage(const uint8_t* source, uint8_t* target,
                            int sourceWidth, int sourceHeight, int sourceStride,
                            int targetWidth, int targetHeight, int targetStride,
                            PixelFormat format, CaptureParams::ResizeQuality quality) {
    const char* dumpEnv = std::getenv("CAPSCR_DUMP_FRAMES");
    if (dumpEnv && dumpEnv[0] != '\0') {
        std::ostringstream f1;
        f1 << "capscr_resize_src_" << std::hex << reinterpret_cast<uintptr_t>(source) << "_" << sourceWidth << "x" << sourceHeight << ".bmp";
        saveBmp(source, sourceWidth, sourceHeight, sourceStride, getBytesPerPixel(format), f1.str());
        std::ostringstream f2;
        f2 << "capscr_resize_tgt_" << std::hex << reinterpret_cast<uintptr_t>(target) << "_" << targetWidth << "x" << targetHeight << ".bmp";
        // create an empty target file to observe
        saveBmp(target, targetWidth, targetHeight, targetStride, getBytesPerPixel(format), f2.str());
    }
    
    int bpp = getBytesPerPixel(format);
    
    // Choose resize algorithm based on quality setting
    switch (quality) {
        case CaptureParams::ResizeQuality::Fastest:
            return resizeNearestNeighbor(source, target, sourceWidth, sourceHeight, sourceStride,
                                        targetWidth, targetHeight, targetStride, bpp);
        
        case CaptureParams::ResizeQuality::Balanced:
            return resizeBilinear(source, target, sourceWidth, sourceHeight, sourceStride,
                                 targetWidth, targetHeight, targetStride, bpp);
        
        case CaptureParams::ResizeQuality::Highest:
            return resizeMitchellNetravali(source, target, sourceWidth, sourceHeight, sourceStride,
                                          targetWidth, targetHeight, targetStride, bpp);
        
        default:
            return resizeNearestNeighbor(source, target, sourceWidth, sourceHeight, sourceStride,
                                        targetWidth, targetHeight, targetStride, bpp);
    }
}

// Nearest neighbor resize (fastest)
bool resizeNearestNeighbor(const uint8_t* source, uint8_t* target,
                          int sourceWidth, int sourceHeight, int sourceStride,
                          int targetWidth, int targetHeight, int targetStride, int bpp) {
    for (int y = 0; y < targetHeight; ++y) {
        int srcY = (y * sourceHeight) / targetHeight;
        const uint8_t* srcRow = source + srcY * sourceStride;
        uint8_t* dstRow = target + y * targetStride;
        for (int x = 0; x < targetWidth; ++x) {
            int srcX = (x * sourceWidth) / targetWidth;
            const uint8_t* srcPx = srcRow + srcX * bpp;
            uint8_t* dstPx = dstRow + x * bpp;
            // Bounds check to prevent heap corruption
            if (static_cast<size_t>(x * bpp + bpp) > static_cast<size_t>(targetStride)) {
                fprintf(stderr, "resizeNearestNeighbor: bounds violation x=%d bpp=%d targetStride=%d\n", x, bpp, targetStride);
                break; // Skip remaining pixels in this row
            }
            memcpy(dstPx, srcPx, bpp);
        }
    }
    return true;
}

// Bilinear interpolation resize
bool resizeBilinear(const uint8_t* source, uint8_t* target,
                   int sourceWidth, int sourceHeight, int sourceStride,
                   int targetWidth, int targetHeight, int targetStride, int bpp) {
    float scaleX = static_cast<float>(sourceWidth) / targetWidth;
    float scaleY = static_cast<float>(sourceHeight) / targetHeight;
    
    for (int y = 0; y < targetHeight; ++y) {
        float srcY = (y + 0.5f) * scaleY - 0.5f;
        int y0 = static_cast<int>(srcY);
        int y1 = (y0 + 1 < sourceHeight) ? (y0 + 1) : (sourceHeight - 1);
        float dy = srcY - y0;
        if (y0 < 0) { y0 = 0; dy = 0; }
        
        uint8_t* dstRow = target + y * targetStride;
        
        for (int x = 0; x < targetWidth; ++x) {
            float srcX = (x + 0.5f) * scaleX - 0.5f;
            int x0 = static_cast<int>(srcX);
            int x1 = (x0 + 1 < sourceWidth) ? (x0 + 1) : (sourceWidth - 1);
            float dx = srcX - x0;
            if (x0 < 0) { x0 = 0; dx = 0; }
            
            const uint8_t* p00 = source + y0 * sourceStride + x0 * bpp;
            const uint8_t* p01 = source + y0 * sourceStride + x1 * bpp;
            const uint8_t* p10 = source + y1 * sourceStride + x0 * bpp;
            const uint8_t* p11 = source + y1 * sourceStride + x1 * bpp;
            
            uint8_t* dstPx = dstRow + x * bpp;
            
            // Bounds check to prevent heap corruption
            if (static_cast<size_t>(x * bpp + bpp) > static_cast<size_t>(targetStride)) {
                fprintf(stderr, "resizeBilinear: bounds violation x=%d bpp=%d targetStride=%d\n", x, bpp, targetStride);
                break; // Skip remaining pixels in this row
            }
            
            for (int c = 0; c < bpp; ++c) {
                float v00 = p00[c];
                float v01 = p01[c];
                float v10 = p10[c];
                float v11 = p11[c];
                
                float v0 = v00 * (1 - dx) + v01 * dx;
                float v1 = v10 * (1 - dx) + v11 * dx;
                float v = v0 * (1 - dy) + v1 * dy;
                
                dstPx[c] = static_cast<uint8_t>(std::clamp(v, 0.0f, 255.0f));
            }
        }
    }
    return true;
}

// Mitchell-Netravali filter function
float mitchellNetravali(float x, float B, float C) {
    x = std::abs(x);
    
    if (x < 1.0f) {
        return ((12.0f - 9.0f * B - 6.0f * C) * x * x * x +
                (-18.0f + 12.0f * B + 6.0f * C) * x * x +
                (6.0f - 2.0f * B)) / 6.0f;
    } else if (x < 2.0f) {
        return ((-B - 6.0f * C) * x * x * x +
                (6.0f * B + 30.0f * C) * x * x +
                (-12.0f * B - 48.0f * C) * x +
                (8.0f * B + 24.0f * C)) / 6.0f;
    } else {
        return 0.0f;
    }
}

// Mitchell-Netravali resize (highest quality)
bool resizeMitchellNetravali(const uint8_t* source, uint8_t* target,
                            int sourceWidth, int sourceHeight, int sourceStride,
                            int targetWidth, int targetHeight, int targetStride, int bpp) {
    float scaleX = static_cast<float>(sourceWidth) / targetWidth;
    float scaleY = static_cast<float>(sourceHeight) / targetHeight;
    
    const float filterSupport = 2.0f; // Mitchell-Netravali has support of 2
    
    for (int y = 0; y < targetHeight; ++y) {
        float centerY = (y + 0.5f) * scaleY - 0.5f;
        int startY = static_cast<int>(std::floor(centerY - filterSupport));
        int endY = static_cast<int>(std::ceil(centerY + filterSupport));
        
        uint8_t* dstRow = target + y * targetStride;
        
        for (int x = 0; x < targetWidth; ++x) {
            float centerX = (x + 0.5f) * scaleX - 0.5f;
            int startX = static_cast<int>(std::floor(centerX - filterSupport));
            int endX = static_cast<int>(std::ceil(centerX + filterSupport));
            
            float weightSum = 0.0f;
            float colorSum[4] = {0.0f, 0.0f, 0.0f, 0.0f}; // RGBA
            
            for (int sy = startY; sy <= endY; ++sy) {
                int clampedY = std::clamp(sy, 0, sourceHeight - 1);
                float weightY = mitchellNetravali(sy - centerY);
                
                if (weightY == 0.0f) continue;
                
                for (int sx = startX; sx <= endX; ++sx) {
                    int clampedX = std::clamp(sx, 0, sourceWidth - 1);
                    float weightX = mitchellNetravali(sx - centerX);
                    
                    if (weightX == 0.0f) continue;
                    
                    float weight = weightX * weightY;
                    weightSum += weight;
                    
                    const uint8_t* srcPx = source + clampedY * sourceStride + clampedX * bpp;
                    for (int c = 0; c < bpp; ++c) {
                        colorSum[c] += srcPx[c] * weight;
                    }
                }
            }
            
            uint8_t* dstPx = dstRow + x * bpp;
            
            // Bounds check to prevent heap corruption
            if (static_cast<size_t>(x * bpp + bpp) > static_cast<size_t>(targetStride)) {
                fprintf(stderr, "resizeMitchellNetravali: bounds violation x=%d bpp=%d targetStride=%d\n", x, bpp, targetStride);
                break; // Skip remaining pixels in this row
            }
            
            if (weightSum > 0.0f) {
                for (int c = 0; c < bpp; ++c) {
                    float value = colorSum[c] / weightSum;
                    dstPx[c] = static_cast<uint8_t>(std::clamp(value, 0.0f, 255.0f));
                }
            } else {
                // Fallback to nearest neighbor if no weights
                int nearestY = std::clamp(static_cast<int>(centerY + 0.5f), 0, sourceHeight - 1);
                int nearestX = std::clamp(static_cast<int>(centerX + 0.5f), 0, sourceWidth - 1);
                const uint8_t* srcPx = source + nearestY * sourceStride + nearestX * bpp;
                memcpy(dstPx, srcPx, bpp);
            }
        }
    }
    return true;
}

bool savePng(const uint8_t* data, int width, int height, int stride, int bytesPerPixel, const std::string& name) {
    const char* dumpDirEnv = std::getenv("CAPSCR_DUMP_DIR");
    std::string outPath;
    if (dumpDirEnv && dumpDirEnv[0] != '\0') {
        outPath = std::string(dumpDirEnv) + "/" + name;
    } else {
        outPath = std::string("build/tests/Debug/") + name;
    }

    // Ensure we provide contiguous row data in the expected channel order (stb expects RGBA)
    int comp = bytesPerPixel;
    const uint8_t* writeData = data;
    std::vector<uint8_t> temp;
    if (comp == 4) {
        // Assume input is BGRA (our internal format). Convert to RGBA for PNG.
        temp.resize(static_cast<size_t>(width) * static_cast<size_t>(height) * 4);
        for (int y = 0; y < height; ++y) {
            const uint8_t* srcRow = data + static_cast<size_t>(y) * stride;
            uint8_t* dstRow = temp.data() + static_cast<size_t>(y) * width * 4;
            for (int x = 0; x < width; ++x) {
                const uint8_t* sp = srcRow + x * 4;
                uint8_t* dp = dstRow + x * 4;
                dp[0] = sp[2]; // R
                dp[1] = sp[1]; // G
                dp[2] = sp[0]; // B
                dp[3] = sp[3]; // A
            }
        }
        writeData = temp.data();
        stride = width * 4; // tightly packed
    }

    int ok = stbi_write_png(outPath.c_str(), width, height, 4, writeData, stride);
    if (ok) {
        fprintf(stderr, "savePng: wrote %s (%dx%d comp=%d)\n", outPath.c_str(), width, height, 4);
    } else {
        fprintf(stderr, "savePng: FAILED to write %s\n", outPath.c_str());
    }
    return ok != 0;
}

} // namespace ImageUtils

} // namespace capscr
