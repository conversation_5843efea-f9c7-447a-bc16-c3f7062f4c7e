#include "capscr/config.hpp"
#include "capscr/logging.hpp"
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>

int main() {
    std::cout << "=== Configuration System Test ===" << std::endl;
    
    // Check if file exists first
    std::ifstream file("capscr_config.json");
    if (!file.good()) {
        std::cout << "Configuration file does not exist!" << std::endl;
        return 1;
    }
    file.close();
    std::cout << "Configuration file exists." << std::endl;
    
    // Load configuration from file
    auto& manager = capscr::config::ConfigManager::instance();
    std::cout << "ConfigManager instance obtained." << std::endl;
    
    bool loaded = manager.load_config("capscr_config.json");
    std::cout << "load_config returned: " << (loaded ? "true" : "false") << std::endl;
    
    if (loaded) {
        std::cout << "Configuration loaded successfully!" << std::endl;
        
        // Get config and initialize logging
        const auto& config = manager.get_config();
        capscr::logging::init(config.logging);
        
        // Test basic logging
        CAPSCR_LOG_INFO("Configuration test started");
        CAPSCR_LOG_DEBUG("Debug level: {}", static_cast<int>(config.logging.level));
        CAPSCR_LOG_TRACE("This should be filtered if level > trace");
        
        // Test different log levels
        CAPSCR_LOG_ERROR("Error message test");
        CAPSCR_LOG_WARN("Warning message test");
        
        std::cout << "Basic logging tests completed." << std::endl;
    } else {
        std::cout << "Failed to load configuration, using defaults." << std::endl;
        
        // Initialize with default config
        capscr::logging::init();
        CAPSCR_LOG_INFO("Using default configuration");
    }
    
    // Shutdown logging
    capscr::logging::shutdown();
    
    std::cout << "=== Test completed ===" << std::endl;
    return 0;
}
