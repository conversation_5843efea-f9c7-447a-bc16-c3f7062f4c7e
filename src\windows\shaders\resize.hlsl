// Mitchell-Netravali resize compute shader
// High-quality two-pass image resize using Mitchell-Netra<PERSON> filter
// Based on "Reconstruction Filters in Computer Graphics" by <PERSON> and <PERSON><PERSON>ra<PERSON>i

// Constant buffer for resize parameters
cbuffer ResizeParams : register(b0)
{
    uint4 dimensions;        // x=src_width, y=src_height, z=dst_width, w=dst_height
    float4 params;          // x=filter_support, y=filter_param_B, z=filter_param_C, w=pass_direction
    float4 scale_offset;    // x=scale_x, y=scale_y, z=offset_x, w=offset_y
};

// Input and output textures
Texture2D<float4> InputTexture : register(t0);
RWTexture2D<float4> OutputTexture : register(u0);

// Sampler for input texture
SamplerState LinearSampler : register(s0);

// Thread group size (must match C++ code)
#define THREAD_GROUP_SIZE_X 16
#define THREAD_GROUP_SIZE_Y 16

// Mitchell-Netra<PERSON> filter function
// B=1/3, C=1/3 provides the best perceptual balance (<PERSON>'s recommendation)
// B controls blur vs ringing, <PERSON> controls overshoot vs undershoot
float MitchellNetravali(float x, float B, float C)
{
    x = abs(x);
    
    if (x < 1.0)
    {
        return ((12.0 - 9.0 * B - 6.0 * C) * x * x * x +
                (-18.0 + 12.0 * B + 6.0 * C) * x * x +
                (6.0 - 2.0 * B)) / 6.0;
    }
    else if (x < 2.0)
    {
        return ((-B - 6.0 * C) * x * x * x +
                (6.0 * B + 30.0 * C) * x * x +
                (-12.0 * B - 48.0 * C) * x +
                (8.0 * B + 24.0 * C)) / 6.0;
    }
    else
    {
        return 0.0;
    }
}

// Optimized Mitchell-Netravali with default parameters (B=1/3, C=1/3)
float MitchellNetravaliDefault(float x)
{
    x = abs(x);
    
    if (x < 1.0)
    {
        return (7.0 * x * x * x - 12.0 * x * x + 16.0 / 3.0) / 6.0;
    }
    else if (x < 2.0)
    {
        return (-7.0 * x * x * x / 3.0 + 12.0 * x * x - 20.0 * x + 32.0 / 3.0) / 6.0;
    }
    else
    {
        return 0.0;
    }
}

// Horizontal resize pass
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSHorizontalResize(uint3 id : SV_DispatchThreadID)
{
    uint dst_x = id.x;
    uint dst_y = id.y;
    
    // Check bounds
    if (dst_x >= dimensions.z || dst_y >= dimensions.w)
        return;
    
    uint src_width = dimensions.x;
    uint src_height = dimensions.y;
    uint dst_width = dimensions.z;
    
    // Calculate source position
    float scale_x = scale_offset.x;
    float src_x_f = (float(dst_x) + 0.5) / scale_x - 0.5;
    
    // Mitchell-Netravali has support of 2.0
    int filter_support = 2;
    int start_x = int(floor(src_x_f - filter_support));
    int end_x = int(ceil(src_x_f + filter_support));
    
    float4 color = float4(0.0, 0.0, 0.0, 0.0);
    float weight_sum = 0.0;
    
    // Use configurable filter parameters
    float B = params.y;
    float C = params.z;
    
    for (int x = start_x; x <= end_x; x++)
    {
        // Clamp to texture bounds
        int sample_x = clamp(x, 0, int(src_width) - 1);
        
        // Calculate filter weight
        float distance = abs(float(x) - src_x_f);
        float weight = MitchellNetravali(distance, B, C);
        
        if (weight != 0.0)
        {
            // Sample input texture
            float4 sample_color = InputTexture.Load(int3(sample_x, dst_y, 0));
            color += sample_color * weight;
            weight_sum += weight;
        }
    }
    
    // Normalize and write result
    if (weight_sum > 0.0)
    {
        color /= weight_sum;
    }
    
    OutputTexture[uint2(dst_x, dst_y)] = color;
}

// Vertical resize pass
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSVerticalResize(uint3 id : SV_DispatchThreadID)
{
    uint dst_x = id.x;
    uint dst_y = id.y;
    
    // Check bounds
    if (dst_x >= dimensions.z || dst_y >= dimensions.w)
        return;
    
    uint src_width = dimensions.x;
    uint src_height = dimensions.y;
    uint dst_height = dimensions.w;
    
    // Calculate source position
    float scale_y = scale_offset.y;
    float src_y_f = (float(dst_y) + 0.5) / scale_y - 0.5;
    
    // Mitchell-Netravali has support of 2.0
    int filter_support = 2;
    int start_y = int(floor(src_y_f - filter_support));
    int end_y = int(ceil(src_y_f + filter_support));
    
    float4 color = float4(0.0, 0.0, 0.0, 0.0);
    float weight_sum = 0.0;
    
    // Use configurable filter parameters
    float B = params.y;
    float C = params.z;
    
    for (int y = start_y; y <= end_y; y++)
    {
        // Clamp to texture bounds
        int sample_y = clamp(y, 0, int(src_height) - 1);
        
        // Calculate filter weight
        float distance = abs(float(y) - src_y_f);
        float weight = MitchellNetravali(distance, B, C);
        
        if (weight != 0.0)
        {
            // Sample input texture
            float4 sample_color = InputTexture.Load(int3(dst_x, sample_y, 0));
            color += sample_color * weight;
            weight_sum += weight;
        }
    }
    
    // Normalize and write result
    if (weight_sum > 0.0)
    {
        color /= weight_sum;
    }
    
    OutputTexture[uint2(dst_x, dst_y)] = color;
}

// Single-pass resize (for small scale changes where quality difference is minimal)
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSSinglePassResize(uint3 id : SV_DispatchThreadID)
{
    uint dst_x = id.x;
    uint dst_y = id.y;
    
    // Check bounds
    if (dst_x >= dimensions.z || dst_y >= dimensions.w)
        return;
    
    uint src_width = dimensions.x;
    uint src_height = dimensions.y;
    
    // Calculate source positions
    float scale_x = scale_offset.x;
    float scale_y = scale_offset.y;
    float src_x_f = (float(dst_x) + 0.5) / scale_x - 0.5;
    float src_y_f = (float(dst_y) + 0.5) / scale_y - 0.5;
    
    // Mitchell-Netravali has support of 2.0
    int filter_support = 2;
    
    int start_x = int(floor(src_x_f - filter_support));
    int end_x = int(ceil(src_x_f + filter_support));
    int start_y = int(floor(src_y_f - filter_support));
    int end_y = int(ceil(src_y_f + filter_support));
    
    float4 color = float4(0.0, 0.0, 0.0, 0.0);
    float weight_sum = 0.0;
    
    // Use configurable filter parameters
    float B = params.y;
    float C = params.z;
    
    for (int y = start_y; y <= end_y; y++)
    {
        for (int x = start_x; x <= end_x; x++)
        {
            // Clamp to texture bounds
            int sample_x = clamp(x, 0, int(src_width) - 1);
            int sample_y = clamp(y, 0, int(src_height) - 1);
            
            // Calculate filter weights
            float distance_x = abs(float(x) - src_x_f);
            float distance_y = abs(float(y) - src_y_f);
            float weight_x = MitchellNetravali(distance_x, B, C);
            float weight_y = MitchellNetravali(distance_y, B, C);
            float weight = weight_x * weight_y;
            
            if (weight != 0.0)
            {
                // Sample input texture
                float4 sample_color = InputTexture.Load(int3(sample_x, sample_y, 0));
                color += sample_color * weight;
                weight_sum += weight;
            }
        }
    }
    
    // Normalize and write result
    if (weight_sum > 0.0)
    {
        color /= weight_sum;
    }
    
    OutputTexture[uint2(dst_x, dst_y)] = color;
}
