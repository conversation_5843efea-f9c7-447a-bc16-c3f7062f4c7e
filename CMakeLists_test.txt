cmake_minimum_required(VERSION 3.16)
project(gpu_logging_test)

set(CMAKE_CXX_STANDARD 17)

# Include the main project build directory to find targets
set(capscr_DIR "${CMAKE_CURRENT_SOURCE_DIR}/build")

# Add include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Create the test executable
add_executable(test_gpu_logging test_gpu_logging.cpp)

# Link with libraries
target_link_libraries(test_gpu_logging 
    ${CMAKE_CURRENT_SOURCE_DIR}/build/Release/capscr_win.lib
    ${CMAKE_CURRENT_SOURCE_DIR}/build/_deps/spdlog-build/Release/spdlog.lib
    d3d11
    dxgi 
    gdi32 
    user32
)
