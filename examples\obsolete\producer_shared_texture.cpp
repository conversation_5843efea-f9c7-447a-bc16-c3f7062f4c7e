#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <iostream>
#include <vector>
#include <string>

using Microsoft::WRL::ComPtr;

// Simple producer that creates a shared texture pair (Y and UV) and writes
// a simple pattern into them. This is intended to be paired with
// consumer_shared_texture in the examples/obsolete directory.

static void usage() {
    std::cout << "producer_shared_texture [--w width] [--h height]" << std::endl;
}

int main(int argc, char** argv) {
    int width = 640;
    int height = 480;
    for (int i = 1; i < argc; ++i) {
        std::string a = argv[i];
        if (a == "--w" && i + 1 < argc) width = atoi(argv[++i]);
        if (a == "--h" && i + 1 < argc) height = atoi(argv[++i]);
    }

    D3D_FEATURE_LEVEL fl;
    ComPtr<ID3D11Device> device;
    ComPtr<ID3D11DeviceContext> ctx;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &device, &fl, &ctx);
    if (FAILED(hr)) {
        std::cerr << "D3D11CreateDevice failed: " << std::hex << hr << std::endl;
        return 1;
    }

    // Create NV12 textures Y and UV (single plane each as D3D11 does for NV12)
    D3D11_TEXTURE2D_DESC descY = {};
    descY.Width = width;
    descY.Height = height;
    descY.MipLevels = 1;
    descY.ArraySize = 1;
    descY.Format = DXGI_FORMAT_R8_UNORM; // for illustrative purposes; real NV12 uses special multi-plane
    descY.SampleDesc.Count = 1;
    descY.Usage = D3D11_USAGE_DEFAULT;
    descY.BindFlags = D3D11_BIND_SHADER_RESOURCE;
    descY.CPUAccessFlags = 0;
    descY.MiscFlags = D3D11_RESOURCE_MISC_SHARED;

    ComPtr<ID3D11Texture2D> texY;
    hr = device->CreateTexture2D(&descY, nullptr, &texY);
    if (FAILED(hr)) {
        std::cerr << "CreateTexture2D Y failed: " << std::hex << hr << std::endl;
        return 1;
    }

    // Create shared handle
    ComPtr<IDXGIResource> dxgiRes;
    hr = texY.As(&dxgiRes);
    if (FAILED(hr)) {
        std::cerr << "Query IDXGIResource failed: " << std::hex << hr << std::endl;
        return 1;
    }

    HANDLE sharedHandle = nullptr;
    hr = dxgiRes->CreateSharedHandle(nullptr, DXGI_SHARED_RESOURCE_READ | DXGI_SHARED_RESOURCE_WRITE, nullptr, &sharedHandle);
    if (FAILED(hr)) {
        std::cerr << "CreateSharedHandle failed: " << std::hex << hr << std::endl;
        return 1;
    }

    std::cout << "Shared handle: 0x" << std::hex << reinterpret_cast<uint64_t>(sharedHandle) << std::endl;

    // Fill with pattern
    D3D11_MAPPED_SUBRESOURCE mapped;
    ComPtr<ID3D11Texture2D> staging;
    D3D11_TEXTURE2D_DESC stDesc = descY; stDesc.Usage = D3D11_USAGE_STAGING; stDesc.BindFlags = 0; stDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE; stDesc.MiscFlags = 0;
    hr = device->CreateTexture2D(&stDesc, nullptr, &staging);
    if (FAILED(hr)) {
        std::cerr << "Create staging failed: " << std::hex << hr << std::endl;
    } else {
        hr = ctx->Map(staging.Get(), 0, D3D11_MAP_WRITE, 0, &mapped);
        if (SUCCEEDED(hr)) {
            for (int y = 0; y < height; ++y) {
                uint8_t* row = reinterpret_cast<uint8_t*>(mapped.pData) + y * mapped.RowPitch;
                for (int x = 0; x < width; ++x) {
                    row[x] = static_cast<uint8_t>((x + y) & 0xFF);
                }
            }
            ctx->Unmap(staging.Get(), 0);
            ctx->CopyResource(texY.Get(), staging.Get());
            ctx->Flush();
        }
    }

    // Keep process alive so consumer can open handle
    std::cout << "Producer running. Press Enter to exit." << std::endl;
    std::cin.get();

    CloseHandle(sharedHandle);
    return 0;
}
#else
int main() { return 0; }
#endif
