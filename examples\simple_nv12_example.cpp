/**
 * @file simple_nv12_example.cpp
 * @brief Simple example showing basic NV12 capture usage
 * 
 * This is a minimal example that demonstrates:
 * - Creating a DXGI capturer
 * - Performing NV12 ZeroCopy capture
 * - Basic error handling
 */

#include <capscr/capture.hpp>
#include <iostream>

int main() {
    std::cout << "Simple NV12 Capture Example" << std::endl;
    std::cout << "============================" << std::endl;

    // Create capturer
    auto capturer = capscr::createDxgiCapturer();
    if (!capturer) {
        std::cerr << "Failed to create capturer" << std::endl;
        return 1;
    }

    // Initialize for full screen capture
    auto result = capturer->init(capscr::CaptureTargetType::FullScreen);
    if (result != capscr::Result::Ok) {
        std::cerr << "Failed to initialize capturer: " << static_cast<int>(result) << std::endl;
        return 1;
    }

    // Check if GPU capture is available
    if (!capturer->isGpuCaptureAvailable()) {
        std::cerr << "GPU capture not available" << std::endl;
        return 1;
    }

    std::cout << "Capturer initialized successfully" << std::endl;

    // Perform NV12 capture
    capscr::ZeroCopyFrame frame;
    capscr::ZeroCopyOptions opts;
    opts.prefer_nv12 = true;

    result = capturer->captureZeroCopy(frame, opts);
    if (result != capscr::Result::Ok) {
        std::cerr << "NV12 capture failed: " << static_cast<int>(result) << std::endl;
        return 1;
    }

    // Check results
    std::cout << "NV12 capture successful!" << std::endl;
    std::cout << "Resolution: " << frame.width << "x" << frame.height << std::endl;
    std::cout << "Format: " << (frame.format == capscr::PixelFormat::NV12 ? "NV12" : "Other") << std::endl;
    std::cout << "Is NV12: " << (frame.is_nv12() ? "Yes" : "No") << std::endl;

#ifdef _WIN32
    if (frame.is_nv12()) {
        std::cout << "Y texture valid: " << (frame.d3d11_texture != nullptr ? "Yes" : "No") << std::endl;
        std::cout << "UV texture valid: " << (frame.d3d11_texture_uv != nullptr ? "Yes" : "No") << std::endl;
    }
#endif

    std::cout << "Example completed successfully!" << std::endl;
    return 0;
}
