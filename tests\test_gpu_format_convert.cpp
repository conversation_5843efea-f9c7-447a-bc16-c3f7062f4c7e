#include <gtest/gtest.h>
#include "capscr/capture.hpp"
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include <memory>

using namespace capscr;

TEST(GPUFormatConvertTest, ConvertOnGpuIfAvailable) {
    auto capturer = createDXGICapturer();
    ASSERT_NE(capturer, nullptr);

    // Initialize capturer to ensure DXGI device/duplication is created before checking availability
    Result initRes = capturer->init(CaptureTargetType::FullScreen, "", nullptr, "");
    if (initRes != Result::Ok) {
        GTEST_SKIP() << "DXGI capturer init failed or unsupported: " << static_cast<int>(initRes);
    }

    if (!capturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available after init";
    }

    // Try to capture processed texture via GPU (if supported)
    CaptureParams params;
    params.use_gpu_processing = true;
    params.output_width = 200;
    params.output_height = 150;
    params.output_format = PixelFormat::RGBA32;

    Frame out;
    Result res = capturer->capture(out, params);
    EXPECT_EQ(res, Result::Ok);
    EXPECT_EQ(out.width, 200);
    EXPECT_EQ(out.height, 150);
    EXPECT_EQ(out.format, PixelFormat::RGBA32);
}
