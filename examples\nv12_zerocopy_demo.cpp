/**
 * @file nv12_zerocopy_demo.cpp
 * @brief Complete demonstration of NV12 ZeroCopy functionality with shared handles
 * 
 * This example demonstrates:
 * 1. Basic NV12 ZeroCopy capture
 * 2. Dual texture access (Y + UV planes)
 * 3. Shared handle generation for encoder integration
 * 4. Cross-device texture sharing simulation
 * 5. Resource management and cleanup
 */

#include <capscr/capture.hpp>
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <wrl/client.h>
using Microsoft::WRL::ComPtr;
#endif

class NV12Demo {
private:
    std::unique_ptr<capscr::ICapturer> capturer_;
    bool initialized_ = false;

public:
    bool initialize() {
        std::cout << "=== NV12 ZeroCopy Demo ===" << std::endl;
        
        // Create DXGI capturer
        capturer_ = capscr::createDxgiCapturer();
        if (!capturer_) {
            std::cerr << "Error: Failed to create DXGI capturer" << std::endl;
            return false;
        }

        // Initialize for full screen capture
        auto result = capturer_->init(capscr::CaptureTargetType::FullScreen);
        if (result != capscr::Result::Ok) {
            std::cerr << "Error: Failed to initialize capturer: " << static_cast<int>(result) << std::endl;
            return false;
        }

        // Check GPU capture availability
        if (!capturer_->isGpuCaptureAvailable()) {
            std::cerr << "Error: GPU capture not available" << std::endl;
            return false;
        }

        std::cout << "✓ Capturer initialized successfully" << std::endl;
        initialized_ = true;
        return true;
    }

    void demonstrateBasicNV12() {
        if (!initialized_) return;

        std::cout << "\n--- Basic NV12 ZeroCopy Capture ---" << std::endl;

        capscr::ZeroCopyFrame frame;
        capscr::ZeroCopyOptions opts;
        opts.prefer_nv12 = true;

        auto result = capturer_->captureZeroCopy(frame, opts);
        if (result != capscr::Result::Ok) {
            std::cerr << "Error: NV12 capture failed: " << static_cast<int>(result) << std::endl;
            return;
        }

        std::cout << "✓ NV12 capture successful!" << std::endl;
        std::cout << "  Format: " << (frame.format == capscr::PixelFormat::NV12 ? "NV12" : "Other") << std::endl;
        std::cout << "  Resolution: " << frame.width << "x" << frame.height << std::endl;
        std::cout << "  Mode: " << (frame.mode == capscr::ZeroCopyFrame::Mode::LocalBorrow ? "LocalBorrow" : 
                                   frame.mode == capscr::ZeroCopyFrame::Mode::SharedHandle ? "SharedHandle" : "Unknown") << std::endl;

#ifdef _WIN32
        if (frame.is_nv12()) {
            std::cout << "  Y Texture: " << frame.d3d11_texture << std::endl;
            std::cout << "  UV Texture: " << frame.d3d11_texture_uv << std::endl;
            
            if (frame.mode == capscr::ZeroCopyFrame::Mode::SharedHandle) {
                std::cout << "  Y Handle: " << frame.shared_handle << std::endl;
                std::cout << "  UV Handle: " << frame.shared_handle_uv << std::endl;
            }
        }
#endif
    }

    void demonstrateTextureProperties() {
        if (!initialized_) return;

        std::cout << "\n--- NV12 Texture Properties ---" << std::endl;

        capscr::ZeroCopyFrame frame;
        capscr::ZeroCopyOptions opts;
        opts.prefer_nv12 = true;

        auto result = capturer_->captureZeroCopy(frame, opts);
        if (result != capscr::Result::Ok) {
            std::cerr << "Error: NV12 capture failed for properties demo" << std::endl;
            return;
        }

#ifdef _WIN32
        if (frame.is_nv12() && frame.d3d11_texture && frame.d3d11_texture_uv) {
            // Get Y plane properties
            D3D11_TEXTURE2D_DESC yDesc, uvDesc;
            frame.d3d11_texture->GetDesc(&yDesc);
            frame.d3d11_texture_uv->GetDesc(&uvDesc);

            std::cout << "Y Plane Properties:" << std::endl;
            std::cout << "  Format: " << yDesc.Format << " (expected: " << DXGI_FORMAT_R8_UNORM << ")" << std::endl;
            std::cout << "  Size: " << yDesc.Width << "x" << yDesc.Height << std::endl;
            std::cout << "  Bind Flags: 0x" << std::hex << yDesc.BindFlags << std::dec << std::endl;

            std::cout << "UV Plane Properties:" << std::endl;
            std::cout << "  Format: " << uvDesc.Format << " (expected: " << DXGI_FORMAT_R8G8_UNORM << ")" << std::endl;
            std::cout << "  Size: " << uvDesc.Width << "x" << uvDesc.Height << std::endl;
            std::cout << "  Bind Flags: 0x" << std::hex << uvDesc.BindFlags << std::dec << std::endl;

            // Verify correct NV12 dimensions
            bool correctDimensions = (uvDesc.Width == yDesc.Width / 2) && (uvDesc.Height == yDesc.Height / 2);
            std::cout << "✓ NV12 dimensions: " << (correctDimensions ? "Correct" : "Incorrect") << std::endl;
        }
#endif
    }

    void demonstrateSharedHandles() {
        if (!initialized_) return;

        std::cout << "\n--- Shared Handle Generation ---" << std::endl;

        capscr::ZeroCopyFrame frame;
        capscr::ZeroCopyOptions opts;
        opts.prefer_nv12 = true;
        opts.prefer_shared_handle = true;  // Request shared handles

        auto result = capturer_->captureZeroCopy(frame, opts);
        if (result != capscr::Result::Ok) {
            std::cerr << "Error: Shared handle capture failed: " << static_cast<int>(result) << std::endl;
            return;
        }

#ifdef _WIN32
        if (frame.mode == capscr::ZeroCopyFrame::Mode::SharedHandle && frame.is_nv12()) {
            std::cout << "✓ Shared handles generated successfully!" << std::endl;
            std::cout << "  Y Handle: 0x" << std::hex << reinterpret_cast<uintptr_t>(frame.shared_handle) << std::dec << std::endl;
            std::cout << "  UV Handle: 0x" << std::hex << reinterpret_cast<uintptr_t>(frame.shared_handle_uv) << std::dec << std::endl;

            // Simulate encoder access
            simulateEncoderAccess(frame.shared_handle, frame.shared_handle_uv, frame.width, frame.height);
        } else {
            std::cout << "⚠ Shared handles not available or NV12 not used" << std::endl;
        }
#endif
    }

#ifdef _WIN32
    void simulateEncoderAccess(HANDLE yHandle, HANDLE uvHandle, int width, int height) {
        std::cout << "\n--- Encoder Simulation ---" << std::endl;

        // Create a separate D3D11 device (simulating encoder's device)
        ComPtr<ID3D11Device> encoderDevice;
        ComPtr<ID3D11DeviceContext> encoderContext;
        
        D3D_FEATURE_LEVEL featureLevel;
        HRESULT hr = D3D11CreateDevice(
            nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr,
            0, nullptr, 0, D3D11_SDK_VERSION,
            &encoderDevice, &featureLevel, &encoderContext
        );

        if (FAILED(hr)) {
            std::cerr << "Error: Failed to create encoder device: 0x" << std::hex << hr << std::dec << std::endl;
            return;
        }

        // Open shared textures
        ComPtr<ID3D11Texture2D> yTexture, uvTexture;
        
        hr = encoderDevice->OpenSharedResource(yHandle, IID_PPV_ARGS(&yTexture));
        if (FAILED(hr)) {
            std::cerr << "Error: Failed to open Y shared texture: 0x" << std::hex << hr << std::dec << std::endl;
            return;
        }

        hr = encoderDevice->OpenSharedResource(uvHandle, IID_PPV_ARGS(&uvTexture));
        if (FAILED(hr)) {
            std::cerr << "Error: Failed to open UV shared texture: 0x" << std::hex << hr << std::dec << std::endl;
            return;
        }

        std::cout << "✓ Encoder successfully opened shared textures!" << std::endl;
        std::cout << "  Y Texture: " << yTexture.Get() << std::endl;
        std::cout << "  UV Texture: " << uvTexture.Get() << std::endl;

        // Verify texture properties
        D3D11_TEXTURE2D_DESC yDesc, uvDesc;
        yTexture->GetDesc(&yDesc);
        uvTexture->GetDesc(&uvDesc);

        std::cout << "Encoder-side verification:" << std::endl;
        std::cout << "  Y: " << yDesc.Width << "x" << yDesc.Height << " (Format: " << yDesc.Format << ")" << std::endl;
        std::cout << "  UV: " << uvDesc.Width << "x" << uvDesc.Height << " (Format: " << uvDesc.Format << ")" << std::endl;

        // Simulate some encoder operations
        std::cout << "✓ Encoder simulation completed successfully!" << std::endl;
    }
#endif

    void demonstratePerformance() {
        if (!initialized_) return;

        std::cout << "\n--- Performance Test ---" << std::endl;

        const int numCaptures = 10;
        capscr::ZeroCopyOptions opts;
        opts.prefer_nv12 = true;

        auto startTime = std::chrono::high_resolution_clock::now();

        int successCount = 0;
        for (int i = 0; i < numCaptures; ++i) {
            capscr::ZeroCopyFrame frame;
            auto result = capturer_->captureZeroCopy(frame, opts);
            if (result == capscr::Result::Ok) {
                successCount++;
            }
            
            // Small delay to avoid overwhelming the system
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        std::cout << "Performance Results:" << std::endl;
        std::cout << "  Captures: " << successCount << "/" << numCaptures << std::endl;
        std::cout << "  Total time: " << duration.count() << "ms" << std::endl;
        std::cout << "  Average: " << (duration.count() / (float)numCaptures) << "ms per capture" << std::endl;
        std::cout << "  Success rate: " << (successCount * 100.0f / numCaptures) << "%" << std::endl;
    }

    void demonstrateResourceManagement() {
        if (!initialized_) return;

        std::cout << "\n--- Resource Management Test ---" << std::endl;

        // Create and immediately release frames to test resource management
        for (int i = 0; i < 5; ++i) {
            capscr::ZeroCopyFrame frame;
            capscr::ZeroCopyOptions opts;
            opts.prefer_nv12 = true;

            auto result = capturer_->captureZeroCopy(frame, opts);
            if (result == capscr::Result::Ok) {
                std::cout << "  Frame " << (i + 1) << ": Created and released" << std::endl;
            }
            // Frame automatically released when going out of scope
        }

        std::cout << "✓ Resource management test completed" << std::endl;
    }

    void runFullDemo() {
        if (!initialize()) {
            return;
        }

        demonstrateBasicNV12();
        demonstrateTextureProperties();
        demonstrateSharedHandles();
        demonstratePerformance();
        demonstrateResourceManagement();

        std::cout << "\n=== Demo Completed ===" << std::endl;
    }
};

int main() {
    std::cout << "NV12 ZeroCopy Demonstration Program" << std::endl;
    std::cout << "====================================" << std::endl;

    try {
        NV12Demo demo;
        demo.runFullDemo();
    }
    catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }

    std::cout << "\nPress Enter to exit..." << std::endl;
    std::cin.get();
    return 0;
}
