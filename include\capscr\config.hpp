#pragma once

#include <string>
#include <memory>
#include <map>
#include <vector>

namespace capscr {
namespace config {

enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    CRITICAL = 5,
    OFF = 6
};

struct LogConfig {
    LogLevel level = LogLevel::INFO;
    bool console_enabled = true;
    bool file_enabled = true;
    std::string file_path = "capscr.log";
    size_t max_file_size = 5 * 1024 * 1024; // 5MB
    size_t max_files = 3;
    std::string pattern = "[%Y-%m-%d %H:%M:%S.%e] [%t] [%l] %v";
    bool auto_flush = true;
    
    // Module-specific log levels
    std::map<std::string, LogLevel> module_levels;
};

struct CaptureConfig {
    bool gpu_texture_enabled = true;
    bool performance_logging = true;
    int capture_timeout_ms = 1000;
    std::string preferred_adapter; // Empty for auto-select
};

struct AppConfig {
    LogConfig logging;
    CaptureConfig capture;
    
    // Configuration file metadata
    std::string config_file_path;
    bool auto_reload = false;
    int reload_interval_ms = 5000;
};

class IConfigProvider {
public:
    virtual ~IConfigProvider() = default;
    virtual bool load(const std::string& file_path, AppConfig& config) = 0;
    virtual bool save(const std::string& file_path, const AppConfig& config) = 0;
    virtual std::string get_format_name() const = 0;
};

class JsonConfigProvider : public IConfigProvider {
public:
    bool load(const std::string& file_path, AppConfig& config) override;
    bool save(const std::string& file_path, const AppConfig& config) override;
    std::string get_format_name() const override { return "JSON"; }
};

class ConfigManager {
public:
    static ConfigManager& instance();
    
    // Load configuration from file
    bool load_config(const std::string& file_path = "");
    
    // Save current configuration to file
    bool save_config(const std::string& file_path = "");
    
    // Get current configuration
    const AppConfig& get_config() const { return config_; }
    AppConfig& get_mutable_config() { return config_; }
    
    // Environment variable overrides
    void apply_env_overrides();
    
    // Create default configuration file
    bool create_default_config(const std::string& file_path);
    
    // Auto-reload functionality
    void enable_auto_reload(bool enable = true);
    void check_for_reload();
    
private:
    ConfigManager() = default;
    std::unique_ptr<IConfigProvider> get_provider(const std::string& file_path);
    void apply_log_level_override(const std::string& env_value);
    void apply_log_file_override(const std::string& env_value);
    
    AppConfig config_;
    std::unique_ptr<IConfigProvider> provider_;
    std::string current_file_path_;
    long long last_modified_time_ = 0;
};

// Utility functions
LogLevel parse_log_level(const std::string& level_str);
std::string log_level_to_string(LogLevel level);

// Environment variable names
constexpr const char* ENV_LOG_LEVEL = "CAPSCR_LOG_LEVEL";
constexpr const char* ENV_LOG_FILE = "CAPSCR_LOG_FILE";
constexpr const char* ENV_CONFIG_FILE = "CAPSCR_CONFIG_FILE";
constexpr const char* ENV_GPU_ENABLED = "CAPSCR_GPU_ENABLED";

} // namespace config
} // namespace capscr
