#include "test_window_helper.hpp"
#include <iostream>
#include <cmath>

const char* TestWindow::WINDOW_CLASS_NAME = "CapscrTestWindow";
bool TestWindow::classRegistered_ = false;

TestWindow::TestWindow() = default;

TestWindow::~TestWindow() {
    Destroy();
}

bool TestWindow::Create(const TestPattern& pattern) {
    pattern_ = pattern;
    
    // Register window class if not already done
    if (!classRegistered_) {
        WNDCLASSEX wc = {};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = CreateSolidBrush(pattern_.backgroundColor);
        wc.lpszClassName = WINDOW_CLASS_NAME;
        
        if (!RegisterClassEx(&wc)) {
            std::cerr << "Failed to register test window class" << std::endl;
            return false;
        }
        classRegistered_ = true;
    }
    
    // Calculate window size including borders
    RECT rect = {0, 0, pattern_.width, pattern_.height};
    AdjustWindowRect(&rect, WS_OVERLAPPEDWINDOW, FALSE);
    int windowWidth = rect.right - rect.left;
    int windowHeight = rect.bottom - rect.top;
    
    // Create window at a predictable position
    hwnd_ = CreateWindowEx(
        0,
        WINDOW_CLASS_NAME,
        "Test Capture Window",
        WS_OVERLAPPEDWINDOW,
        100, 100, // Fixed position for testing
        windowWidth, windowHeight,
        nullptr, nullptr,
        GetModuleHandle(nullptr),
        this // Pass this pointer for WindowProc
    );
    
    if (!hwnd_) {
        std::cerr << "Failed to create test window" << std::endl;
        return false;
    }
    
    ShowWindow(hwnd_, SW_SHOW);
    UpdateWindow(hwnd_);
    
    // Give window time to be fully painted
    Sleep(100);
    
    created_ = true;
    return true;
}

void TestWindow::Destroy() {
    if (hwnd_) {
        DestroyWindow(hwnd_);
        hwnd_ = nullptr;
    }
    created_ = false;
}

void TestWindow::Repaint() {
    if (hwnd_) {
        InvalidateRect(hwnd_, nullptr, TRUE);
        UpdateWindow(hwnd_);
        Sleep(50); // Give time for repaint
    }
}

void TestWindow::MoveTo(int x, int y) {
    if (hwnd_) {
        SetWindowPos(hwnd_, nullptr, x, y, 0, 0, SWP_NOSIZE | SWP_NOZORDER);
        Sleep(50); // Give time for move
    }
}

void TestWindow::Resize(int width, int height) {
    if (hwnd_) {
        pattern_.width = width;
        pattern_.height = height;
        
        RECT rect = {0, 0, width, height};
        AdjustWindowRect(&rect, WS_OVERLAPPEDWINDOW, FALSE);
        SetWindowPos(hwnd_, nullptr, 0, 0, rect.right - rect.left, rect.bottom - rect.top, 
                    SWP_NOMOVE | SWP_NOZORDER);
        Sleep(50); // Give time for resize
    }
}

LRESULT CALLBACK TestWindow::WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    TestWindow* window = nullptr;
    
    if (msg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        window = static_cast<TestWindow*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(window));
    } else {
        window = reinterpret_cast<TestWindow*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    switch (msg) {
        case WM_PAINT:
            if (window) {
                PAINTSTRUCT ps;
                HDC hdc = BeginPaint(hwnd, &ps);
                window->OnPaint(hdc);
                EndPaint(hwnd, &ps);
                return 0;
            }
            break;
            
        case WM_CLOSE:
            // Don't allow user to close test window during tests
            return 0;
            
        case WM_DESTROY:
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    
    return DefWindowProc(hwnd, msg, wParam, lParam);
}

void TestWindow::OnPaint(HDC hdc) {
    // Fill background
    RECT clientRect;
    GetClientRect(hwnd_, &clientRect);
    HBRUSH bgBrush = CreateSolidBrush(pattern_.backgroundColor);
    FillRect(hdc, &clientRect, bgBrush);
    DeleteObject(bgBrush);
    
    DrawTestPattern(hdc);
}

void TestWindow::DrawTestPattern(HDC hdc) {
    // Draw test rectangle
    HBRUSH rectBrush = CreateSolidBrush(pattern_.testRect.color);
    RECT rect = {
        pattern_.testRect.x,
        pattern_.testRect.y,
        pattern_.testRect.x + pattern_.testRect.width,
        pattern_.testRect.y + pattern_.testRect.height
    };
    FillRect(hdc, &rect, rectBrush);
    DeleteObject(rectBrush);
    
    // Draw test circle (actually ellipse)
    HBRUSH circleBrush = CreateSolidBrush(pattern_.testCircle.color);
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, circleBrush);
    
    Ellipse(hdc, 
            pattern_.testCircle.x,
            pattern_.testCircle.y,
            pattern_.testCircle.x + pattern_.testCircle.width,
            pattern_.testCircle.y + pattern_.testCircle.height);
    
    SelectObject(hdc, oldBrush);
    DeleteObject(circleBrush);
    
    // Draw text
    SetTextColor(hdc, pattern_.textColor);
    SetBkMode(hdc, TRANSPARENT);
    
    RECT textRect = {10, 10, pattern_.width - 10, 40};
    DrawTextA(hdc, pattern_.text.c_str(), -1, &textRect, DT_LEFT | DT_TOP);
}

bool TestWindow::VerifyPixel(const uint8_t* frameData, int frameWidth, int frameStride,
                            int x, int y, COLORREF expectedColor, int tolerance) {
    if (x < 0 || x >= frameWidth || y < 0) {
        return false;
    }
    
    // Assuming BGRA format (4 bytes per pixel)
    const uint8_t* pixel = frameData + y * frameStride + x * 4;
    
    uint8_t b = pixel[0];
    uint8_t g = pixel[1];
    uint8_t r = pixel[2];
    // alpha = pixel[3] (not used for color comparison)
    
    uint8_t expectedR = GetRValue(expectedColor);
    uint8_t expectedG = GetGValue(expectedColor);
    uint8_t expectedB = GetBValue(expectedColor);
    
    int diffR = abs(r - expectedR);
    int diffG = abs(g - expectedG);
    int diffB = abs(b - expectedB);
    
    return (diffR <= tolerance && diffG <= tolerance && diffB <= tolerance);
}

bool TestWindow::VerifyRect(const uint8_t* frameData, int frameWidth, int frameStride,
                           int rectX, int rectY, int rectW, int rectH,
                           COLORREF expectedColor, int tolerance, float matchThreshold) {
    int totalPixels = 0;
    int matchingPixels = 0;
    
    for (int y = rectY; y < rectY + rectH; ++y) {
        for (int x = rectX; x < rectX + rectW; ++x) {
            if (x >= 0 && x < frameWidth) {
                totalPixels++;
                if (VerifyPixel(frameData, frameWidth, frameStride, x, y, expectedColor, tolerance)) {
                    matchingPixels++;
                }
            }
        }
    }
    
    if (totalPixels == 0) return false;
    
    float matchRatio = static_cast<float>(matchingPixels) / totalPixels;
    return matchRatio >= matchThreshold;
}
