#ifdef _WIN32
#include <windows.h>
#include <iostream>
#include <d3d11.h>
#include <wrl.h>

using Microsoft::WRL::ComPtr;

int main() {
    D3D_FEATURE_LEVEL fl;
    ComPtr<ID3D11Device> device;
    ComPtr<ID3D11DeviceContext> ctx;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &device, &fl, &ctx);
    if (FAILED(hr)) {
        std::cerr << "D3D11CreateDevice failed: " << std::hex << hr << std::endl;
        return 1;
    }

    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = 16; desc.Height = 16; desc.MipLevels = 1; desc.ArraySize = 1; desc.Format = DXGI_FORMAT_R8G8B8A8_UNORM; desc.SampleDesc.Count = 1; desc.Usage = D3D11_USAGE_DEFAULT; desc.BindFlags = D3D11_BIND_SHADER_RESOURCE; desc.MiscFlags = D3D11_RESOURCE_MISC_SHARED;

    ComPtr<ID3D11Texture2D> tex;
    hr = device->CreateTexture2D(&desc, nullptr, &tex);
    if (FAILED(hr)) { std::cerr << "CreateTexture2D failed: " << std::hex << hr << std::endl; return 1; }

    ComPtr<IDXGIResource> dxgiRes;
    hr = tex.As(&dxgiRes);
    if (FAILED(hr)) { std::cerr << "As IDXGIResource failed: " << std::hex << hr << std::endl; return 1; }

    HANDLE h = nullptr;
    hr = dxgiRes->CreateSharedHandle(nullptr, DXGI_SHARED_RESOURCE_READ | DXGI_SHARED_RESOURCE_WRITE, nullptr, &h);
    if (FAILED(hr)) { std::cerr << "CreateSharedHandle failed: " << std::hex << hr << std::endl; return 1; }

    std::cout << "Shared handle: 0x" << std::hex << reinterpret_cast<uint64_t>(h) << std::endl;
    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();
    CloseHandle(h);
    return 0;
}
#else
int main() { return 0; }
#endif
