cmake_minimum_required(VERSION 3.15)
project(capscr_ui_demo)

set(CMAKE_CXX_STANDARD 17)

# Use FetchContent to download ImGui and GLFW automatically
include(FetchContent)

FetchContent_Declare(
  glfw
  GIT_REPOSITORY https://github.com/glfw/glfw.git
  GIT_TAG        3.3.8
)
FetchContent_Declare(
  imgui
  GIT_REPOSITORY https://github.com/ocornut/imgui.git
  GIT_TAG        v1.89.8
)

FetchContent_MakeAvailable(glfw imgui)

include(FetchContent)
FetchContent_Declare(
    spdlog
    GIT_REPOSITORY https://github.com/gabime/spdlog.git
    GIT_TAG v1.11.0
)

# Create the UI demo executable
add_executable(capscr_ui_demo main.cpp)

# Link GLFW
target_link_libraries(capscr_ui_demo PRIVATE glfw)

# Add ImGui sources
target_sources(capscr_ui_demo PRIVATE
    ${imgui_SOURCE_DIR}/imgui.cpp
    ${imgui_SOURCE_DIR}/imgui_demo.cpp
    ${imgui_SOURCE_DIR}/imgui_draw.cpp
    ${imgui_SOURCE_DIR}/imgui_tables.cpp
    ${imgui_SOURCE_DIR}/imgui_widgets.cpp
    ${imgui_SOURCE_DIR}/backends/imgui_impl_glfw.cpp
)

# Platform-specific setup
if (WIN32)
    # Use D3D11 backend on Windows
    target_sources(capscr_ui_demo PRIVATE
        ${imgui_SOURCE_DIR}/backends/imgui_impl_dx11.cpp
    )
    target_link_libraries(capscr_ui_demo PRIVATE d3d11 dxgi)
    target_compile_definitions(capscr_ui_demo PRIVATE "_CRT_SECURE_NO_WARNINGS")
else()
    # Use OpenGL backend on other platforms
    find_package(OpenGL REQUIRED)
    target_sources(capscr_ui_demo PRIVATE
        ${imgui_SOURCE_DIR}/backends/imgui_impl_opengl3.cpp
    )
    target_link_libraries(capscr_ui_demo PRIVATE ${OPENGL_gl_LIBRARY})
endif()

# Include directories
target_include_directories(capscr_ui_demo PRIVATE 
    ${imgui_SOURCE_DIR} 
    ${imgui_SOURCE_DIR}/backends
)

# Try to find capscr libraries (optional)
find_path(CAPSCR_INCLUDE_DIR NAMES capscr/capture.hpp HINTS ${CMAKE_SOURCE_DIR}/../include ${CMAKE_SOURCE_DIR}/../)
find_library(CAPSCR_WIN_LIB NAMES capscr_win HINTS ${CMAKE_SOURCE_DIR}/../build ${CMAKE_SOURCE_DIR}/../build/Release)
find_library(CAPSCR_COMMON_LIB NAMES capscr_common HINTS ${CMAKE_SOURCE_DIR}/../build ${CMAKE_SOURCE_DIR}/../build/Release)
if (CAPSCR_INCLUDE_DIR)
    target_include_directories(capscr_ui_demo PRIVATE ${CAPSCR_INCLUDE_DIR})
    # capscr uses spdlog - try to find it quietly; if not present, try to fetch it
    find_package(spdlog QUIET CONFIG)
    if (NOT spdlog_FOUND)
        message(STATUS "spdlog not found - attempting to fetch spdlog via FetchContent")
        FetchContent_MakeAvailable(spdlog)
        find_package(spdlog QUIET CONFIG)
    endif()

    # Prefer linking using spdlog target if FetchContent provided it
    if (CAPSCR_WIN_LIB AND (spdlog_FOUND OR TARGET spdlog::spdlog))
        # Link capscr library
        target_link_libraries(capscr_ui_demo PRIVATE ${CAPSCR_WIN_LIB})
        if (CAPSCR_COMMON_LIB)
            target_link_libraries(capscr_ui_demo PRIVATE ${CAPSCR_COMMON_LIB})
        endif()

        # Link spdlog either via imported target or the one provided by FetchContent
        if (TARGET spdlog::spdlog)
            target_link_libraries(capscr_ui_demo PRIVATE spdlog::spdlog)
        else()
            target_link_libraries(capscr_ui_demo PRIVATE spdlog::spdlog)
        endif()

        target_compile_definitions(capscr_ui_demo PRIVATE HAVE_CAPSCR=1)
        message(STATUS "capscr and spdlog found - enabling HAVE_CAPSCR")
    else()
        message(STATUS "capscr libs found but spdlog not available; demo will run in simulated mode")
    endif()
else()
    message(STATUS "capscr headers not found; demo will run in simulated mode")
endif()
