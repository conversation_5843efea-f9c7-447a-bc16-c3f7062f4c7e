// 简单测试NV12双句柄支持
#include "include/capscr/capture.hpp"
#include "src/windows/dxgi_capture.hpp"
#include <iostream>

using namespace capscr;

int main() {
    std::cout << "测试NV12双句柄支持..." << std::endl;
    
    // 创建DXGICapture实例 (不是DXGICapturer)
    DXGICapture capture;
    
    // 初始化
    Result result = capture.initialize(CaptureTargetType::FullScreen, "", nullptr, "");
    if (result != Result::Ok) {
        std::cout << "初始化失败: " << static_cast<int>(result) << std::endl;
        return 1;
    }
    
    std::cout << "DXGICapture初始化成功" << std::endl;
    
    // 测试NV12 ZeroCopy
    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    ZeroCopyFrame frame;
    result = capture.captureZeroCopy(frame, opts);
    
    if (result == Result::Ok) {
        std::cout << "NV12 ZeroCopy成功!" << std::endl;
        std::cout << "格式: " << static_cast<int>(frame.format) << std::endl;
        std::cout << "是否为NV12: " << (frame.is_nv12() ? "是" : "否") << std::endl;
        std::cout << "Y纹理: " << (frame.d3d11_texture ? "有效" : "无效") << std::endl;
        std::cout << "UV纹理: " << (frame.d3d11_texture_uv ? "有效" : "无效") << std::endl;
    } else {
        std::cout << "NV12 ZeroCopy失败: " << static_cast<int>(result) << std::endl;
    }
    
    return 0;
}
