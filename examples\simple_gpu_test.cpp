#include <iostream>
#include <memory>
#include "capscr/capture.hpp"
#ifdef _WIN32
#include "capscr/platform/windows/capturer_dxgi.hpp"
#endif

using namespace capscr;

int main() {
    std::cout << "=== Simple GPU Texture Test ===" << std::endl;
    
    // Create DXGI capturer using the same method as example_capture.cpp
    auto capturer = createBestCapturer();
    
#ifdef _WIN32
    // Try to get DXGI specifically
    try {
        auto dx = capscr::createDXGICapturer();
        if (dx) {
            capturer = std::move(dx);
            std::cout << "Using DXGI capturer" << std::endl;
        } else {
            std::cout << "DXGI not available, using default capturer" << std::endl;
        }
    } catch(...) { 
        std::cout << "Failed to create DXGI capturer, using default" << std::endl; 
    }
#endif

    if (!capturer) {
        std::cout << "FAILED: Could not create any capturer" << std::endl;
        return 1;
    }
    
    std::cout << "Capturer created successfully" << std::endl;
    
    // Initialize for full screen capture
    auto result = capturer->init(CaptureTargetType::FullScreen, "", nullptr, "");
    if (result != Result::Ok) {
        std::cout << "FAILED: Could not initialize capturer (result=" << static_cast<int>(result) << ")" << std::endl;
        return 1;
    }
    
    std::cout << "Capturer initialized successfully" << std::endl;
    
    // Check GPU capture capability
    bool gpuAvailable = capturer->isGpuCaptureAvailable();
    std::cout << "GPU capture available: " << (gpuAvailable ? "YES" : "NO") << std::endl;
    
    if (gpuAvailable) {
        std::cout << "Attempting GPU texture capture..." << std::endl;
        
        GpuTexture gpuTexture;
        auto gpuResult = capturer->captureGpu(gpuTexture);
        
        if (gpuResult == Result::Ok) {
            std::cout << "SUCCESS: GPU texture captured!" << std::endl;
            std::cout << "  Texture size: " << gpuTexture.width << "x" << gpuTexture.height << std::endl;
            std::cout << "  D3D11 Device: " << (gpuTexture.d3d11_device ? "Valid" : "NULL") << std::endl;
            std::cout << "  D3D11 Context: " << (gpuTexture.d3d11_context ? "Valid" : "NULL") << std::endl;
            std::cout << "  D3D11 Texture: " << (gpuTexture.d3d11_texture ? "Valid" : "NULL") << std::endl;
            std::cout << "  Is Valid: " << (gpuTexture.is_valid() ? "YES" : "NO") << std::endl;
            
            // This demonstrates zero-copy GPU texture access
            std::cout << "  ** Zero-copy texture ready for hardware encoding! **" << std::endl;
            
        } else {
            std::cout << "GPU texture capture result: " << static_cast<int>(gpuResult) << std::endl;
            std::cout << "(This may be normal if no screen updates)" << std::endl;
        }
    } else {
        std::cout << "GPU capture not available" << std::endl;
    }
    
    // Test traditional capture for comparison
    std::cout << "Attempting traditional frame capture..." << std::endl;
    Frame frame;
    auto frameResult = capturer->capture(frame);
    if (frameResult == Result::Ok) {
        std::cout << "SUCCESS: Traditional frame captured!" << std::endl;
        std::cout << "  Frame size: " << frame.width << "x" << frame.height << std::endl;
        std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    } else {
        std::cout << "Traditional frame capture result: " << static_cast<int>(frameResult) << std::endl;
    }
    
    std::cout << "=== Test completed ===" << std::endl;
    return 0;
}
