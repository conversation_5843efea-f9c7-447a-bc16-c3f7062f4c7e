5433210: 分析一下dxgi模式下，要做到zerocopy，對於d3ddevice要有什麼要求

GitHub Copilot: 我将先创建一个可跟踪的待办清单并把第一项标记为正在进行，然后开始分析 DXGI zero-copy 对 D3D 设备的要求（预期输出：要点清单、实现细节、边界情况与建议）。接下来我会写出第一个待办项并开始分析。 我将并行搜索仓库中与“共享资源 / keyed mutex / shared handle”相关的符号，以便参考已有实现并避免误报；预期结果：找到示例或点到具体API名称。 接收任务：我将分析在 DXGI 模式下实现 zero-copy 时，对 D3D 设备（D3D11 / D3D12）需要满足的具体要求，并给出实现要点、同步/共享建议和常见边界情况。下面按要点列出并给出短建议。

## 需求清单（todo 覆盖）
- 列出必需的设备/资源创建标志与能力 —— Done  
- 给出关键 API 与同步/共享 模式示例（D3D11 / D3D12） —— Done  
- 总结兼容性、边界情况与工程性建议 —— Done

## 合同（简短）
- 输入：在同一台机器上，使用 DXGI 进行屏幕/帧采集并把 GPU 纹理零拷贝给下游（编码器/其他进程/另一个 D3D 设备）。  
- 输出：可实现零拷贝的资源创建与共享策略、同步方法、成功/失败的判定标准。  
- 成功条件：生产者和消费者运行在同一物理 GPU（同一适配器 LUID），资源以共享句柄或 keyed-mutex/共享 fence 的形式创建并能在消费者端直接打开并使用，无 CPU 端读回或显式复制。  
- 常见失败模式：设备位于不同物理 GPU、创建资源不含共享标志、错误的格式（需编码器支持）、不同 Windows/驱动/Feature Level 不支持所选分享方式。

## 关键要求（精简要点）
1. 适配器一致性
   - 为了避免隐式 PCIe / 系统内拷贝，生产者（创建纹理）和消费者（消费纹理/编码器）应位于同一物理 GPU（同一 adapter/LUID）。跨 adapter 会触发额外拷贝，破坏 zero-copy。

2. 设备能力与 Feature Level
   - 最低建议 D3D feature level 为 11_0（多数共享功能在 11+ 可用）。D3D12 则需合适的 WDDM 驱动版本以支持共享句柄/fence。
   - 如果需要 Direct2D / MF 的 BGRA 支持，创建 D3D11 设备时加 `D3D11_CREATE_DEVICE_BGRA_SUPPORT`。

3. 资源创建必须带共享标志
   - D3D11:
     - 对应 `D3D11_TEXTURE2D_DESC.MiscFlags`：使用 `D3D11_RESOURCE_MISC_SHARED`（早期）、或更推荐的 `D3D11_RESOURCE_MISC_SHARED_NTHANDLE`（NT handle，跨进程更可靠），以及当需要 keyed mutex 时 `D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX`（用于 IDXGIKeyedMutex）。
     - BindFlags 通常包含 `D3D11_BIND_SHADER_RESOURCE` 或 `D3D11_BIND_RENDER_TARGET`，根据使用场景选。
   - D3D12:
     - 通过 `CreateCommittedResource` 创建资源，然后用 `ID3D12Device::CreateSharedHandle` 来生成可跨进程/设备的 HANDLE。也可使用 `IDXGIResource1::CreateSharedHandle`（兼容层）或使用共享堆（视场景而定）。

4. 格式与像素排列
   - 为避免色彩/像素格式拷贝，优先使用下游（例如硬件编码器）本身支持的 GPU 原生格式：
     - 对视频编码器常用：`DXGI_FORMAT_NV12`（若编码器支持），可避免从 RGBA -> NV12 的 GPU/CPU 转换。
     - 桌面复制通常产出 `DXGI_FORMAT_B8G8R8A8_UNORM`（BGRA），如果编码器不能直接消费，至少要在 GPU 上做格式转换并在 GPU 内完成（避免 Map/Readback）。

5. 同步机制（避免 CPU stall）
   - D3D11 keyed mutex: 当资源创建时设置 `D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX`，在不同 D3D11 设备间使用 `IDXGIKeyedMutex::AcquireSync` / `ReleaseSync` 同步。
   - NT-handle + keyed-mutex: 推荐用于跨进程，因为它能生成可通过名字传递的 HANDLE。
   - D3D12 同步：使用 `ID3D12Fence` 并创建共享 fence（CreateSharedHandle / OpenSharedHandle），消费者等待 fence 值（Signal/Wait），这在 D3D12 间同步比 keyed-mutex 更通用。
   - 切勿用 CPU blocking Map/Unmap 进行同步（会导致读回）。

6. 共享句柄 vs IDXGIKeyedMutex 的选择
   - 单进程内不同设备或同一进程内跨 API（D3D11 -> D3D12）：
     - 可以使用共享句柄 + 映射到目标 API 的资源打开（OpenSharedResource / OpenSharedHandle）。注意 D3D12 和 D3D11 之间需要合适的桥接。
   - 同步粒度、性能与 API 兼容性决定使用 keyed-mutex（直接在纹理上锁）或 fence（更灵活，适合 D3D12）。

7. 权限 / NT Handle 注意
   - 使用 `CreateSharedHandle` 时考虑权限（GENERIC_ALL 等）及进程间传递方式（DuplicateHandle / name-based Open）。Windows 8+ 推荐使用 NT handle（`_NTHANDLE`）方式。

## 简短 API 流程（概念性，注意错误检查/权限）
- D3D11 生产者（创建共享纹理）
  - 填 `D3D11_TEXTURE2D_DESC`：Format、Width/Height、BindFlags、CPUAccessFlags=0、Usage=D3D11_USAGE_DEFAULT
  - MiscFlags |= `D3D11_RESOURCE_MISC_SHARED_NTHANDLE` 或 `D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX`
  - device->CreateTexture2D(...)
  - 获取 `IDXGIResource` / `IDXGIResource1`，调用 `GetSharedHandle` / `CreateSharedHandle` 得到 HANDLE
- D3D11 消费者（打开）
  - 使用 `OpenSharedResource` 或 `OpenSharedResourceByName`（或 `OpenSharedResource` + HANDLE）得到纹理引用
  - 若 keyed-mutex 存在，QueryInterface `IDXGIKeyedMutex` 并 Acquire/Release
- D3D12 流程
  - CreateCommittedResource(...) 创建
  - ID3D12Device::CreateSharedHandle(...) 返回 HANDLE
  - 消费方用 `OpenSharedHandle` 或 `OpenSharedHandleByName`（通过 ID3D12Device 或 DXGI 接口）打开资源；用 fence 来同步。

## 边界情况与常见问题（工程建议）
- 多 GPU（独显 + iGPU）: 若生产者在 iGPU，消费者在 dGPU（或反之），Windows 可能在后台做复制；检查 adapter LUID 必须一致以保证 zero-copy。若要跨 GPU，接受拷贝或使用显卡厂商专用跨卡直通（通常不可行）。
- Windows 版本 / 驱动差异：一些共享特性在老系统/老驱动上受限。建议最低测试平台（Windows 10+）并检测 API 返回值。  
- 编码器兼容性：硬件编码器（NVENC/AMF/QuickSync）通常能直接消费 NV12，若你的捕获是 BGRA，GPU 转 NV12 的零拷贝转换应在 GPU 上完成（例如通过 compute shader 或 D3D11/D3D12 的显式格式转换），并把结果作为共享资源传递给编码器。  
- CPU 可见性：避免使用 `D3D11_USAGE_STAGING` 或 Map 来做中转，会造成 CPU 读回，破坏 zero-copy。  
- 同步开销：Keyed mutex acquire/release 有开销，频繁小帧同步会拖慢。对高帧率场景，用 fence/batch 信号更高效。  
- 内存排布（row pitch）和对齐：确保使用的格式与消费者预期的对齐规则匹配，避免消费者在访问时进行额外转换。

## 实践建议（简短）
- 优先确保 producer/consumer 在同一 adapter/LUID。  
- 优先使用 NV12（若编码器支持）并在 GPU 上完成颜色空间转换。  
- 使用 NT shared handles（`_NTHANDLE`）进行跨进程共享，配合 keyed-mutex 或共享 fence 做同步。  
- 在初始化阶段检测并记录：FeatureLevel、Adapter LUID、驱动版本、是否支持 keyed mutex / shared handles，以便回退策略（例如：若不支持共享句柄则改为 GPU -> CPU -> 编码器 拷贝方案）。  
- 做健壮的错误处理：对每个共享/打开/同步 API 都检查返回，记录失败原因并选择安全回退。

---

如果你需要，我可以：
- 根据你的 repo（例如 dxgi_capture.cpp）定位现有实现并标注需要修改的具体代码位置（我会先读取相关文件并给出具体 patch 建议）；或
- 给出一段最小化 D3D11 / D3D12 的示例代码片段（创建共享 NV12 纹理 + 生产者/消费者 同步流程）。

要我接着做哪一步？

5433210: 核對現有dxgi的capture實現，是否在zerocopy支持方面，存在問題，如果有，請說明

GitHub Copilot: 我要并行读取与 DXGI 捕获实现相关的源文件来审查 zero-copy 支持（目标：找到资源创建、共享、同步或 CPU 读回的代码位置并摘录问题点）。我会读取 dxgi_capture.cpp、capturer_dxgi.cpp 和 dxgi_texture.cpp。 我将检视仓库中的 DXGI 捕获实现（已读取三个文件），下面给出发现的问题点、每项的原因和建议的修复方向（短、直接、针对性强）。

## 快速进度
- 已读取并分析：dxgi_capture.cpp、capturer_dxgi.cpp、dxgi_texture.cpp。
- 下一步：列出逐条问题、影响与修复优先级（下面开始）。

## 发现的与 zero-copy 相关的问题（逐条）

1) 直接用 CPU staging / Map 来读回像素（破坏 zero-copy）
- 位置：
  - `DXGICapture::capture()` -> `textureManager_->copyFullTexture` / `copySubregion`（dxgi_capture.cpp 调用）  
  - `DXGITexture::copyFullTexture` / `copySubregion` -> `createStagingTexture` -> `context_->CopyResource(...); Map(...)`（dxgi_texture.cpp，函数 `mapAndCopyTexture`）
  - `DXGICapturer::captureWithGpuProcessing` 最终调用 `copyProcessedTextureToFrame`，创建 staging 并 Map 后 memcpy 到 CPU (`capture.cpp`)
- 原因与影响：
  - 这些路径总是将 GPU 纹理内容复制到 CPU 可见的 staging 纹理，然后 Map/复制，这会产生 GPU->CPU 数据迁移，不是 zero-copy。即使提供了 `captureGpu()` API（返回 GPU 纹理），主 capture 路径仍然常常做读回以得出 `Frame`。
- 建议：
  - 把 zero-copy 的“优先策略”改为：当调用者请求 GPU 纹理（如 `captureGpu`），不要执行任何 staging/Map；仅返回获取到的 `ID3D11Texture2D`（已实现），并延迟或让调用方负责 `duplication_->ReleaseFrame()`。  
  - 为返回给 CPU 的 `Frame` 保留当前 Map 路径（因为用户期望 CPU 数据），但为 zero-copy path 增加明确 API（例如 `captureGpuZeroCopy()`）与文档说明：调用方需确保同一 adapter、负责 ReleaseFrame 并采用 GPU-side consumers（编码器）打开共享句柄或使用 keyed mutex/fence。

2) 没有创建可共享的资源（不能跨进程/编码器零拷贝）
- 位置：
  - `DXGITexture::createGpuTexture` 创建 GPU 中间纹理时，`MiscFlags=0`，没有 `D3D11_RESOURCE_MISC_SHARED*` 标志（dxgi_texture.cpp）。`smallTex` / `intermediateTexture` 都没有共享标志。
- 原因与影响：
  - 若要将 GPU 纹理直接交给编码器或另一个进程/设备（D3D12/NVENC/OpenHardware encoder），必须在创建时加上共享标志（如 `D3D11_RESOURCE_MISC_SHARED_NTHANDLE`，或 keyed-mutex 标志）。当前实现只能在同一 D3D11 device/context 内使用，且无法跨进程零拷贝。
- 建议：
  - 在需要 zero-copy 的路径（例如 `captureGpu` 返回 GPU 纹理或准备把纹理给 HW 编码器时），创建纹理时使用 `MiscFlags |= D3D11_RESOURCE_MISC_SHARED_NTHANDLE` 或 `D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX`（如果会在不同 D3D11 devices 间使用 keyed mutex）。并提供 `IDXGIResource1::CreateSharedHandle` 或 `ID3D11Device::CreateSharedHandle` 流程把 HANDLE 传递给消费者。

3) 缺少明确的同步（keyed mutex / fence）用于生产者/消费者
- 位置：
  - 代码中对 `acquiredTexture` 直接 CopyResource/CopySubresourceRegion，然后立即 Map staging、ReleaseFrame；对共享场景没有使用 `IDXGIKeyedMutex` 或共享 fence。
- 原因与影响：
  - 如果下游在不同 D3D device / 另一个进程中使用同一资源，需要使用 keyed mutex 或共享 fence 保证资源使用顺序。当前实现没有这部分，可能导致数据竞争或未同步的读取（尽管在同一 device+context 中 Copy+Map 顺序 OK，但跨设备/进程则不安全）。
- 建议：
  - 在创建共享资源时（步骤 2）同时考虑使用 keyed mutex（`D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX`）并在生产者端 Acquire/Release（使用 key 值），消费者端相应 Acquire/Release。对 D3D12 使用 shared fence (`ID3D12Fence` + shared HANDLE)。

4) captureGpu / captureGpuUnlocked AcquireNextFrame 的超时与 ReleaseFrame 管理
- 位置：
  - `captureGpuUnlocked` 调用 `duplication->AcquireNextFrame(0, ...)`，但没有在函数内部调用 `ReleaseFrame()`，而是将 acquired texture ref 放到 `GpuTexture` 并 AddRef 设备/上下文/texture（capturer_dxgi.cpp）。但是没有包装说明调用者必须 later call duplication->ReleaseFrame()；实现上可能导致遗漏 ReleaseFrame 或时序问题。
- 原因与影响：
  - 若调用者忘记 ReleaseFrame，DXGI duplication 可能耗尽队列或阻塞后续帧，或导致资源未被正确回收。并且把 acquired texture 直接交出且没有共享句柄限制了可用场景（只有在同一 device+context 的短期内可用）。
- 建议：
  - 文档化 `captureGpu` 的契约：谁负责 ReleaseFrame？或改为返回共享句柄并在返回前 ReleaseFrame。最佳实践：在 `captureGpu` 返回 GPU 纹理时，创建共享句柄并 ReleaseFrame，然后消费者用共享句柄打开资源并使用（消费端无需引用 duplication）。如果想保留原始 behavior，明确 API 并在类型中跟踪是否调用者获得了 responsibility。

5) 颜色格式与编码器兼容性（没有 NV12 优先策略）
- 位置：
  - 代码普遍假设 BGRA32（`captureGpu` 设置 PixelFormat::BGRA32；`mapAndCopyTexture` 将 DXGI formats 转成 BGRA32）。GPU 编解码器通常偏好 NV12。
- 原因与影响：
  - 若想实现零拷贝给 HW encoder（通常接受 NV12），需要在 GPU 上把 BGRA 转 NV12（GPU 内转换），并创建 NV12 格式的共享资源（注意 NV12 是平面格式，需要正确创建或使用 `ID3D11Texture2D` 的 `DXGI_FORMAT_NV12` 并对 Y/U/V plane 处理）。当前实现没有 GPU 内的颜色转换共享流程，会强制 CPU 端转换或 memcpy，从而破坏 zero-copy。
- 建议：
  - 在 GPU path（`captureWithGpuProcessing`）中，如果目标是硬件编码器，优先在 GPU 上生成 NV12 格式并创建为共享资源；或提供一个 DirectX 中的 GPU convert pass（compute / pixel shader）输出 NV12，然后 share handle 给编码器。确保使用支持 NV12 的 resource creation（有时需要 `D3D11_BIND_DECODER` 或特定 API）。

6) resource 创建时 BindFlags 与 U/A/ShaderResource 兼容性不足
- 位置：
  - `createGpuTexture` 使用 `BindFlags = D3D11_BIND_RENDER_TARGET | D3D11_BIND_SHADER_RESOURCE`；`captureWithGpuProcessing` 需要 `D3D11_BIND_UNORDERED_ACCESS` 有时会创建兼容 copy（compatibleDesc.BindFlags = ... U/A）。
- 原因与影响：
  - 如果 acquired DXGI texture 没有所需 BindFlags，代码会执行 CopyResource 到新纹理（当前实现）。但 CopyResource 的创建也不会默认包含共享标志。每次拷贝都造成 GPU 内部 copy，非理想。
- 建议：
  - 当初始化时检测是否 want zero-copy compute pipeline：若要，尽量在 duplication 创建时请求合适绑定（注意 DXGI duplication 输出 texture 的属性由系统/driver 决定，不能控制）。因此要么尽早在创建 consume-side 的纹理上创建共享纹理并用 CopySubresourceRegion（仍有一次 GPU->GPU copy，但不会 Map），要么使用编码器能直接消费的格式。

7) 没有检查 adapter LUID / 跨 GPU 警告
- 位置：
  - 在设备创建(`D3D11CreateDevice(nullptr,...)`) 之后，只通过 adapter->EnumOutputs 找 output。没有显式记录 adapter LUID 或确保消费者和生产者在同一适配器。
- 原因与影响：
  - 若进程或 consumer 在不同 GPU, 共享句柄或 OpenSharedResource 可能需要内核搬运，无法保证 zero-copy。
- 建议：
  - 在 `device_` 初始化阶段记录 Adapter LUID（从 `IDXGIAdapter1::GetDesc`），在提供共享句柄或文档 API 时检查消费者的 adapter LUID 并警告/回退。

## 优先级建议（立即 -> 中期 -> 长期）
- 立即（高优先级）
  1. 文档化 `captureGpu` 行为：谁负责 ReleaseFrame；避免误用造成资源泄漏。或修改为返回共享句柄并 ReleaseFrame（更安全）。  
  2. 在 `DXGITexture::createGpuTexture` / `createStagingTexture` 中，为 zero-copy 路径添加共享标志（`D3D11_RESOURCE_MISC_SHARED_NTHANDLE` 或 keyed mutex）。只在需要 zero-copy 时设置，避免影响普通路径。  
  3. 增加检测：如果 consumer 不在同一 adapter，退回 CPU path 并记录/报错。

- 中期（中优先级）
  4. 实现 keyed mutex / shared fence 同步示例（生产者 Acquire -> CreateSharedHandle -> ReleaseFrame -> 消费者 OpenSharedHandle -> Acquire -> 使用 -> Release）。  
  5. 增加 GPU 内颜色转换到 NV12，并在 GPU 上生成 NV12 共享资源以直接给编码器。  

- 长期（低优先级）
  6. 支持 D3D12 互操作（如果需要），并把共享 fence & resource 互操作整合。  
  7. 改造接口以更清晰地表达 zero-copy 意图（例如 `captureZeroCopyToEncoder()`，内部完成共享句柄创建并返回 HANDLE）。

## 可执行修补示例（概念级）
- 在 `DXGITexture::createGpuTexture` 中：
  - if (wantShared) desc.MiscFlags |= D3D11_RESOURCE_MISC_SHARED_NTHANDLE;
  - if (wantKeyedMutex) desc.MiscFlags |= D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX;
- 在 `captureGpuUnlocked`：
  - 在成功拿到 `acquiredTexture` 后，若 caller requests cross-process/cross-device zero-copy：
    - QueryInterface `IDXGIResource1` -> `CreateSharedHandle` -> return handle string; then call `duplication->ReleaseFrame()` and do not keep acquired texture AddRef.
    - 文档化 caller 使用 `OpenSharedResourceByName` / `OpenSharedHandle` 在消费端打开。
- 在同步上，提供 helpers 使用 `IDXGIKeyedMutex::AcquireSync`/`ReleaseSync` 或 D3D12 shared fence（示例代码我可以补充）。

---

要我现在做哪一件事（我会立刻执行并用 TODO 管理）？
- A: 在代码中自动查找并插入共享标志的最小 patch（例如把 `createGpuTexture` / `createTexture2D` 修改为在 detect zero-copy 配置下设置 `MiscFlags`），并在必要处添加注释/日志（我可以对 dxgi_texture.cpp 做最小改）。  
- B: 在 `captureGpuUnlocked` 中实现创建 shared handle 的示例路径（返回 HANDLE 给上层并 ReleaseFrame）。  
- C: 生成一篇简短的修改建议补丁清单（手工步骤和代码片段），由你选择后我再应用补丁。

5433210: 要完整支持zerocopy的話，需要做怎樣的接口調整？說說你的建議

GitHub Copilot: 我要先写入并标记待办清单的第一项为“进行接口建议分析”，然后给出完整的接口调整建议与示例原型，最后把待办标记为完成。预期产出：有序的接口变更列表、C++ 风格原型与迁移步骤。 我将给出一套完整的接口调整建议，包含设计 rationale、C++ 原型、生命周期/同步约定、迁移步骤与兼容退路；先说明计划：1) 列出需求与设计原则；2) 提出接口原型与使用契约；3) 给出迁移步骤与测试要点。下面开始。  

## 高层目标与设计原则
- 明确区分“GPU zero-copy”（直接在 GPU 间/进程间共享资源，不发生 CPU 读回）和“CPU frame”路径。  
- 提供简单、安全的契约，避免调用方误用（例如未 ReleaseFrame）。  
- 支持两类消费者：同一进程/同一 device（轻量借用），跨进程或跨 device（需 shared-handle + 同步）。  
- 默认保持向后兼容：不改变现有 capture API 的基本行为；新增 API 扩展 zero-copy 能力。  
- 提供 RAII 风格的资源生命周期管理，减少错误。

## 推荐的接口调整（按用途分组）

1) 新的 GPU zero-copy 获取接口（返回共享 handle OR RAII token）
- 目标：应用/编码器不需要依赖 duplication 的内部状态；调用者获得可在其他 device/进程打开的 HANDLE 或本进程直接使用的纹理指针并有明确释放语义。
- 原型（概念）：
  - enum ZeroCopyMode { LocalBorrow, SharedHandle };
  - struct GpuResourceHandle { // POD
      // 当 mode==LocalBorrow:
      ID3D11Device* device; ID3D11DeviceContext* context; ID3D11Texture2D* texture; // borrowed, caller must not ReleaseFrame
      // 当 mode==SharedHandle:
      HANDLE sharedHandle; DXGI_FORMAT format; int width; int height; // owner has released duplication frame
      ZeroCopyMode mode;
    };
  - Result captureZeroCopy(GpuResourceHandle& out, ZeroCopyOptions opts = {}); // 新 API
- 使用契约：
  - 如果 out.mode == LocalBorrow：捕获保持对 duplication frame 的占用，调用者必须在使用完毕后调用 `releaseZeroCopyLocal(GpuResourceHandle&)`（或调用返回的 RAII token 的 destructor）来 ReleaseFrame 并释放引用。
  - 如果 out.mode == SharedHandle：实现会调用 `IDXGIResource1::CreateSharedHandle`（或对应 D3D12 方法）生成 HANDLE，然后立即 `duplication->ReleaseFrame()`，返回给调用方一个可跨进程/跨设备打开的 HANDLE。调用方负责调用 `CloseHandle(sharedHandle)` 与在另一个 device 中 `OpenSharedResourceByName` 打开资源并使用。

2) RAII Token / ScopeGuard 风格（减少误用）
- 提案原型：
  - class ZeroCopyFrame {
      public:
        static std::unique_ptr<ZeroCopyFrame> acquireZeroCopy(...);
        // For LocalBorrow:
        ID3D11Texture2D* texture(); // non-owning pointer valid until release()
        // For SharedHandle:
        HANDLE sharedHandle(); // valid always
        void release(); // explicit release (idempotent)
        ~ZeroCopyFrame() { release(); }
    };
- 好处：若用户忘记 release，析构会安全释放资源（但要警告：若 sharedHandle returned and duplication released already，则析构仅 CloseHandle）。

3) 直接返回 shared-handle + metadata 的简短 API（跨进程）
- 原型：
  - struct SharedTextureInfo { HANDLE handle; DXGI_FORMAT format; int width; int height; std::string adapterLuid; /* optional */ };
  - Result captureSharedTexture(SharedTextureInfo& out); // synchronous: returns handle and has released duplication
- 说明：适合直接传给外部编码器进程。实现需要创建纹理或转换到 encoder-friendly format (e.g., NV12) before CreateSharedHandle.

4) 同步原语 API：keyed mutex / shared fence helpers
- 原型：
  - // Keyed mutex (D3D11)
    Result acquireKeyedMutex(IDXGIKeyedMutex* kmutex, UINT64 key, DWORD timeoutMs);
    Result releaseKeyedMutex(IDXGIKeyedMutex* kmutex, UINT64 key);
  - // Shared fence (D3D12)
    Result openSharedFence(HANDLE fenceHandle, ID3D12Fence** outFence);
    Result waitOnSharedFence(ID3D12Fence* fence, UINT64 value, DWORD timeoutMs);
- 说明：提供包装函数并在文档中给出模板（producer signal -> consumer wait 使用范例）。

5) 设备与适配器检查 API
- 为避免跨 GPU 拷贝，提供辅助手段：
  - struct AdapterInfo { LUID adapterLuid; std::string name; };
  - AdapterInfo getCaptureAdapterInfo();
  - bool isSameAdapter(const AdapterInfo& a, const AdapterInfo& b);
- 在返回 `SharedTextureInfo` 时也返回 adapter LUID，调用方在打开共享资源前验证一致性，或选择回退（例如把 shared handle 传回生产者，让生产者在本地用 GPU-to-GPU copy 处理）。

6) 兼容性与回退策略接口
- capture options 增加 flags：
  - struct ZeroCopyOptions { bool preferSharedHandle = false; bool preferLocalBorrow = true; bool requireNv12 = false; bool failIfNotZeroCopy = false; UINT timeoutMs = 500; };
- 如果 requireNv12 且硬件转换失败，返回明确错误（`Result::ZeroCopyNotPossible`），允许上层决定是否回退到 CPU path。

## 生命周期与错误模型（重要）
- 对于 LocalBorrow 模式：调用方必须在持有 texture 期间保持对 device/context 的引用（或实现返回内部 AddRef）。推荐：ZeroCopyFrame 在构造时 AddRef device/context/texture，析构时 Release 并调用 duplication->ReleaseFrame()。这样调用方可以简单持有 unique_ptr<ZeroCopyFrame> 直到使用完毕。
- 对于 SharedHandle 模式：实现创建 shared handle，并 ReleaseFrame 后立即返回 handle。调用方负责 CloseHandle，当消费者在另一进程调用 `OpenSharedResourceByName` 时应使用适当权限。实现应记录并返回 adapter LUID 供消费者校验是否真的 zero-copy（同一 adapter）。
- 错误返回应该足够明确：例如区分 `Result::Unsupported`, `Result::ZeroCopyNotPossible`, `Result::DeviceRemoved`。

## 迁移步骤（逐步实施）
1. 设计与实现基础的 `ZeroCopyFrame` RAII 类型和 `captureGpuZeroCopy()`（LocalBorrow 模式）。实现少量改动：在 `captureGpuUnlocked` 成功获取 `acquiredTexture` 后，创建 ZeroCopyFrame 对象并将 acquired texture + device/context 存入其中，并在 ZeroCopyFrame::~release 调用 `duplication->ReleaseFrame()`。这修正了现有 API 的生命周期模糊问题。
2. 实现 `captureSharedTexture`：当调用者请求 `preferSharedHandle` 时，基于 acquired texture 尝试做所需 format 转换（在 GPU），然后 QueryInterface IDXGIResource1 并调用 `CreateSharedHandle`，把 HANDLE 返回并 ReleaseFrame。增加权限/错误处理。
3. 增加 keyed mutex / fence helper 实现与示范代码（producer signal, consumer wait），并在 README 添加示例。  
4. 在 `DXGITexture::createGpuTexture` 中按需添加 `MiscFlags` 以支持 `SHARED_NTHANDLE` 或 `SHARED_KEYEDMUTEX`（基于 options）。  
5. 新 API 文档与示例：两种用法示例（local borrow 的 RAII 示例，shared handle 给外部编码器示例）。  
6. 测试矩阵：同一 adapter 同进程（local borrow），同一 adapter 跨进程（shared handle），不同 adapter 回退（验证失败并回退 CPU path），NV12 path 测试（encoder 接受 NV12）。

## 示例用法（伪代码，说明意图）
- LocalBorrow
  - auto frame = ZeroCopyFrame::acquireZeroCopy(capturer, /*options preferLocalBorrow*/);
  - // use frame->texture() on same device/context or passed to same-process encoder
  - // when frame destroyed (scope exit), duplication->ReleaseFrame() is called
- SharedHandle to encoder process
  - SharedTextureInfo s;
  - capturer.captureSharedTexture(s); // returns handle + meta
  - send handle (or handle duplication) to encoder process
  - encoder process: OpenSharedResourceByName/OpenSharedHandle -> get ID3D11Texture2D -> use directly

## 测试要点与验收标准
- 单元/集成测试：
  - LocalBorrow: Acquire -> render/encode -> ensure destructor triggers ReleaseFrame -> next frames still available。
  - SharedHandle same-adapter cross-process: producer returns handle -> consumer opens handle and reads without CPU readback -> no extra copies observed（可通过 perf counters / timing 测试）。
  - Cross-adapter: captureSharedTexture should fail or mark as non-zero-copy; consumer sees appropriate Result and fallback triggers。
- 性能：zero-copy 路径 end-to-end latency 明显低于 CPU path（测量并记录）。
- 兼容性：在 Windows 10/11 多个 GPU 驱动上验证。

## 文档与 API 变更说明（供发布用）
- 新增 API: `captureZeroCopy` / `captureSharedTexture` / `ZeroCopyFrame` RAII / `ZeroCopyOptions` / `SharedTextureInfo` / `adapterLuid` 查询函数。  
- 向后兼容：保留现有 `capture(Frame&)` 与 `captureGpu(GpuTexture&)`（Deprecated: 标注 `captureGpu` 因生命周期不明确，建议迁移到 `captureZeroCopy`）。  
- 在 README 中加入“如何选择 zero-copy 模式”和“消费者端如何使用 shared handle / keyed mutex”示例。

---

我已把第一项标记为进行中并给出完整建议；下一步我可以：
- A: 在代码里添加一个最小 RAII 实现（`ZeroCopyFrame`）并修改 `captureGpuUnlocked` 来返回它（我会更新文件并运行快速静态检查）。  
- B: 先生成一份精确的 patch 列表（包含修改哪些函数、如何添加标志和 helper）供你审阅。  
- C: 直接在 dxgi_texture.cpp / capturer_dxgi.cpp 中实现 `createSharedHandle` 路径示例并添加注释。

选择一个开始（A/B/C），我就马上用 TODO 工具标记并实施。

5433210: 先說對現有接口，要做的調整，我要評估一下影響

GitHub Copilot: 我将把任务 1 标记为已完成（write），并返回对现有接口的详细调整建议与影响评估。之后我会停留等待你决定是否让我继续执行后续的原型实现（任务 2）。 收到。我将直接给出基于你项目公开头文件 `include/capscr/capture.hpp` 的具体接口调整清单与影响评估，便于你做决策。都保持尽量向后兼容并标注风险与迁移成本。

## 总体目标（简短）
- 新增一组明确的 zero-copy 接口（RAII token + shared-handle path +同步 helper），同时保留现有 `capture(Frame&)` 与 `captureGpu(GpuTexture&)`，将旧方法标注为“不推荐用于跨进程/跨设备 zero-copy”。
- 最小化对现有用户的破坏：新增类型/方法，不改现有签名行为；逐步迁移说明与示例。

下面按改动点列出具体调整、理由、兼容性与影响。

---

1) 新增类型：ZeroCopyOptions、ZeroCopyResult、SharedTextureInfo、ZeroCopyFrame（RAII）
- 位置：添加到 `capture.hpp`（公共 API）
- 原型（建议）
  - struct ZeroCopyOptions {
      bool prefer_shared_handle = false; // 返回 cross-process HANDLE if possible
      bool require_same_adapter = true;  // fail if consumer/producer on different adapters
      bool prefer_nv12 = false; // try produce NV12 for encoder
      unsigned timeout_ms = 500;
    };
  - struct SharedTextureInfo {
      HANDLE shared_handle = nullptr; // Windows only
      DXGI_FORMAT dxgi_format = DXGI_FORMAT_UNKNOWN;
      int width = 0; int height = 0;
      std::string adapter_luid; // diagnostic, optional
    };
  - class ZeroCopyFrame { // RAII
      public:
        enum class Mode { LocalBorrow, SharedHandle };
        Mode mode() const;
        // LocalBorrow mode: non-owning pointers valid until release()
        ID3D11Texture2D* d3d11_texture() const;
        ID3D11Device* d3d11_device() const;
        ID3D11DeviceContext* d3d11_context() const;
        // SharedHandle mode:
        HANDLE shared_handle() const;
        DXGI_FORMAT dxgi_format() const;
        int width() const; int height() const;
        void release(); // idempotent
        ~ZeroCopyFrame(); // ensures release if not called
    };
- 理由：RAII 强制生命周期管理，降低遗漏 ReleaseFrame 的风险；SharedTextureInfo 用于跨进程传递 metadata。
- 向后兼容性/影响：新增类型，不影响现有 API。轻微发布成本：头文件修改，重新编译消费者代码。

2) 新增方法：captureZeroCopy / captureSharedTexture
- 位置：在 `ICapturer` 抽象类中新增虚方法
- 原型（建议）
  - // returns RAII object that encapsulates acquired frame (local borrow or shared handle)
    virtual Result captureZeroCopy(std::unique_ptr<ZeroCopyFrame>& out, const ZeroCopyOptions& opts = {});
  - // convenience: directly create shared handle + metadata and release duplication immediately
    virtual Result captureSharedTexture(SharedTextureInfo& out, const ZeroCopyOptions& opts = {});
- 理由：将 zero-copy 能力暴露为新 API，保留原 `captureGpu`。captureSharedTexture 适合直接把 HANDLE 传给外部进程/encoder。
- 向后兼容性/影响：
  - 现有 callers unaffected.
  - New methods must be implemented by DXGI backend; on backends that don't support zero-copy they return `Result::Unsupported`.
  - Minimal migration cost: callers that want zero-copy opt-in to the new APIs.

3) 修改/标注现有 `captureGpu(GpuTexture&)` 行为与契约
- 变化：在注释/文档中明确 `captureGpu` 返回的 `GpuTexture` 是“短期借用/绑定到 duplication frame”的语义，调用者需在处理完后调用 capturer 的 Release API（或 new ZeroCopyFrame）来 ReleaseFrame。并在头文件中标注“Deprecated for zero-copy across process/device”（或至少文档化风险）。
- 理由：当前实现已返回 raw D3D pointers but without clear lifetime rules;明确契约降低误用。
- 向后兼容性/影响：函数签名不变；需要文档/日志改动。若希望，可实现 `captureGpu` internally to call `captureZeroCopy` and fill `GpuTexture` for compatibility.

4) 新增同步 helpers（keyed mutex / shared fence wrappers）
- 位置：公共 API 新增一小组函数或静态 helpers（可在 `capture.hpp` 内声明或单独 header `zerocopy.hpp`）
- 原型（建议）
  - // for D3D11 keyed mutex
    Result acquireKeyedMutex(IDXGIKeyedMutex* km, uint64_t key, uint32_t timeoutMs);
    Result releaseKeyedMutex(IDXGIKeyedMutex* km, uint64_t key);
  - // for D3D12 shared fence
    Result openSharedFence(HANDLE fenceHandle, /*out*/ void** fence); // platform-opaque for header
    Result waitOnFence(void* fence, uint64_t value, uint32_t timeoutMs);
- 理由：消费者和生产者使用共享 handle 需要同步原语；提供 helpers 降低实现复杂度。
- 向后兼容性/影响：新增 API。用户若不使用 zero-copy 不受影响。

5) 返回 adapter metadata 与 capability query 增强
- 位置：ICapturer::capabilities 增加或单独方法 getAdapterInfo()
- 原型（建议）
  - struct AdapterInfo { std::string luid; std::string name; };
  - virtual AdapterInfo getAdapterInfo() const; // returns empty on unsupported backends
- 理由：用于前端判断是否能实现 zero-copy（同 adapter 是必要条件）。
- 向后兼容性/影响：新增 API，兼容。

6) Options for fail-fast or fallback policy
- 位置：ZeroCopyOptions.flag `failIfNotZeroCopy` 或 `require_zero_copy` 参数
- 理由：调用者可能希望当 zero-copy 不可行时直接返回错误而非回退到 CPU copy。
- 向后兼容性/影响：新增行为由 caller 决定（默认不改变现有回退逻辑）。

7) 文档与迁移策略（重要）
- 在 `capture.hpp` 注释和 README 中说明：
  - `capture(Frame&)` 仍然可用且做 CPU readback。
  - `captureGpu` 为 legacy GPU borrow；推荐使用 `captureZeroCopy` / `captureSharedTexture`。
  - 给出示例：LocalBorrow (same-process), SharedHandle (cross-process), 以及同步范例（keyed mutex/fence）。
- 向后兼容性/影响：无 API breakage，但要求发布说明与示例更新。

---

影響評估（技术负担、回归风险、用户改动量）
- 对现有用户（不使用 zero-copy）：几乎无影响。新增接口只需重新编译库/头文件。
- 对使用 `captureGpu` 的用户：需要阅读新文档，因为 `captureGpu` 语义需要明确（可能建议替换为 `captureZeroCopy`）。可保留原行为，但应标注为“仅在短期借用场景安全”。
- 对实现者（你们的 DXGI backend）：
  - 中等实现成本：需要在 backend 实现：
    - RAII ZeroCopyFrame（持有 duplication 引用并在析构中 ReleaseFrame）；
    - 创建共享 handle（需要 IDXGIResource1::CreateSharedHandle）并处理权限；
    - 在某些情形增加 GPU 内转换（BGRA->NV12）以满足编码器（需要 compute shader 或 pixel shader、额外资源）；
    - 增加 keyed mutex/fence 支持与示例。
  - 需要在 `DXGITexture::createGpuTexture` 或 capture path 中根据 options 设置 `MiscFlags`（如 `D3D11_RESOURCE_MISC_SHARED_NTHANDLE` 或 `..._KEYEDMUTEX`），并在创建失败时回退。
- 对跨平台：Zero-copy 的具体 HANDLE/IDXGI 类型是 Windows/DXGI 特有；为跨平台设计，应在 API 里把 platform-specific details（HANDLE、DXGI_FORMAT）封装在 `SharedTextureInfo`，在非 Windows 平台返回 `Result::Unsupported`。

风险点与兼容性问题
- 驱动/OS 支持不一致：某些驱动可能不支持 `CreateSharedHandle` 或 keyed mutex，需 robust error handling 并回退到 CPU path。测试矩阵覆盖 Windows 10/11、不同 GPU 厂商。
- 生命周期错误：若未正确实现 RAII 或调用方误用 shared HANDLE（未 CloseHandle），会产生 resource leak。RAII design 减少风险。
- 跨 GPU 行为：跨 adapter 共享可能导致内核复制或失败，API 应提供 adapter LUID metadata 以便前端检测并退回。

---

建议的最小可行改动（按优先级）
1. 在 `capture.hpp` 新增 `ZeroCopyOptions`, `ZeroCopyFrame`（头部声明）与 `captureZeroCopy` 方法签名（默认实现返回 Unsupported）。——低破坏，快速增量。
2. 实现在 DXGI backend：LocalBorrow path（RAII ZeroCopyFrame） ——中等工作量，能立刻改善 `captureGpu` 生命周期问题。
3. 实现 createSharedHandle path（captureSharedTexture）和返回 `SharedTextureInfo` ——较高工作量（格式转换、权限），但能实现真正的跨进程 zero-copy。
4. 添加同步 helper 与示例，增强文档与示例代码。

---

我已经把任务 1（列出对现有接口的调整并评估影响）完成。下一步我可以（请选一）：
- A: 直接实现头文件改动（修改 `include/capscr/capture.hpp`，添加类型/方法原型和注释），并提交 patch。  
- B: 在代码中实现第一阶段（RAII LocalBorrow）具体补丁（修改 capturer_dxgi.cpp 和 dxgi_capture.cpp 等），并运行 quick compile check。  
- C: 只生成详细 patch 清单（每个函数需修改的具体代码位置与示例实现），供你审阅后手动合并。

你希望我继续哪一步？

5433210: win7下可以使用zerocopy嗎？

GitHub Copilot: 我会简短说明在 Windows 7 上能否做 DXGI/桌面 zero-copy，并给出可行替代方案和建议。

要点结论
- 原生 DXGI Desktop Duplication（IDXGIOutputDuplication）是从 Windows 8 / WDDM 1.2 开始引入的，因此 Windows 7 上没有该 API，不能用官方桌面 duplication 实现的 zero-copy 捕获。
- 在 Windows 7 上你仍可使用 D3D11 的资源共享（shared handle / keyed mutex）做 zero-copy 但前提是你已经有 GPU-side 的图像来源（即捕获源本身就是 GPU 纹理），而不是直接从系统桌面拿到纹理；因此这对“捕获桌面并零拷贝交给编码器”通常无效。

可行替代方案（Win7）
- 硬件/厂商 SDK：NVIDIA 的 NVFBC（framebuffer capture）、AMD 的类似方案或厂商提供的 capture SDK，部分支持在 Win7 上直接把显存帧交给编码器（真正的 zero-copy）。这是最常见的在 Win7 实现零拷贝的办法，但依赖特定 GPU/驱动和许可。  
- Mirror driver / kernel 驱动（老方法）：可实现高效捕获但需安装驱动，兼容性、稳定性与性能和现代方法相比有限。  
- 进程内/同设备 zero-copy：如果被捕获目标是同一进程或可注入到目标进程在 GPU 上渲染到可共享纹理，则可以直接创建带共享标志的 D3D11 资源并交给编码器（同一 adapter、同一 GPU）。这不是“桌面全局捕获”的通用方案。  
- 回退到 CPU 路径：使用 BitBlt/GetDIBits（GDI）或 PrintWindow 等做读回再上传给编码器（非 zero-copy，但最兼容）。

工程建议
- 如果必须支持 Win7 并且需要 zero-copy：优先评估并集成厂商 SDK（NVFBC 等），并在代码中保留回退到 CPU 路径的逻辑。  
- 若可控制目标环境，强烈建议升级到 Windows 8/10/11：可直接使用 Desktop Duplication + D3D11 共享句柄/keyed-mutex + NV12 输出来实现可靠的 zero-copy 流水线。  
- 在代码层面：把 zero-copy 能力封装成可切换策略（prefer_zero_copy、require_zero_copy），并在运行时探测可用性（Desktop Duplication 可用、厂商 SDK 可用、adapter LUID 匹配等），选择最佳路径并明确返回失败原因以便上层退回。

需要我帮你做什么具体评估或实现？
- 检查当前代码是否已经可方便插入 NVFBC（或其他厂商 SDK）的集成点；或给出在代码中实现 Win7 检测并在运行时选择策略的补丁。

5433210: captureGpu本來的設計目的就是為了zerocopy，現在看來沒有起到作用，如果要讓它實現zerocopy的目的，要做什麼改動？

GitHub Copilot: 基于我对现有 `captureGpu` 实现的分析，确实存在几个关键问题阻碍了 zero-copy 的实现。我将给出具体的改动建议：

## 当前 `captureGpu` 的主要问题

1. **生命周期不明确**：调用者拿到 `GpuTexture` 后不知道何时/如何释放 DXGI frame
2. **无共享资源支持**：返回的纹理无法跨进程/设备使用
3. **缺少同步机制**：没有 keyed mutex 或 fence 支持

## 建议的改动方案

### 方案 1：最小改动 - 修复生命周期问题

````cpp
// 在 GpuTexture 结构中添加释放回调
struct GpuTexture {
#ifdef _WIN32
    ID3D11Texture2D* d3d11_texture = nullptr;
    ID3D11Device* d3d11_device = nullptr;
    ID3D11DeviceContext* d3d11_context = nullptr;
#endif
    int width = 0;
    int height = 0;
    PixelFormat format = PixelFormat::Unknown;
    std::string backend_info;
    
    // 新增：释放函数指针
    std::function<void()> release_callback;
    
    bool is_valid() const { /* 现有实现 */ }
    
    // 新增：手动释放方法
    void release() {
        if (release_callback) {
            release_callback();
            release_callback = nullptr;
        }
    }
    
    // 新增：析构时自动释放
    ~GpuTexture() { release(); }
};
````

````cpp
Result DXGICapturer::captureGpuUnlocked(GpuTexture& outTexture) {
    // ...existing code...
    
    // 创建释放回调，捕获必要的状态
    auto duplication = pimpl->duplication; // 保存引用
    outTexture.release_callback = [duplication]() {
        if (duplication) {
            duplication->ReleaseFrame();
        }
    };
    
    return capscr::Result::Ok;
}
````

### 方案 2：完整 zero-copy 支持 - 新增共享句柄接口

````cpp
// 新增零拷贝选项
struct ZeroCopyOptions {
    bool create_shared_handle = false;  // 创建可跨进程的共享句柄
    bool use_keyed_mutex = false;       // 使用 keyed mutex 同步
    bool prefer_nv12 = false;           // 优先输出 NV12 格式（编码器友好）
    uint32_t timeout_ms = 500;
};

// 零拷贝结果
struct ZeroCopyResult {
    // 本地使用（LocalBorrow 模式）
    ID3D11Texture2D* d3d11_texture = nullptr;
    ID3D11Device* d3d11_device = nullptr;
    ID3D11DeviceContext* d3d11_context = nullptr;
    
    // 跨进程使用（SharedHandle 模式）
    HANDLE shared_handle = nullptr;
    DXGI_FORMAT dxgi_format = DXGI_FORMAT_UNKNOWN;
    
    int width = 0;
    int height = 0;
    std::string adapter_luid;  // 用于验证同一 GPU
    
    enum Mode { LocalBorrow, SharedHandle } mode = LocalBorrow;
    
    // 释放资源
    std::function<void()> release_callback;
    void release() { if (release_callback) release_callback(); }
    ~ZeroCopyResult() { release(); }
};

// 在 ICapturer 中添加新方法
class ICapturer {
public:
    // ...existing methods...
    
    // 零拷贝捕获方法
    virtual Result captureZeroCopy(ZeroCopyResult& outResult, const ZeroCopyOptions& opts = {}) {
        return Result::Unsupported; // 默认实现
    }
    
    // 便利方法：直接返回共享句柄
    virtual Result captureSharedHandle(HANDLE& outHandle, DXGI_FORMAT& outFormat, 
                                      int& outWidth, int& outHeight, const ZeroCopyOptions& opts = {}) {
        return Result::Unsupported;
    }
    
    // 获取适配器信息（用于验证 zero-copy 可行性）
    virtual std::string getAdapterLUID() const { return ""; }
};
````

````cpp
Result DXGICapturer::captureZeroCopy(ZeroCopyResult& outResult, const ZeroCopyOptions& opts) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    
    if (!pimpl->device || !pimpl->duplication) {
        return Result::Unsupported;
    }
    
    // 获取帧
    DXGI_OUTDUPL_FRAME_INFO frameInfo{};
    ComPtr<IDXGIResource> dxgiResource;
    HRESULT hr = pimpl->duplication->AcquireNextFrame(opts.timeout_ms, &frameInfo, &dxgiResource);
    
    if (FAILED(hr)) {
        return Result::Error;
    }
    
    ComPtr<ID3D11Texture2D> acquiredTexture;
    hr = dxgiResource->QueryInterface(__uuidof(ID3D11Texture2D), 
                                     reinterpret_cast<void**>(acquiredTexture.GetAddressOf()));
    if (FAILED(hr)) {
        pimpl->duplication->ReleaseFrame();
        return Result::Error;
    }
    
    D3D11_TEXTURE2D_DESC desc{};
    acquiredTexture->GetDesc(&desc);
    
    if (opts.create_shared_handle) {
        // 创建可共享的纹理
        ComPtr<ID3D11Texture2D> sharedTexture;
        hr = createSharedTexture(acquiredTexture, sharedTexture, opts);
        if (FAILED(hr)) {
            pimpl->duplication->ReleaseFrame();
            return Result::Error;
        }
        
        // 获取共享句柄
        ComPtr<IDXGIResource1> dxgiRes1;
        hr = sharedTexture->QueryInterface(__uuidof(IDXGIResource1), 
                                          reinterpret_cast<void**>(dxgiRes1.GetAddressOf()));
        if (FAILED(hr)) {
            pimpl->duplication->ReleaseFrame();
            return Result::Error;
        }
        
        HANDLE sharedHandle = nullptr;
        hr = dxgiRes1->CreateSharedHandle(nullptr, DXGI_SHARED_RESOURCE_READ, 
                                         nullptr, &sharedHandle);
        if (FAILED(hr)) {
            pimpl->duplication->ReleaseFrame();
            return Result::Error;
        }
        
        // 填充 SharedHandle 模式结果
        outResult.mode = ZeroCopyResult::SharedHandle;
        outResult.shared_handle = sharedHandle;
        outResult.dxgi_format = desc.Format;
        outResult.width = desc.Width;
        outResult.height = desc.Height;
        outResult.adapter_luid = getAdapterLUID();
        
        // 立即释放 DXGI frame，因为已经创建了共享句柄
        pimpl->duplication->ReleaseFrame();
        
        outResult.release_callback = [sharedHandle]() {
            if (sharedHandle) CloseHandle(sharedHandle);
        };
        
    } else {
        // LocalBorrow 模式
        outResult.mode = ZeroCopyResult::LocalBorrow;
        outResult.d3d11_texture = acquiredTexture.Get();
        outResult.d3d11_device = pimpl->device.Get();
        outResult.d3d11_context = pimpl->context.Get();
        outResult.width = desc.Width;
        outResult.height = desc.Height;
        
        // 保持引用
        acquiredTexture.Get()->AddRef();
        pimpl->device.Get()->AddRef();
        pimpl->context.Get()->AddRef();
        
        // 延迟释放 DXGI frame
        auto duplication = pimpl->duplication;
        outResult.release_callback = [duplication, acquiredTexture]() {
            if (duplication) duplication->ReleaseFrame();
        };
    }
    
    return Result::Ok;
}

private:
HRESULT DXGICapturer::createSharedTexture(ComPtr<ID3D11Texture2D> source, 
                                         ComPtr<ID3D11Texture2D>& outShared,
                                         const ZeroCopyOptions& opts) {
    D3D11_TEXTURE2D_DESC sourceDesc;
    source->GetDesc(&sourceDesc);
    
    // 创建带共享标志的纹理
    D3D11_TEXTURE2D_DESC sharedDesc = sourceDesc;
    sharedDesc.MiscFlags = D3D11_RESOURCE_MISC_SHARED_NTHANDLE;
    if (opts.use_keyed_mutex) {
        sharedDesc.MiscFlags |= D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX;
    }
    sharedDesc.Usage = D3D11_USAGE_DEFAULT;
    sharedDesc.CPUAccessFlags = 0;
    sharedDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
    
    // 如果需要 NV12 格式转换
    if (opts.prefer_nv12 && sourceDesc.Format != DXGI_FORMAT_NV12) {
        // 这里需要实现 GPU 内的格式转换
        // 暂时跳过，保持原格式
    }
    
    HRESULT hr = pimpl->device->CreateTexture2D(&sharedDesc, nullptr, &outShared);
    if (FAILED(hr)) {
        return hr;
    }
    
    // 复制内容
    pimpl->context->CopyResource(outShared.Get(), source.Get());
    pimpl->context->Flush();
    
    return S_OK;
}

std::string DXGICapturer::getAdapterLUID() const {
    if (!pimpl->device) return "";
    
    ComPtr<IDXGIDevice> dxgiDevice;
    if (FAILED(pimpl->device.As(&dxgiDevice))) return "";
    
    ComPtr<IDXGIAdapter> adapter;
    if (FAILED(dxgiDevice->GetAdapter(&adapter))) return "";
    
    DXGI_ADAPTER_DESC desc;
    if (FAILED(adapter->GetDesc(&desc))) return "";
    
    char luidStr[32];
    sprintf_s(luidStr, "%08x%08x", desc.AdapterLuid.HighPart, desc.AdapterLuid.LowPart);
    return std::string(luidStr);
}
````

### 方案 3：简化实用方案 - 只修复当前 `captureGpu`

````cpp
// 修改现有的 captureGpu 方法，添加自动释放支持
Result DXGICapturer::captureGpu(GpuTexture& outTexture) {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    Result result = captureGpuUnlocked(outTexture);
    
    if (result == Result::Ok) {
        // 创建 RAII 包装器来管理 ReleaseFrame
        struct FrameReleaser {
            ComPtr<IDXGIOutputDuplication> duplication;
            bool released = false;
            
            FrameReleaser(ComPtr<IDXGIOutputDuplication> dup) : duplication(dup) {}
            
            ~FrameReleaser() {
                if (!released && duplication) {
                    duplication->ReleaseFrame();
                }
            }
            
            void release() {
                if (!released && duplication) {
                    duplication->ReleaseFrame();
                    released = true;
                }
            }
        };
        
        auto releaser = std::make_shared<FrameReleaser>(pimpl->duplication);
        
        // 将释放器绑定到 GpuTexture
        outTexture.backend_info = "dxgi_with_auto_release";
        outTexture.release_callback = [releaser]() {
            releaser->release();
        };
    }
    
    return result;
}

// 添加显式释放方法给用户调用
void DXGICapturer::releaseGpuFrame() {
    std::lock_guard<std::mutex> g(pimpl->mtx);
    if (pimpl->duplication) {
        pimpl->duplication->ReleaseFrame();
    }
}
````

## 推荐实施步骤

1. **立即实施方案 3**：修复现有 `captureGpu` 的生命周期问题，影响最小
2. **中期实施方案 2**：添加完整的 zero-copy 支持，提供 `captureZeroCopy` 新接口  
3. **标记 `captureGpu` 为 deprecated**：建议用户迁移到新的 zero-copy 接口

这样可以保持向后兼容，同时提供真正的 zero-copy 能力。你希望我实施哪个方案？

5433210: 如果在新增的zerocopy接口中，補充使用計算著色器進行resize和convert的話，對zerocopy有影響嗎？

GitHub Copilot: 简短结论  
- 使用计算着色器（CS）做 resize/format-convert 本身不会破坏 zero-copy；只要整个流程不把数据 Map/读回 CPU，且最终供消费者（同进程/跨进程）使用的资源是在 GPU 上产生并以共享句柄或同设备引用方式交付，就仍然是 zero-copy（“zero CPU copy”）。  

要点与注意事项
- 资源绑定与创建
  - 目标输出纹理必须以能被着色器写入或作为渲染目标的方式创建（例如需要 D3D11_BIND_UNORDERED_ACCESS 或 D3D11_BIND_RENDER_TARGET / D3D11_BIND_SHADER_RESOURCE），并在需要跨进程时在创建时设置 D3D11_RESOURCE_MISC_SHARED_NTHANDLE 或 D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX。
  - Shared-handle 必须基于 Default usage（D3D11_USAGE_DEFAULT）并且 CPUAccessFlags = 0，否则 CreateSharedHandle/跨进程打开会受限。
- 避免 CPU Map/Readback
  - 所有转换/缩放步骤必须在 GPU 上完成（Compute/Pixel shader 或视频处理 API），不要创建 staging + Map，这才是真正的 zero-copy。
- 关于 NV12（或其它编码器友好平面格式）
  - 直接用 CS 写 NV12 有实现限制：NV12 通常不能当作 UAV 写入（驱动/feature 受限）。常见做法：
    - 用 CS/RT 输出 RGBA/U8，然后让编码器能接受 RGBA（若支持）；
    - 或在 GPU 上用专用 video conversion / video processor（或 D3D11 video APIs / D3D12 支持）输出 NV12，再创建共享句柄；
    - 或创建两块平面资源并用像素着色器逐像素写入 Y 和 UV 平面（需注意 API/格式支持）。
- 同步（必须）
  - 转换结束后必须通过同步原语通知消费者：在 D3D11 场景可用 IDXGIKeyedMutex（若资源用了 KEYEDMUTEX），或创建共享 fence（D3D12）/使用显式同步（CreateSharedHandle 后生产者 ReleaseFrame 并用额外 fence 通知）。
  - 切勿用 Map 作为同步手段（导致 CPU stall）。
- 对 DXGI Desktop Duplication 的限制
  - 从 duplication 拿到的 acquired texture 的 bind flags/usage 受系统/驱动控制，可能不允许直接作为 SRV/UAV，常见需要一次 GPU->GPU Copy 到可写的 output texture。这个 GPU copy 不影响 CPU zero-copy，但会有 GPU 带宽开销。
- 兼容性与特性检测
  - 在运行时检测目标 GPU/驱动是否支持所需 UAV/format/共享特性；若不支持，回退到 GPU->GPU copy 或 CPU path。
- 性能与工程建议
  - 最好直接创建输出纹理为最终共享格式并由 CS 写入（避免中间拷贝）。  
  - 测试在目标 GPU 上的性能与帧率（CS 有线程/内存带宽限制）。  
  - 返回给上层的 zero-copy 接口应包含同步契约（RAII token / release callback）并记录 adapter LUID。

总结（一句话）  
- 用计算着色器做 resize/convert 可以并且常常是实现 zero-copy 的正确方式，但需确保输出资源的创建标志、着色器写入路径、同步机制与共享句柄流程都在 GPU 端完成且不做 CPU Map/readback；否则就会失去 zero-copy 的效果。