#include <iostream>
#include "include/capscr/capture.hpp"
#include "include/capscr/logging.hpp"

int main() {
    std::cout << "=== GPU Texture Capture and Logging Test ===" << std::endl;
    
    // Initialize logging system
    capscr::logging::init();
    capscr::logging::set_level(capscr::LogLevel::Debug);
    
    std::cout << "Logging system initialized" << std::endl;
    
    // Test logging functionality
    CAPSCR_LOG_INFO("Test application started");
    CAPSCR_LOG_DEBUG("Debug logging works");
    CAPSCR_LOG_WARN("Warning logging works");
    
    // Create DXGI capturer
    auto capturer = capscr::createCapturer(capscr::Backend::DXGI);
    if (!capturer) {
        CAPSCR_LOG_ERROR("Failed to create DXGI capturer");
        std::cout << "FAILED: Could not create DXGI capturer" << std::endl;
        return 1;
    }
    
    CAPSCR_LOG_INFO("DXGI capturer created successfully");
    std::cout << "DXGI capturer created" << std::endl;
    
    // Check if GPU capture is available
    bool gpuAvailable = capturer->isGpuCaptureAvailable();
    std::cout << "GPU capture available: " << (gpuAvailable ? "YES" : "NO") << std::endl;
    
    // Initialize capturer with full screen capture
    auto result = capturer->init(capscr::CaptureTargetType::FullScreen, "", nullptr, "");
    if (result != capscr::Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to initialize capturer");
        std::cout << "FAILED: Could not initialize capturer" << std::endl;
        return 1;
    }
    
    CAPSCR_LOG_INFO("Capturer initialized successfully");
    std::cout << "Capturer initialized" << std::endl;
    
    // Check GPU capture availability after initialization
    gpuAvailable = capturer->isGpuCaptureAvailable();
    std::cout << "GPU capture available after init: " << (gpuAvailable ? "YES" : "NO") << std::endl;
    
    if (gpuAvailable) {
        // Test GPU texture capture
        capscr::GpuTexture gpuTexture;
        auto gpuResult = capturer->captureGpu(gpuTexture);
        
        if (gpuResult == capscr::Result::Ok) {
            CAPSCR_LOG_INFO("GPU texture capture successful");
            std::cout << "SUCCESS: GPU texture captured!" << std::endl;
            std::cout << "  Texture size: " << gpuTexture.width << "x" << gpuTexture.height << std::endl;
            std::cout << "  D3D11 Device: " << (gpuTexture.d3d11_device ? "Valid" : "NULL") << std::endl;
            std::cout << "  D3D11 Context: " << (gpuTexture.d3d11_context ? "Valid" : "NULL") << std::endl;
            std::cout << "  D3D11 Texture: " << (gpuTexture.d3d11_texture ? "Valid" : "NULL") << std::endl;
            std::cout << "  Is Valid: " << (gpuTexture.is_valid() ? "YES" : "NO") << std::endl;
            
            // Clean up - release the frame
            if (gpuTexture.d3d11_context && gpuTexture.d3d11_texture) {
                // Note: In a real application, you would use the texture for hardware encoding
                // before releasing the frame
                gpuTexture.d3d11_texture->Release();
                gpuTexture.d3d11_context->Release();
                gpuTexture.d3d11_device->Release();
            }
        } else {
            CAPSCR_LOG_WARN("GPU texture capture failed");
            std::cout << "GPU texture capture failed (this may be normal if no screen updates)" << std::endl;
        }
    }
    
    // Test traditional frame capture for comparison
    capscr::Frame frame;
    auto frameResult = capturer->capture(frame);
    if (frameResult == capscr::Result::Ok) {
        CAPSCR_LOG_INFO("Traditional frame capture successful");
        std::cout << "SUCCESS: Traditional frame captured!" << std::endl;
        std::cout << "  Frame size: " << frame.width << "x" << frame.height << std::endl;
        std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    } else {
        CAPSCR_LOG_WARN("Traditional frame capture failed");
        std::cout << "Traditional frame capture failed" << std::endl;
    }
    
    CAPSCR_LOG_INFO("Test application completed");
    std::cout << "=== Test completed ===" << std::endl;
    
    capscr::logging::shutdown();
    return 0;
}
