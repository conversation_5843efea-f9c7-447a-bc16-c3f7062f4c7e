#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/logging.hpp"
#include <cstdio>

namespace capscr {

Result ICapturer::processFrame(const Frame& source, Frame& target, const CaptureParams& params) {
    // CAPSCR_LOG_INFO("processFrame: source=%dx%d fmt=%d stride=%d params.out=%dx%d fmt=%d use_gpu=%d",
    //                source.width, source.height, (int)source.format, source.stride,
    //                params.output_width, params.output_height, (int)params.output_format, params.use_gpu_processing);
    // If no processing requested, copy through
    int outW = params.output_width > 0 ? params.output_width : source.width;
    int outH = params.output_height > 0 ? params.output_height : source.height;
    PixelFormat outFmt = params.output_format;
    // Validate requested output format
    if (outFmt == PixelFormat::Unknown) outFmt = source.format;
    if (!isValidPixelFormat(outFmt)) {
        CAPSCR_LOG_ERROR("processFrame: invalid requested output_format=%d", (int)params.output_format);
        return Result::Error;
    }

    if (outW == source.width && outH == source.height && outFmt == source.format) {
        target = source; // copy
        return Result::Ok;
    }

    // Allocate target
    target.width = outW;
    target.height = outH;
    target.format = outFmt;
    target.stride = capscr::ImageUtils::getStride(outW, outFmt);
    
    fprintf(stderr, "processFrame: about to allocate target buffer: size=%zu bytes\n", static_cast<size_t>(target.stride) * target.height);
    target.data.resize(target.stride * outH);
    fprintf(stderr, "processFrame: target buffer allocated successfully\n");

    // If resize needed but format same, do resize then return
    bool resized = (outW != source.width) || (outH != source.height);
    bool converted = (outFmt != source.format);

    // If both resize and convert needed, do resize to temp then convert
    if (resized && converted) {
        Frame tmp;
        tmp.width = outW; tmp.height = outH; tmp.format = source.format;
        tmp.stride = capscr::ImageUtils::getStride(outW, source.format);
        tmp.data.resize(tmp.stride * outH);
        fprintf(stderr, "DEBUG: About to call resizeImage\n");
        fflush(stderr);
    bool okResize = capscr::ImageUtils::resizeImage(source.data.data(), tmp.data.data(), source.width, source.height, source.stride, outW, outH, tmp.stride, source.format, params.resize_quality);
        fprintf(stderr, "DEBUG: resizeImage completed ok=%d\n", okResize);
        fflush(stderr);
    // CAPSCR_LOG_DEBUG("processFrame: resized from %dx%d fmt=%d to %dx%d tmp.stride=%d -> ok=%d", source.width, source.height, (int)source.format, outW, outH, tmp.stride, okResize);
    fprintf(stderr, "processFrame: resize ok=%d src=%dx%d srcStride=%d tmp=%dx%d tmpStride=%d\n", okResize, source.width, source.height, source.stride, outW, outH, tmp.stride);
        if (!okResize) {
            return Result::Error;
        }
        fprintf(stderr, "DEBUG: About to call convertPixelFormat\n");
        fflush(stderr);
    bool okConvert = capscr::ImageUtils::convertPixelFormat(tmp.data.data(), target.data.data(), outW, outH, tmp.stride, target.stride, source.format, outFmt);
        fprintf(stderr, "DEBUG: convertPixelFormat completed ok=%d\n", okConvert);
        fflush(stderr);
    // CAPSCR_LOG_DEBUG("processFrame: convertPixelFormat srcFmt=%d tgtFmt=%d srcStride=%d tgtStride=%d ok=%d", (int)source.format, (int)outFmt, tmp.stride, target.stride, okConvert);
    fprintf(stderr, "processFrame: convert called srcFmt=%d tgtFmt=%d srcStride=%d tgtStride=%d ok=%d\n", (int)source.format, (int)outFmt, tmp.stride, target.stride, okConvert);
        if (!okConvert) {
            return Result::Error;
        }
        fprintf(stderr, "DEBUG: processFrame about to return Ok\n");
        fflush(stderr);
        return Result::Ok;
    }

    if (resized) {
        if (!capscr::ImageUtils::resizeImage(source.data.data(), target.data.data(), source.width, source.height, source.stride, outW, outH, target.stride, source.format, params.resize_quality)) {
            return Result::Error;
        }
        return Result::Ok;
    }

    if (converted) {
        if (!capscr::ImageUtils::convertPixelFormat(source.data.data(), target.data.data(), outW, outH, source.stride, target.stride, source.format, outFmt)) {
            return Result::Error;
        }
        return Result::Ok;
    }

    return Result::Ok;
}

} // namespace capscr
