#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <d3d11_1.h>
#include <dxgi.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <iostream>
#include <string>
#include <vector>

using Microsoft::WRL::ComPtr;

static void printMessages(ID3D11Device* dev, const char* tag) {
    ComPtr<ID3D11InfoQueue> iq;
    if (SUCCEEDED(dev->QueryInterface(__uuidof(ID3D11InfoQueue), reinterpret_cast<void**>(iq.GetAddressOf()))) && iq) {
        UINT64 n = iq->GetNumStoredMessagesAllowedByRetrievalFilter();
        if (n > 0) std::cout << tag << " ID3D11InfoQueue: " << n << " messages\n";
        for (UINT64 i = 0; i < n; ++i) {
            SIZE_T msgLen = 0; iq->GetMessage(i, nullptr, &msgLen);
            D3D11_MESSAGE* msg = (D3D11_MESSAGE*)malloc(msgLen);
            if (!msg) break;
            iq->GetMessage(i, msg, &msgLen);
            std::cout << "  " << msg->pDescription << "\n";
            free(msg);
        }
    }
}

int wmain(int argc, wchar_t** argv) {
    // Modes: parent creates texture and either: "child_cmd" (pass handle value in cmdline),
    // "child_inherit" (create inheritable handle and spawn child), "parent_dup" (duplicate handle to child pid)
    bool isChild = false;
    std::wstring mode;
    HANDLE handed = nullptr;
// This example was moved to examples/obsolete/zero_copy_repro.cpp
// to keep the examples directory focused. See that file for the original
// implementation. This placeholder prevents accidental rebuild errors.
int main() {
    std::cout << "zero_copy_repro has been moved to examples/obsolete/zero_copy_repro.cpp" << std::endl;
    return 0;
}
            // parent mode
            mode = arg;
        } else if (arg == L"child_run") {
            // child mode; second arg is handle value (hex)
            isChild = true;
            if (argc >= 3) {
                handed = reinterpret_cast<HANDLE>(std::stoull(std::wstring(argv[2]), nullptr, 16));
            }
            if (argc >= 5) {
                // adapter LUID passed as two 32-bit hex parts
                uint32_t low = static_cast<uint32_t>(std::stoul(std::wstring(argv[3]), nullptr, 16));
                uint32_t high = static_cast<uint32_t>(std::stoul(std::wstring(argv[4]), nullptr, 16));
                adapterLuid.LowPart = low; adapterLuid.HighPart = high;
            }
        }
    }

    LUID adapterLuid = {};

    if (isChild) {
        std::cout << "Child: attempting to OpenSharedResource on handle=" << std::hex << reinterpret_cast<uintptr_t>(handed) << std::dec << "\n";
        // Create device
        ComPtr<ID3D11Device> dev; ComPtr<ID3D11DeviceContext> ctx; D3D_FEATURE_LEVEL fl;
        // If adapterLuid is set, try to find matching adapter and create device on it
        HRESULT hr = E_FAIL;
        if (adapterLuid.LowPart != 0 || adapterLuid.HighPart != 0) {
            ComPtr<IDXGIFactory1> factory; if (SUCCEEDED(CreateDXGIFactory1(__uuidof(IDXGIFactory1), reinterpret_cast<void**>(factory.GetAddressOf())))) {
                UINT idx=0; ComPtr<IDXGIAdapter1> ada1; bool found=false;
                while (SUCCEEDED(factory->EnumAdapters1(idx, &ada1))) {
                    DXGI_ADAPTER_DESC1 adesc; ada1->GetDesc1(&adesc);
                    if (adesc.AdapterLuid.LowPart == adapterLuid.LowPart && adesc.AdapterLuid.HighPart == adapterLuid.HighPart) { found=true; break; }
                    ada1.Reset(); ++idx;
                }
                if (found && ada1) {
                    hr = D3D11CreateDevice(ada1.Get(), D3D_DRIVER_TYPE_UNKNOWN, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &dev, &fl, &ctx);
                }
            }
        }
        if (FAILED(hr)) {
            // fallback to default adapter creation
            hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &dev, &fl, &ctx);
        }
        if (FAILED(hr)) { std::cout << "Child: CreateDevice failed hr=" << std::hex << hr << std::dec << "\n"; return 2; }
        printMessages(dev.Get(), "Child");

        ComPtr<ID3D11Texture2D> tex;
        if (handed) hr = dev->OpenSharedResource(handed, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(tex.GetAddressOf()));
        else hr = E_INVALIDARG;
        std::cout << "Child: OpenSharedResource hr=0x" << std::hex << hr << std::dec << "\n";
        if (FAILED(hr)) {
            // try OpenSharedResource1
            ComPtr<ID3D11Device1> dev1; if (SUCCEEDED(dev.As(&dev1)) && dev1) {
                HRESULT hr1 = dev1->OpenSharedResource1(handed, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(tex.GetAddressOf()));
                std::cout << "Child: OpenSharedResource1 hr=0x" << std::hex << hr1 << std::dec << "\n";
            }
        }
        printMessages(dev.Get(), "Child-after");
        return 0;
    }

    // Parent mode: create device
    ComPtr<ID3D11Device> dev; ComPtr<ID3D11DeviceContext> ctx; D3D_FEATURE_LEVEL fl;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &dev, &fl, &ctx);
    if (FAILED(hr)) { std::cout << "Parent: CreateDevice failed hr=" << std::hex << hr << std::dec << "\n"; return 1; }
    printMessages(dev.Get(), "Parent");

    // Get adapter LUID for the device to allow child to create device on same adapter
    LUID adapterLuid = {};
    {
        ComPtr<IDXGIDevice> dxgidx; if (SUCCEEDED(dev.As(&dxgidx))) {
            ComPtr<IDXGIAdapter> ada; if (SUCCEEDED(dxgidx->GetAdapter(&ada)) && ada) {
                DXGI_ADAPTER_DESC desc; if (SUCCEEDED(ada->GetDesc(&desc))) adapterLuid = desc.AdapterLuid;
            }
        }
    }

    // We'll iterate several MiscFlags combinations to see which (if any) allow CreateSharedHandle.
    struct MiscTry { const char* name; UINT flags; } miscTries[] = {
        {"SHARED", D3D11_RESOURCE_MISC_SHARED},
        {"SHARED_KEYEDMUTEX", D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX},
        {"SHARED_NTHANDLE", D3D11_RESOURCE_MISC_SHARED_NTHANDLE},
        {"SHARED|KEYED", D3D11_RESOURCE_MISC_SHARED | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX},
        {"SHARED|NTHANDLE", D3D11_RESOURCE_MISC_SHARED | D3D11_RESOURCE_MISC_SHARED_NTHANDLE},
    };

    HANDLE h = nullptr;
    SECURITY_ATTRIBUTES sa = {}; sa.nLength = sizeof(sa); sa.bInheritHandle = FALSE; sa.lpSecurityDescriptor = nullptr;

    for (auto &m : miscTries) {
        D3D11_TEXTURE2D_DESC desc = {};
        desc.Width = 256; desc.Height = 128; desc.MipLevels = 1; desc.ArraySize = 1;
        desc.Format = DXGI_FORMAT_B8G8R8A8_UNORM; desc.SampleDesc.Count = 1;
        desc.Usage = D3D11_USAGE_DEFAULT; desc.BindFlags = D3D11_BIND_SHADER_RESOURCE; desc.CPUAccessFlags = 0;
        desc.MiscFlags = m.flags;

        std::vector<uint8_t> bmp(desc.Width * desc.Height * 4, 127);
        D3D11_SUBRESOURCE_DATA sd = {}; sd.pSysMem = bmp.data(); sd.SysMemPitch = desc.Width * 4;
        ComPtr<ID3D11Texture2D> tex;
        HRESULT createTex = dev->CreateTexture2D(&desc, &sd, &tex);
        std::cout << "Parent: try Misc=" << m.name << " CreateTexture2D hr=0x" << std::hex << createTex << std::dec << "\n";
        printMessages(dev.Get(), "Parent-beforecreateshared");
        if (FAILED(createTex) || !tex) { continue; }

        ComPtr<IDXGIResource1> res1; if (FAILED(tex.As(&res1))) { std::cout << "Parent: QI IDXGIResource1 failed for " << m.name << "\n"; continue; }

        // Try two CreateSharedHandle parameter variants for this resource
        HANDLE trial = nullptr;
        HRESULT r1 = res1->CreateSharedHandle(&sa, GENERIC_ALL, nullptr, &trial);
        std::cout << "Parent: CreateSharedHandle(" << m.name << ", sa, GENERIC_ALL) hr=0x" << std::hex << r1 << std::dec << " handle=" << std::hex << reinterpret_cast<uintptr_t>(trial) << std::dec << "\n";
        if (trial) { CloseHandle(trial); trial = nullptr; }
        printMessages(dev.Get(), "Parent-aftercreateshared1");

        HRESULT r2 = res1->CreateSharedHandle(nullptr, GENERIC_ALL, nullptr, &trial);
        std::cout << "Parent: CreateSharedHandle(" << m.name << ", nullptr, GENERIC_ALL) hr=0x" << std::hex << r2 << std::dec << " handle=" << std::hex << reinterpret_cast<uintptr_t>(trial) << std::dec << "\n";
        if (trial) { CloseHandle(trial); trial = nullptr; }
        printMessages(dev.Get(), "Parent-aftercreateshared2");

        HRESULT r3 = res1->CreateSharedHandle(&sa, DXGI_SHARED_RESOURCE_READ, nullptr, &trial);
        std::cout << "Parent: CreateSharedHandle(" << m.name << ", sa, DXGI_SHARED_RESOURCE_READ) hr=0x" << std::hex << r3 << std::dec << " handle=" << std::hex << reinterpret_cast<uintptr_t>(trial) << std::dec << "\n";
        if (trial) { CloseHandle(trial); trial = nullptr; }
        printMessages(dev.Get(), "Parent-aftercreateshared3");

        // If any attempt returned S_OK and provided a handle, keep it and break
        if (SUCCEEDED(r1) || SUCCEEDED(r2) || SUCCEEDED(r3)) {
            // Re-create final texture and get a final handle (prefer r2 success)
            ComPtr<ID3D11Texture2D> finalTex;
            HRESULT cr = dev->CreateTexture2D(&desc, &sd, &finalTex);
            if (SUCCEEDED(cr) && finalTex) {
                ComPtr<IDXGIResource1> finalRes; if (SUCCEEDED(finalTex.As(&finalRes))) {
                    HRESULT finalHr = finalRes->CreateSharedHandle(nullptr, GENERIC_ALL, nullptr, &h);
                    std::cout << "Parent: final CreateSharedHandle result hr=0x" << std::hex << finalHr << std::dec << " handle=" << std::hex << reinterpret_cast<uintptr_t>(h) << std::dec << "\n";
                }
            }
            break;
        }
    }

    if (mode == L"child_cmd") {
        // spawn child and pass handle as hex on command line
    wchar_t cmd[512]; swprintf(cmd, 512, L"%ls child_run 0x%llx 0x%08x 0x%08x", L"zero_copy_repro.exe", (unsigned long long)reinterpret_cast<uintptr_t>(h), adapterLuid.LowPart, adapterLuid.HighPart);
        STARTUPINFOW si = {}; PROCESS_INFORMATION pi = {}; si.cb = sizeof(si);
        if (!CreateProcessW(nullptr, cmd, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            std::cout << "Parent: CreateProcess failed lastErr=" << GetLastError() << "\n";
        } else {
            WaitForSingleObject(pi.hProcess, INFINITE);
            CloseHandle(pi.hThread); CloseHandle(pi.hProcess);
        }
    } else if (mode == L"child_inherit") {
        // make handle inheritable and spawn child with bInheritHandles=TRUE
        HANDLE inher = nullptr;
        if (!DuplicateHandle(GetCurrentProcess(), h, GetCurrentProcess(), &inher, 0, TRUE, DUPLICATE_SAME_ACCESS)) {
            std::cout << "Parent: DuplicateHandle for inherit failed err=" << GetLastError() << "\n";
        } else {
            wchar_t cmd[256]; swprintf(cmd, 256, L"%ls child_run 0x%llx 0x%08x 0x%08x", L"zero_copy_repro.exe", (unsigned long long)reinterpret_cast<uintptr_t>(inher), adapterLuid.LowPart, adapterLuid.HighPart);
            STARTUPINFOW si = {}; PROCESS_INFORMATION pi = {}; si.cb = sizeof(si);
            if (!CreateProcessW(nullptr, cmd, nullptr, nullptr, TRUE, 0, nullptr, nullptr, &si, &pi)) {
                std::cout << "Parent: CreateProcess(inherit) failed err=" << GetLastError() << "\n";
            } else {
                WaitForSingleObject(pi.hProcess, INFINITE);
                CloseHandle(pi.hThread); CloseHandle(pi.hProcess);
            }
            CloseHandle(inher);
        }
    } else if (mode == L"parent_dup") {
        // ask user for child pid and DuplicateHandle into it
        std::cout << "Parent: enter child pid to dup handle into: ";
        DWORD pid=0; std::cin >> pid;
        // spawn a waiting child to receive handle (child will block waiting for console input)
        // For simplicity, we expect a separate child started that prints its pid and waits; we'll just duplicate into that pid
        HANDLE hTargetProc = OpenProcess(PROCESS_DUP_HANDLE | PROCESS_QUERY_INFORMATION, FALSE, pid);
        if (!hTargetProc) { std::cout << "Parent: OpenProcess failed err=" << GetLastError() << "\n"; }
        else {
            HANDLE remoteH = nullptr;
            if (!DuplicateHandle(GetCurrentProcess(), h, hTargetProc, &remoteH, 0, FALSE, DUPLICATE_SAME_ACCESS)) {
                std::cout << "Parent: DuplicateHandle->child failed err=" << GetLastError() << "\n";
            } else {
                std::cout << "Parent: duplicated handle value to child: 0x" << std::hex << reinterpret_cast<uintptr_t>(remoteH) << std::dec << "\n";
                CloseHandle(remoteH);
            }
            CloseHandle(hTargetProc);
        }
    } else {
        std::cout << "Parent: no mode specified. Use child_cmd, child_inherit, or parent_dup\n";
    }

    if (h) CloseHandle(h);
    return 0;
}
#else
int main() { return 0; }
#endif
