# NV12 ZeroCopy Examples

This directory contains examples demonstrating the NV12 ZeroCopy functionality of the capscr library.

## Examples Overview

### 1. simple_nv12_example.cpp
**Purpose**: Basic introduction to NV12 capture
**Features**:
- Simple NV12 ZeroCopy capture
- Basic error handling
- Texture validation

**Usage**:
```bash
cd build/examples/Release
./simple_nv12_example.exe
```

### 2. nv12_zerocopy_demo.cpp
**Purpose**: Complete demonstration of all NV12 features
**Features**:
- Basic NV12 capture
- Texture property inspection
- Shared handle generation
- Performance testing
- Resource management validation
- Cross-device texture sharing simulation

**Usage**:
```bash
cd build/examples/Release
./nv12_zerocopy_demo.exe
```

### 3. encoder_integration_example.cpp
**Purpose**: Shows how to integrate NV12 capture with video encoders
**Features**:
- NV12 capture with shared handles
- Encoder device simulation
- Cross-device texture sharing
- Proper resource management for encoding scenarios
- Error handling for encoder integration

**Usage**:
```bash
cd build/examples/Release
./encoder_integration_example.exe
```

## Building the Examples

All examples are built as part of the main CMake build:

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release

# Examples will be in build/examples/Release/
```

## NV12 Format Details

NV12 is a YUV 4:2:0 planar format commonly used by video encoders:
- **Y Plane**: Full resolution luminance data (DXGI_FORMAT_R8_UNORM)
- **UV Plane**: Half resolution chroma data (DXGI_FORMAT_R8G8_UNORM)
- **UV Dimensions**: Width/2 × Height/2 of Y plane

## Shared Handle Integration

For encoder integration, shared handles enable zero-copy texture sharing between:
- **Capture Device**: Where screen content is captured
- **Encoder Device**: Where video encoding occurs

This eliminates the need for CPU memory copies, significantly improving performance.

## Error Handling

All examples include comprehensive error handling for:
- Device creation failures
- Capture initialization errors
- GPU unavailability
- Shared handle creation failures
- Cross-device access issues

## Performance Considerations

- NV12 conversion is performed on GPU using compute shaders
- Shared handles enable zero-copy integration with encoders
- Resource management is automatic through RAII
- Performance testing shows typical capture times under 10ms

## Prerequisites

- Windows 10/11 with DirectX 11 support
- DXGI-compatible graphics hardware
- Visual Studio 2019 or later (for building)

## Troubleshooting

**"GPU capture not available"**: 
- Ensure DirectX 11 is supported
- Check graphics driver updates

**"Shared handles not available"**:
- Verify hardware supports shared resources
- Check if running on discrete vs integrated GPU

**"NV12 conversion failed"**:
- Ensure compute shaders are available
- Check HLSL shader compilation

## Integration Guide

To integrate NV12 capture in your application:

1. **Basic Setup**:
```cpp
auto capturer = capscr::createDXGICapturer();
capturer->init(capscr::CaptureTargetType::FullScreen);
```

2. **NV12 Capture**:
```cpp
capscr::ZeroCopyFrame frame;
capscr::ZeroCopyOptions opts;
opts.prefer_nv12 = true;
opts.enable_shared_handle = true;  // For encoder integration
capturer->captureZeroCopy(frame, opts);
```

3. **Encoder Integration**:
```cpp
// Open shared textures in encoder device
encoderDevice->OpenSharedResource(frame.shared_handle, IID_PPV_ARGS(&yTexture));
encoderDevice->OpenSharedResource(frame.shared_handle_uv, IID_PPV_ARGS(&uvTexture));
```

For more details, see the example source code and documentation.
