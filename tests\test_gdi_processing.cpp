#include <gtest/gtest.h>
#include "capscr/capture.hpp"
#include "capscr/platform/windows/capturer_gdi.hpp"
#include "test_window_helper.hpp"
#include <memory>

using namespace capscr;

class GDIProcessingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // create test window
        testWindow = std::make_unique<TestWindow>();
        TestWindow::TestPattern pattern;
        pattern.width = 2320; pattern.height = 1240;
        pattern.backgroundColor = RGB(100,120,140);
        pattern.text = "GDI_PROC_TEST";
        bool created = testWindow->Create(pattern);
        ASSERT_TRUE(created);

        // 测试直接构造是否还会崩溃
        capturer = std::make_unique<GdiCapturer>();  // 现在应该可以直接构造了
        // capturer = createGdiCapturer();  // 工厂函数方式
        // capturer = std::make_unique<GdiCapturer>();  // 直接构造可能导致虚函数表问题
        ASSERT_NE(capturer, nullptr);
    }

    void TearDown() override {
        if (capturer) capturer->shutdown();
        if (testWindow) testWindow->Destroy();
    }

    std::unique_ptr<TestWindow> testWindow;
    std::unique_ptr<GdiCapturer> capturer;  // 现在应该可以直接使用具体类型了
    // std::unique_ptr<ICapturer> capturer;  // 基类指针方式
};

TEST_F(GDIProcessingTest, ResizeAndConvertCPU) {
    ASSERT_TRUE(testWindow);
    Rect r{0,0, testWindow->GetPattern().width, testWindow->GetPattern().height};
    Result r0 = capturer->init(CaptureTargetType::Window, "", &r, std::to_string(reinterpret_cast<uintptr_t>(testWindow->GetHandle())));
    ASSERT_EQ(r0, Result::Ok);

    CaptureParams params;
    params.use_gpu_processing = false;
    params.output_width = 160;
    params.output_height = 120;
    params.output_format = PixelFormat::RGB24;

    Frame out;
    Result res = capturer->capture(out, params);
    EXPECT_EQ(res, Result::Ok);
    EXPECT_EQ(out.width, 160);
    EXPECT_EQ(out.height, 120);
    EXPECT_EQ(out.format, PixelFormat::RGB24);
    EXPECT_GE((int)out.data.size(), out.width * out.height * 3);
}
