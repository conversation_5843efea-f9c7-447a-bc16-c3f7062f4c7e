#!/usr/bin/env bash
# Loop-run the specific DXGI capture test until a black-frame is detected or max runs reached.

set -euo pipefail
ROOT_DIR="$(cd "$(dirname "$0")/.." && pwd)"
BUILD_TEST_DIR="$ROOT_DIR/build/tests/Debug"
DUMP_DIR="$ROOT_DIR/build/tests/dumps"
TEST_EXE="$BUILD_TEST_DIR/capscr_tests.exe"
GTEST_FILTER="DXGICaptureTest.ConsistentCapturesFromControlledWindow"
MAX_RUNS=${1:-1000}

mkdir -p "$DUMP_DIR"

export CAPSCR_DUMP_DIR="$DUMP_DIR"
export CAPSCR_DUMP_FRAMES=1

echo "Starting test loop up to $MAX_RUNS runs, dumps -> $DUMP_DIR"

for i in $(seq 1 $MAX_RUNS); do
    logfile="$DUMP_DIR/run_loop_${i}.log"
    echo "Run #$i -> $logfile"
    # Run the test and capture stdout/stderr
    (cd "$BUILD_TEST_DIR" && "$TEST_EXE" --gtest_filter="$GTEST_FILTER") > "$logfile" 2>&1 || true

    # Scan for machine-readable blackchk event indicating an all-zero frame
    if grep -q "event=blackchk_out.*out_nonzero=0" "$logfile" || grep -q "BLACKCHK_FRAME[12] .*nonzero=0" "$logfile" || grep -q "event=raw_written" "$logfile"; then
        echo "Black-frame or raw dump detected on run #$i. See $logfile"
        # copy summary to latest
        cp "$logfile" "$DUMP_DIR/last_failure.log"
        exit 0
    fi

    # Optional: small sleep to avoid hammering GPU
    sleep 0.05
done

echo "No black-frame detected in $MAX_RUNS runs"
exit 1
