#include "capscr/logging.hpp"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <iostream>
#include <mutex>

namespace capscr {

namespace {
    class LoggerManager {
    public:
        static LoggerManager& instance() {
            static LoggerManager manager;
            return manager;
        }
        
        void init() {
            std::lock_guard<std::mutex> lock(mutex_);
            if (initialized_) return;
            
            try {
                // Try to find existing logger first
                auto existing_logger = spdlog::get("capscr");
                if (existing_logger) {
                    logger_ = std::make_shared<SpdlogAdapter>(existing_logger);
                } else {
                    // Create new console logger
                    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                    console_sink->set_level(spdlog::level::debug);
                    
                    auto spdlog_logger = std::make_shared<spdlog::logger>("capscr", console_sink);
                    spdlog_logger->set_level(spdlog::level::debug);
                    spdlog_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
                    
                    spdlog::register_logger(spdlog_logger);
                    logger_ = std::make_shared<SpdlogAdapter>(spdlog_logger);
                }
                initialized_ = true;
            }
            catch (const std::exception& e) {
                // Fallback to console logger
                logger_ = std::make_shared<ConsoleLogger>();
                initialized_ = true;
            }
        }
        
        void init(const std::string& log_file_path) {
            std::lock_guard<std::mutex> lock(mutex_);
            if (initialized_) return;
            
            try {
                // Try to find existing logger first
                auto existing_logger = spdlog::get("capscr");
                if (existing_logger) {
                    logger_ = std::make_shared<SpdlogAdapter>(existing_logger);
                } else {
                    // Create console and file sinks
                    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                    console_sink->set_level(spdlog::level::info);
                    
                    auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                        log_file_path, 1024*1024*5, 3);
                    file_sink->set_level(spdlog::level::debug);
                    
                    auto spdlog_logger = std::make_shared<spdlog::logger>("capscr", 
                        spdlog::sinks_init_list{console_sink, file_sink});
                    spdlog_logger->set_level(spdlog::level::debug);
                    spdlog_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
                    
                    spdlog::register_logger(spdlog_logger);
                    logger_ = std::make_shared<SpdlogAdapter>(spdlog_logger);
                }
                initialized_ = true;
            }
            catch (const std::exception& e) {
                // Fallback to console logger
                logger_ = std::make_shared<ConsoleLogger>();
                initialized_ = true;
            }
        }
        
        void set_logger(std::shared_ptr<ILogger> logger) {
            std::lock_guard<std::mutex> lock(mutex_);
            logger_ = std::move(logger);
            initialized_ = true;
        }
        
        std::shared_ptr<ILogger> get_logger() {
            std::lock_guard<std::mutex> lock(mutex_);
            if (!initialized_) {
                // Auto-initialize with console logger
                init();
            }
            return logger_;
        }
        
        void shutdown() {
            std::lock_guard<std::mutex> lock(mutex_);
            if (initialized_) {
                logger_.reset();
                spdlog::drop("capscr");
                initialized_ = false;
            }
        }
        
    private:
        LoggerManager() = default;
        ~LoggerManager() {
            shutdown();
        }
        
        std::shared_ptr<ILogger> logger_;
        std::mutex mutex_;
        bool initialized_ = false;
    };
}

// spdlog adapter implementation
class SpdlogAdapter : public ILogger {
public:
    explicit SpdlogAdapter(std::shared_ptr<spdlog::logger> logger) 
        : logger_(std::move(logger)) {}
    
    void log(LogLevel level, const std::string& message) override {
        if (!logger_) return;
        
        switch (level) {
            case LogLevel::Trace:
                logger_->trace(message);
                break;
            case LogLevel::Debug:
                logger_->debug(message);
                break;
            case LogLevel::Info:
                logger_->info(message);
                break;
            case LogLevel::Warn:
                logger_->warn(message);
                break;
            case LogLevel::Error:
                logger_->error(message);
                break;
            case LogLevel::Critical:
                logger_->critical(message);
                break;
            case LogLevel::Off:
                break;
        }
    }
    
    void set_level(LogLevel level) override {
        if (!logger_) return;
        
        spdlog::level::level_enum spdlog_level;
        switch (level) {
            case LogLevel::Trace:
                spdlog_level = spdlog::level::trace;
                break;
            case LogLevel::Debug:
                spdlog_level = spdlog::level::debug;
                break;
            case LogLevel::Info:
                spdlog_level = spdlog::level::info;
                break;
            case LogLevel::Warn:
                spdlog_level = spdlog::level::warn;
                break;
            case LogLevel::Error:
                spdlog_level = spdlog::level::err;
                break;
            case LogLevel::Critical:
                spdlog_level = spdlog::level::critical;
                break;
            case LogLevel::Off:
                spdlog_level = spdlog::level::off;
                break;
        }
        logger_->set_level(spdlog_level);
    }
    
    LogLevel level() const override {
        if (!logger_) return LogLevel::Off;
        
        switch (logger_->level()) {
            case spdlog::level::trace:
                return LogLevel::Trace;
            case spdlog::level::debug:
                return LogLevel::Debug;
            case spdlog::level::info:
                return LogLevel::Info;
            case spdlog::level::warn:
                return LogLevel::Warn;
            case spdlog::level::err:
                return LogLevel::Error;
            case spdlog::level::critical:
                return LogLevel::Critical;
            case spdlog::level::off:
                return LogLevel::Off;
            default:
                return LogLevel::Info;
        }
    }

private:
    std::shared_ptr<spdlog::logger> logger_;
};

// Fallback logger for when spdlog is not available
class ConsoleLogger : public ILogger {
public:
    ConsoleLogger() : current_level_(LogLevel::Info) {}
    
    void log(LogLevel level, const std::string& message) override {
        if (level < current_level_) return;
        
        const char* level_str = "INFO";
        switch (level) {
            case LogLevel::Trace: level_str = "TRACE"; break;
            case LogLevel::Debug: level_str = "DEBUG"; break;
            case LogLevel::Info: level_str = "INFO"; break;
            case LogLevel::Warn: level_str = "WARN"; break;
            case LogLevel::Error: level_str = "ERROR"; break;
            case LogLevel::Critical: level_str = "CRITICAL"; break;
            case LogLevel::Off: return;
        }
        
        std::cout << "[" << level_str << "] " << message << std::endl;
    }
    
    void set_level(LogLevel level) override {
        current_level_ = level;
    }
    
    LogLevel level() const override {
        return current_level_;
    }

private:
    LogLevel current_level_;
};

namespace logging {

void init() {
    std::lock_guard<std::mutex> lock(g_logger_mutex);
    if (g_initialized) return;
    
    try {
        // First check if a logger with this name already exists
        auto existing_logger = spdlog::get("capscr");
        if (existing_logger) {
            // Use existing logger
            g_logger = std::make_shared<SpdlogAdapter>(existing_logger);
            g_initialized = true;
            return;
        }
        
        // Create console logger with colors
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::debug);
        
        // Create the logger
        auto logger = std::make_shared<spdlog::logger>("capscr", console_sink);
        logger->set_level(spdlog::level::debug);
        logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
        
        // Register with spdlog
        spdlog::register_logger(logger);
        
        // Wrap with our adapter
        g_logger = std::make_shared<SpdlogAdapter>(logger);
        g_initialized = true;
        
        CAPSCR_LOG_INFO("Logging system initialized (console only)");
    }
    catch (const std::exception& e) {
        // Fallback to simple console logger
        g_logger = std::make_shared<ConsoleLogger>();
        g_initialized = true;
        CAPSCR_LOG_WARN("Failed to initialize spdlog, using fallback console logger: " + std::string(e.what()));
    }
}

void init(const std::string& log_file_path) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);
    if (g_initialized) return;
    
    try {
        // First check if a logger with this name already exists
        auto existing_logger = spdlog::get("capscr");
        if (existing_logger) {
            // Use existing logger
            g_logger = std::make_shared<SpdlogAdapter>(existing_logger);
            g_initialized = true;
            return;
        }
        
        // Create console and file sinks
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::info);
        
        // Rotating file sink (5MB per file, 3 files max)
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            log_file_path, 1024 * 1024 * 5, 3);
        file_sink->set_level(spdlog::level::trace);
        
        // Create multi-sink logger
        std::vector<spdlog::sink_ptr> sinks {console_sink, file_sink};
        auto logger = std::make_shared<spdlog::logger>("capscr", sinks.begin(), sinks.end());
        logger->set_level(spdlog::level::trace);
        logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
        
        // Register with spdlog
        spdlog::register_logger(logger);
        
        // Wrap with our adapter
        g_logger = std::make_shared<SpdlogAdapter>(logger);
        g_initialized = true;
        
        CAPSCR_LOG_INFO("Logging system initialized (console + file: " + log_file_path + ")");
    }
    catch (const std::exception& e) {
        // Fallback to simple console logger
        g_logger = std::make_shared<ConsoleLogger>();
        g_initialized = true;
        CAPSCR_LOG_WARN("Failed to initialize spdlog with file, using fallback console logger: " + std::string(e.what()));
    }
}

std::shared_ptr<ILogger> get_logger() {
    std::lock_guard<std::mutex> lock(g_logger_mutex);
    if (!g_initialized) {
        // Auto-initialize with console logger
        init();
    }
    return g_logger;
}

void set_logger(std::shared_ptr<ILogger> logger) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);
    g_logger = std::move(logger);
    g_initialized = true;
}

void set_level(LogLevel level) {
    auto logger = get_logger();
    if (logger) {
        logger->set_level(level);
    }
}

void shutdown() {
    std::lock_guard<std::mutex> lock(g_logger_mutex);
    if (g_initialized) {
        CAPSCR_LOG_INFO("Shutting down logging system");
        g_logger.reset();
        spdlog::shutdown();
        g_initialized = false;
    }
}

} // namespace logging

} // namespace capscr
