#include "capscr/logging.hpp"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <iostream>
#include <mutex>
#include <cstdlib>

namespace capscr {

// spdlog adapter implementation
class SpdlogAdapter : public ILogger {
public:
    explicit SpdlogAdapter(std::shared_ptr<spdlog::logger> logger) 
        : logger_(std::move(logger)) {}
    
    void log(LogLevel level, const std::string& message) override {
        if (!logger_) return;
        
        switch (level) {
            case LogLevel::Trace:
                logger_->trace(message);
                break;
            case LogLevel::Debug:
                logger_->debug(message);
                break;
            case LogLevel::Info:
                logger_->info(message);
                break;
            case LogLevel::Warn:
                logger_->warn(message);
                break;
            case LogLevel::Error:
                logger_->error(message);
                break;
            case LogLevel::Critical:
                logger_->critical(message);
                break;
            case LogLevel::Off:
                break;
        }
    }
    
    void set_level(LogLevel level) override {
        if (!logger_) return;
        
        spdlog::level::level_enum spdlog_level;
        switch (level) {
            case LogLevel::Trace:
                spdlog_level = spdlog::level::trace;
                break;
            case LogLevel::Debug:
                spdlog_level = spdlog::level::debug;
                break;
            case LogLevel::Info:
                spdlog_level = spdlog::level::info;
                break;
            case LogLevel::Warn:
                spdlog_level = spdlog::level::warn;
                break;
            case LogLevel::Error:
                spdlog_level = spdlog::level::err;
                break;
            case LogLevel::Critical:
                spdlog_level = spdlog::level::critical;
                break;
            case LogLevel::Off:
                spdlog_level = spdlog::level::off;
                break;
        }
        logger_->set_level(spdlog_level);
    }
    
    LogLevel level() const override {
        if (!logger_) return LogLevel::Off;
        
        switch (logger_->level()) {
            case spdlog::level::trace:
                return LogLevel::Trace;
            case spdlog::level::debug:
                return LogLevel::Debug;
            case spdlog::level::info:
                return LogLevel::Info;
            case spdlog::level::warn:
                return LogLevel::Warn;
            case spdlog::level::err:
                return LogLevel::Error;
            case spdlog::level::critical:
                return LogLevel::Critical;
            case spdlog::level::off:
                return LogLevel::Off;
            default:
                return LogLevel::Info;
        }
    }

private:
    std::shared_ptr<spdlog::logger> logger_;
};

// Fallback logger for when spdlog is not available
class ConsoleLogger : public ILogger {
public:
    ConsoleLogger() : current_level_(LogLevel::Info) {}
    
    void log(LogLevel level, const std::string& message) override {
        if (level < current_level_) return;
        
        const char* level_str = "INFO";
        switch (level) {
            case LogLevel::Trace: level_str = "TRACE"; break;
            case LogLevel::Debug: level_str = "DEBUG"; break;
            case LogLevel::Info: level_str = "INFO"; break;
            case LogLevel::Warn: level_str = "WARN"; break;
            case LogLevel::Error: level_str = "ERROR"; break;
            case LogLevel::Critical: level_str = "CRITICAL"; break;
            case LogLevel::Off: return;
        }
        
        std::cout << "[" << level_str << "] " << message << std::endl;
    }
    
    void set_level(LogLevel level) override {
        current_level_ = level;
    }
    
    LogLevel level() const override {
        return current_level_;
    }

private:
    LogLevel current_level_;
};

namespace {
    class LoggerManager {
    public:
        static LoggerManager& instance() {
            static LoggerManager manager;
            return manager;
        }
        
        void init() {
            std::lock_guard<std::mutex> lock(mutex_);
            if (initialized_) return;
            
            try {
                // Try to find existing logger first
                auto existing_logger = spdlog::get("capscr");
                if (existing_logger) {
                    logger_ = std::make_shared<SpdlogAdapter>(existing_logger);
                } else {
                    // Create new console logger
                    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                    console_sink->set_level(spdlog::level::debug);
                    
                    auto spdlog_logger = std::make_shared<spdlog::logger>("capscr", console_sink);
                    spdlog_logger->set_level(spdlog::level::debug);
                    spdlog_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
                    
                    spdlog::register_logger(spdlog_logger);
                    logger_ = std::make_shared<SpdlogAdapter>(spdlog_logger);
                }
                initialized_ = true;
            }
            catch (const std::exception& e) {
                // Fallback to console logger
                logger_ = std::make_shared<ConsoleLogger>();
                initialized_ = true;
            }
        }
        
        void init(const std::string& log_file_path) {
            std::lock_guard<std::mutex> lock(mutex_);
            if (initialized_) return;
            
            try {
                // Try to find existing logger first
                auto existing_logger = spdlog::get("capscr");
                if (existing_logger) {
                    logger_ = std::make_shared<SpdlogAdapter>(existing_logger);
                } else {
                    // Create console and file sinks
                    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                    console_sink->set_level(spdlog::level::info);
                    
                    auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                        log_file_path, 1024*1024*5, 3);
                    file_sink->set_level(spdlog::level::debug);
                    
                    auto spdlog_logger = std::make_shared<spdlog::logger>("capscr", 
                        spdlog::sinks_init_list{console_sink, file_sink});
                    spdlog_logger->set_level(spdlog::level::debug);
                    spdlog_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
                    
                    spdlog::register_logger(spdlog_logger);
                    logger_ = std::make_shared<SpdlogAdapter>(spdlog_logger);
                }
                initialized_ = true;
            }
            catch (const std::exception& e) {
                // Fallback to console logger
                logger_ = std::make_shared<ConsoleLogger>();
                initialized_ = true;
            }
        }
        
        void set_logger(std::shared_ptr<ILogger> logger) {
            std::lock_guard<std::mutex> lock(mutex_);
            logger_ = std::move(logger);
            initialized_ = true;
        }
        
        std::shared_ptr<ILogger> get_logger() {
            std::lock_guard<std::mutex> lock(mutex_);
            if (!initialized_) {
                // Auto-initialize with console logger (without recursive lock)
                try {
                    // Try to find existing logger first
                    auto existing_logger = spdlog::get("capscr");
                    if (existing_logger) {
                        logger_ = std::make_shared<SpdlogAdapter>(existing_logger);
                    } else {
                        // Create new console logger
                        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                        console_sink->set_level(spdlog::level::debug);
                        
                        auto spdlog_logger = std::make_shared<spdlog::logger>("capscr", console_sink);
                        spdlog_logger->set_level(spdlog::level::debug);
                        spdlog_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %v");
                        
                        spdlog::register_logger(spdlog_logger);
                        logger_ = std::make_shared<SpdlogAdapter>(spdlog_logger);
                    }
                    initialized_ = true;
                }
                catch (const std::exception& e) {
                    // Fallback to console logger
                    logger_ = std::make_shared<ConsoleLogger>();
                    initialized_ = true;
                }
            }
            return logger_;
        }
        
        void shutdown() {
            // During program exit, skip all cleanup to prevent static destruction issues
            // Just mark as not initialized without touching spdlog
            std::printf("[LoggerManager] shutdown() called - skipping spdlog cleanup to prevent crashes\n");
            std::lock_guard<std::mutex> lock(mutex_);
            if (initialized_) {
                logger_.reset();
                initialized_ = false;
            }
            std::printf("[LoggerManager] shutdown() completed\n");
        }
        
    private:
        // Check if we're in static destruction phase by testing if atexit handlers work
        bool isStaticDestructionPhase() {
            static bool phase_detected = false;
            if (phase_detected) return true;
            
            // Try to register an atexit handler - this will fail during static destruction
            int result = std::atexit([](){});
            if (result != 0) {
                phase_detected = true;
                return true;
            }
            return false;
        }
        
    private:
        LoggerManager() = default;
        ~LoggerManager() {
            shutdown();
        }
        
        std::shared_ptr<ILogger> logger_;
        std::mutex mutex_;
        bool initialized_ = false;
    };
}

namespace logging {

void init() {
    LoggerManager::instance().init();
}

void init(const std::string& log_file_path) {
    LoggerManager::instance().init(log_file_path);
}

void set_logger(std::shared_ptr<ILogger> logger) {
    LoggerManager::instance().set_logger(std::move(logger));
}

std::shared_ptr<ILogger> get_logger() {
    return LoggerManager::instance().get_logger();
}

void set_level(LogLevel level) {
    auto logger = LoggerManager::instance().get_logger();
    if (logger) {
        logger->set_level(level);
    }
}

void shutdown() {
    LoggerManager::instance().shutdown();
}

} // namespace logging
} // namespace capscr
