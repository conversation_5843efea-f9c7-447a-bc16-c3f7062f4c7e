#include <gtest/gtest.h>
#include "capture_test_environment.hpp"

// Main test runner - Google Test will automatically discover and run all tests
int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    // Register global test environment (Google Test will manage the instance)
    ::testing::AddGlobalTestEnvironment(CaptureTestEnvironment::Instance());
    
    return RUN_ALL_TESTS();
}
