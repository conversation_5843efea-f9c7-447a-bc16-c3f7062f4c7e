#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <iostream>
#include <string>
#include <vector>

using Microsoft::WRL::ComPtr;

static void printError(const char* msg, HRESULT hr) {
    std::cerr << msg << " HRESULT=0x" << std::hex << hr << std::dec << std::endl;
}

int runConsumer(HANDLE sharedHandle, HANDLE sharedHandleUV, const std::string& dumpBase, const LUID& targetLuid = {}) {
    ComPtr<ID3D11Device> device;
    ComPtr<ID3D11DeviceContext> context;
    D3D_FEATURE_LEVEL fl;
    HRESULT hr = E_FAIL;

    // If a target adapter LUID was provided, try to create device on that adapter
    if (targetLuid.HighPart != 0 || targetLuid.LowPart != 0) {
        ComPtr<IDXGIFactory1> factory;
        if (SUCCEEDED(CreateDXGIFactory1(__uuidof(IDXGIFactory1), reinterpret_cast<void**>(factory.GetAddressOf())))) {
            ComPtr<IDXGIAdapter1> adapter;
            for (UINT i = 0; factory->EnumAdapters1(i, adapter.GetAddressOf()) != DXGI_ERROR_NOT_FOUND; ++i) {
                DXGI_ADAPTER_DESC1 ad; adapter->GetDesc1(&ad);
                std::cout << "Consumer: enumerated adapter " << i << " LUID high=0x" << std::hex << ad.AdapterLuid.HighPart << " low=0x" << ad.AdapterLuid.LowPart << std::dec << std::endl;
                if (ad.AdapterLuid.HighPart == targetLuid.HighPart && ad.AdapterLuid.LowPart == targetLuid.LowPart) {
                    std::cout << "Consumer: matching adapter found at index " << i << ", attempting D3D11CreateDevice on it" << std::endl;
                    hr = D3D11CreateDevice(adapter.Get(), D3D_DRIVER_TYPE_UNKNOWN, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &device, &fl, &context);
                    std::cout << "Consumer: D3D11CreateDevice on adapter returned hr=0x" << std::hex << hr << std::dec << std::endl;
                    if (SUCCEEDED(hr)) break;
                }
                adapter.Reset();
            }
        }
    }

    // Fallback: create device on default adapter
    if (FAILED(hr)) {
        hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr,
                               0, nullptr, 0, D3D11_SDK_VERSION, &device, &fl, &context);
    }
    if (FAILED(hr)) {
        printError("Consumer: D3D11CreateDevice failed", hr);
        return 2;
    }

    // Open shared texture
    ComPtr<ID3D11Texture2D> sharedTex;
    hr = device->OpenSharedResource(sharedHandle, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(sharedTex.GetAddressOf()));
    if (FAILED(hr) || !sharedTex) {
        printError("Consumer: OpenSharedResource failed", hr);
        return 3;
    }

    D3D11_TEXTURE2D_DESC desc; sharedTex->GetDesc(&desc);
    std::cout << "Consumer: opened shared texture " << desc.Width << "x" << desc.Height << " format=" << desc.Format << std::endl;

    // Try keyed mutex
    ComPtr<IDXGIKeyedMutex> km;
    if (SUCCEEDED(sharedTex.As(&km))) {
        std::cout << "Consumer: Acquiring keyed mutex..." << std::endl;
        hr = km->AcquireSync(0, 5000);
        if (FAILED(hr)) {
            printError("Consumer: AcquireSync failed", hr);
        } else {
            std::cout << "Consumer: Acquired keyed mutex" << std::endl;
        }
    }

    // Copy to staging
    D3D11_TEXTURE2D_DESC stDesc = desc;
    stDesc.Usage = D3D11_USAGE_STAGING;
    stDesc.BindFlags = 0;
    stDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stDesc.MiscFlags = 0;
    ComPtr<ID3D11Texture2D> staging;
    hr = device->CreateTexture2D(&stDesc, nullptr, &staging);
    if (FAILED(hr) || !staging) {
        printError("Consumer: Create staging failed", hr);
        if (km) km->ReleaseSync(0);
        return 4;
    }

    context->CopyResource(staging.Get(), sharedTex.Get());
    context->Flush();

    D3D11_MAPPED_SUBRESOURCE mapped;
    hr = context->Map(staging.Get(), 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) {
        printError("Consumer: Map failed", hr);
        if (km) km->ReleaseSync(0);
        return 5;
    }

    std::string outFile = dumpBase + "_plane0.raw";
    FILE* f = nullptr;
    fopen_s(&f, outFile.c_str(), "wb");
    if (f) {
        for (UINT row = 0; row < desc.Height; ++row) {
            fwrite(reinterpret_cast<uint8_t*>(mapped.pData) + row * mapped.RowPitch, 1, desc.Width * 4, f);
        }
        fclose(f);
        std::cout << "Consumer: wrote " << outFile << std::endl;
    }
    context->Unmap(staging.Get(), 0);

    if (km) {
        km->ReleaseSync(0);
        std::cout << "Consumer: Released keyed mutex" << std::endl;
    }

    // UV plane if provided
    if (sharedHandleUV) {
        ComPtr<ID3D11Texture2D> uvTex;
        hr = device->OpenSharedResource(sharedHandleUV, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(uvTex.GetAddressOf()));
        if (SUCCEEDED(hr) && uvTex) {
            D3D11_TEXTURE2D_DESC uvDesc; uvTex->GetDesc(&uvDesc);
            std::cout << "Consumer: opened UV texture " << uvDesc.Width << "x" << uvDesc.Height << " format=" << uvDesc.Format << std::endl;
            D3D11_TEXTURE2D_DESC uvSt = uvDesc; uvSt.Usage = D3D11_USAGE_STAGING; uvSt.BindFlags = 0; uvSt.CPUAccessFlags = D3D11_CPU_ACCESS_READ; uvSt.MiscFlags = 0;
            ComPtr<ID3D11Texture2D> stUV; hr = device->CreateTexture2D(&uvSt, nullptr, &stUV);
            if (SUCCEEDED(hr) && stUV) {
                context->CopyResource(stUV.Get(), uvTex.Get());
                context->Flush();
                D3D11_MAPPED_SUBRESOURCE m2; hr = context->Map(stUV.Get(), 0, D3D11_MAP_READ, 0, &m2);
                if (SUCCEEDED(hr)) {
                    std::string outUV = dumpBase + "_uv.raw";
                    FILE* f2 = nullptr; fopen_s(&f2, outUV.c_str(), "wb");
                    if (f2) {
                        for (UINT row = 0; row < uvDesc.Height; ++row) {
                            fwrite(reinterpret_cast<uint8_t*>(m2.pData) + row * m2.RowPitch, 1, uvDesc.Width * 2, f2);
                        }
                        fclose(f2);
                        std::cout << "Consumer: wrote " << outUV << std::endl;
                    }
                    context->Unmap(stUV.Get(), 0);
                }
            }
        } else {
            printError("Consumer: open UV failed", hr);
        }
    }

    return 0;
}

int runProducerAndSpawnConsumer() {
    // Create device
    ComPtr<ID3D11Device> device;
    ComPtr<ID3D11DeviceContext> context;
    D3D_FEATURE_LEVEL fl;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr,
                                   0, nullptr, 0, D3D11_SDK_VERSION, &device, &fl, &context);
    if (FAILED(hr)) {
        printError("Producer: D3D11CreateDevice failed", hr);
        return 1;
    }

    // Create a test texture
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = 640;
    desc.Height = 480;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    desc.SampleDesc.Count = 1;
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
    desc.CPUAccessFlags = 0;
    desc.MiscFlags = D3D11_RESOURCE_MISC_SHARED_NTHANDLE | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX;

    std::vector<uint8_t> buf(desc.Width * desc.Height * 4);
    for (int y = 0; y < (int)desc.Height; ++y) {
        for (int x = 0; x < (int)desc.Width; ++x) {
            int i = (y * desc.Width + x) * 4;
            buf[i+0] = (uint8_t)(x % 256); // B
            buf[i+1] = (uint8_t)(y % 256); // G
            buf[i+2] = 0; // R
            buf[i+3] = 255; // A
        }
    }

    ComPtr<ID3D11Texture2D> tex;
    D3D11_SUBRESOURCE_DATA sd = {};
    sd.pSysMem = buf.data();
    sd.SysMemPitch = desc.Width * 4;
    hr = device->CreateTexture2D(&desc, &sd, &tex);
    if (FAILED(hr) || !tex) {
        printError("Producer: CreateTexture2D failed", hr);
        return 2;
    }

    // Create shared handle
    ComPtr<IDXGIResource1> res1;
    if (FAILED(tex.As(&res1))) {
        printError("Producer: QI IDXGIResource1 failed", E_FAIL);
        return 3;
    }

    HANDLE hShared = nullptr;
    // Use SECURITY_ATTRIBUTES to make handle inheritable, and GENERIC_ALL access for consumer
    SECURITY_ATTRIBUTES sa = {}; sa.nLength = sizeof(sa); sa.lpSecurityDescriptor = nullptr; sa.bInheritHandle = TRUE;
    hr = res1->CreateSharedHandle(&sa, GENERIC_ALL, nullptr, &hShared);
    if (FAILED(hr) || !hShared) {
        printError("Producer: CreateSharedHandle failed", hr);
        return 4;
    }

    // Optionally try keyed mutex ReleaseSync so consumer can AcquireSync immediately
    ComPtr<IDXGIKeyedMutex> km;
    if (SUCCEEDED(tex.As(&km))) {
        km->ReleaseSync(0);
    }

    // Obtain adapter LUID to tell consumer which adapter to use
    ComPtr<IDXGIDevice> dxgiDevice;
    if (SUCCEEDED(device.As(&dxgiDevice))) {
        ComPtr<IDXGIAdapter> adapter;
        if (SUCCEEDED(dxgiDevice->GetAdapter(&adapter))) {
                DXGI_ADAPTER_DESC descAdapter;
                if (SUCCEEDED(adapter->GetDesc(&descAdapter))) {
                    // pass adapter LUID as two separate hex args (high and low)
                    char luidHigh[32]; char luidLow[32];
                    sprintf_s(luidHigh, "%x", descAdapter.AdapterLuid.HighPart);
                    sprintf_s(luidLow, "%x", descAdapter.AdapterLuid.LowPart);

                    // Spawn consumer process (same exe) passing handle and adapter LUID parts
                    char exePath[MAX_PATH];
                    GetModuleFileNameA(nullptr, exePath, MAX_PATH);
                    std::string cmd = std::string("\"") + exePath + "\" consumer ";
                    char bufHex[64];
                    sprintf_s(bufHex, "%llx", reinterpret_cast<unsigned long long>(hShared));
                    cmd += bufHex;
                    cmd += " ";
                    cmd += luidHigh;
                    cmd += " ";
                    cmd += luidLow;
                    cmd += " --dump testdump";

                STARTUPINFOA si = {}; si.cb = sizeof(si);
                PROCESS_INFORMATION pi = {};
                BOOL ok = CreateProcessA(nullptr, cmd.data(), nullptr, nullptr, TRUE, 0, nullptr, nullptr, &si, &pi);
                if (!ok) {
                    printError("Producer: CreateProcess failed", HRESULT_FROM_WIN32(GetLastError()));
                    CloseHandle(hShared);
                    return 5;
                }

                std::cout << "Producer: spawned consumer with handle 0x" << std::hex << reinterpret_cast<unsigned long long>(hShared) << std::dec << std::endl;

                // Wait for consumer to finish
                WaitForSingleObject(pi.hProcess, INFINITE);
                DWORD code = 0; GetExitCodeProcess(pi.hProcess, &code);
                std::cout << "Producer: consumer exited with code " << code << std::endl;

                CloseHandle(pi.hProcess); CloseHandle(pi.hThread);
                CloseHandle(hShared);
                return 0;
            }
        }
    }
    // Fallback: failed to get adapter LUID
    printError("Producer: failed to obtain adapter LUID", E_FAIL);
    CloseHandle(hShared);
    return 6;
}

int main(int argc, char** argv) {
    if (argc >= 2 && std::string(argv[1]) == "consumer") {
        if (argc < 3) {
            std::cerr << "consumer usage: consumer <shared_handle_hex> [adapter_luid] [--dump base]" << std::endl;
            return 1;
        }
        unsigned long long v = std::stoull(argv[2], nullptr, 16);
        HANDLE h = reinterpret_cast<HANDLE>(v);
        HANDLE hUV = nullptr; std::string dumpBase = "consumer_dump";
        LUID targetLuid = {};
        // optional adapter LUID pieces are argv[3]=High and argv[4]=Low (hex)
        int argIdx = 3;
        if (argc > 4) {
            unsigned long high = std::stoul(argv[3], nullptr, 16);
            unsigned long low = std::stoul(argv[4], nullptr, 16);
            targetLuid.HighPart = static_cast<LONG>(high);
            targetLuid.LowPart = static_cast<ULONG>(low);
            argIdx = 5;
        }
        for (int i = argIdx; i < argc; ++i) {
            if (std::string(argv[i]) == "--dump" && i + 1 < argc) dumpBase = argv[++i];
        }

        return runConsumer(h, hUV, dumpBase, targetLuid);
    }

    std::cout << "Producer/Consumer test - spawning consumer" << std::endl;
    return runProducerAndSpawnConsumer();
}
#else
int main() { return 0; }
#endif
