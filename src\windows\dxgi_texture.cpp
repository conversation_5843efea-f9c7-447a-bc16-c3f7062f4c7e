#ifdef _WIN32
#include "dxgi_texture.hpp"
#include <algorithm>
#include <cstdint>
#include <cstring>
#include <cstdio>
#include <vector>
#include <errno.h>
#include <string.h>

namespace capscr {

DXGITexture::DXGITexture(ComPtr<ID3D11Device> device, ComPtr<ID3D11DeviceContext> context)
    : device_(device), context_(context) {
}

DXGITexture::~DXGITexture() {
    // ComPtr handles cleanup automatically
}

Result DXGITexture::createStagingTexture(UINT width, UINT height, DXGI_FORMAT format,
                                        ComPtr<ID3D11Texture2D>& outTexture) {
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = width;
    desc.Height = height;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = format;
    desc.SampleDesc.Count = 1;
    desc.SampleDesc.Quality = 0;
    desc.Usage = D3D11_USAGE_STAGING;
    desc.BindFlags = 0;
    desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    desc.MiscFlags = 0;

    return createTexture2D(desc, outTexture);
}

Result DXGITexture::createGpuTexture(UINT width, UINT height, DXGI_FORMAT format,
                                    ComPtr<ID3D11Texture2D>& outTexture) {
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = width;
    desc.Height = height;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = format;
    desc.SampleDesc.Count = 1;
    desc.SampleDesc.Quality = 0;
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.BindFlags = D3D11_BIND_RENDER_TARGET | D3D11_BIND_SHADER_RESOURCE;
    desc.CPUAccessFlags = 0;
    desc.MiscFlags = 0;

    return createTexture2D(desc, outTexture);
}

Result DXGITexture::createTexture2D(const D3D11_TEXTURE2D_DESC& desc,
                                   ComPtr<ID3D11Texture2D>& outTexture) {
    if (!device_) {
        CAPSCR_LOG_ERROR("Device is null");
        return Result::Error;
    }

    if (!validateTextureDesc(desc)) {
        CAPSCR_LOG_ERROR("Invalid texture description");
        return Result::Error;
    }

    HRESULT hr = device_->CreateTexture2D(&desc, nullptr, &outTexture);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("CreateTexture2D failed");
        
        // Check for device removal
        if (hr == DXGI_ERROR_DEVICE_REMOVED || hr == DXGI_ERROR_DEVICE_RESET) {
            return Result::Error; // Changed from DeviceLost to Error
        }
        
        return Result::Error;
    }

    CAPSCR_LOG_TRACE("Created texture2D");
    return Result::Ok;
}

Result DXGITexture::copyFullTexture(ComPtr<ID3D11Texture2D> source, Frame& outFrame) {
    if (!source) {
        CAPSCR_LOG_ERROR("Source texture is null");
        return Result::Error;
    }

    D3D11_TEXTURE2D_DESC sourceDesc;
    source->GetDesc(&sourceDesc);

    // Create staging texture with same dimensions
    ComPtr<ID3D11Texture2D> stagingTexture;
    Result result = createStagingTexture(sourceDesc.Width, sourceDesc.Height, 
                                       sourceDesc.Format, stagingTexture);
    if (result != Result::Ok) {
        return result;
    }

    // Copy resource
    context_->CopyResource(stagingTexture.Get(), source.Get());
    context_->Flush();

    // Map and copy data
    return mapAndCopyTexture(stagingTexture, sourceDesc.Width, sourceDesc.Height, outFrame);
}

Result DXGITexture::copySubregion(ComPtr<ID3D11Texture2D> source, const Rect& region, Frame& outFrame) {
    if (!source) {
        CAPSCR_LOG_ERROR("Source texture is null");
        return Result::Error;
    }

    D3D11_TEXTURE2D_DESC sourceDesc;
    source->GetDesc(&sourceDesc);

    // Validate region bounds
    UINT clampedX = region.x >= 0 ? static_cast<UINT>(region.x) : 0;
    UINT clampedY = region.y >= 0 ? static_cast<UINT>(region.y) : 0;
    UINT clampedWidth = region.width > 0 ? static_cast<UINT>(region.width) : 0;
    UINT clampedHeight = region.height > 0 ? static_cast<UINT>(region.height) : 0;
    
    // Clamp to texture bounds
    if (clampedX >= sourceDesc.Width) clampedX = sourceDesc.Width;
    if (clampedY >= sourceDesc.Height) clampedY = sourceDesc.Height;
    if (clampedX + clampedWidth > sourceDesc.Width) clampedWidth = sourceDesc.Width - clampedX;
    if (clampedY + clampedHeight > sourceDesc.Height) clampedHeight = sourceDesc.Height - clampedY;

    if (clampedWidth == 0 || clampedHeight == 0) {
        CAPSCR_LOG_ERROR("Invalid region");
        return Result::Error;
    }

    // Create intermediate GPU texture for the subregion
    ComPtr<ID3D11Texture2D> intermediateTexture;
    Result result = createGpuTexture(clampedWidth, clampedHeight, 
                                   sourceDesc.Format, intermediateTexture);
    if (result != Result::Ok) {
        return result;
    }

    // Define source box
    D3D11_BOX srcBox;
    srcBox.left = clampedX;
    srcBox.top = clampedY;
    srcBox.front = 0;
    srcBox.right = clampedX + clampedWidth;
    srcBox.bottom = clampedY + clampedHeight;
    srcBox.back = 1;

    // Copy subregion to intermediate texture
    context_->CopySubresourceRegion(intermediateTexture.Get(), 0, 0, 0, 0, 
                                   source.Get(), 0, &srcBox);
    context_->Flush();

    // Create staging texture for readback
    ComPtr<ID3D11Texture2D> stagingTexture;
    result = createStagingTexture(clampedWidth, clampedHeight, 
                                sourceDesc.Format, stagingTexture);
    if (result != Result::Ok) {
        return result;
    }

    // Copy to staging
    context_->CopyResource(stagingTexture.Get(), intermediateTexture.Get());
    context_->Flush();

    // Map and copy data
    return mapAndCopyTexture(stagingTexture, clampedWidth, clampedHeight, outFrame);
}

Result DXGITexture::mapAndCopyTexture(ComPtr<ID3D11Texture2D> stagingTexture, 
                                     UINT width, UINT height, Frame& outFrame) {
    if (!stagingTexture) {
        CAPSCR_LOG_ERROR("Staging texture is null");
        return Result::Error;
    }

    D3D11_MAPPED_SUBRESOURCE mapped;
    HRESULT hr = context_->Map(stagingTexture.Get(), 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("Failed to map staging texture");
        return Result::Error;
    }

    // Quick mapped-data diagnostics: count non-zero bytes and optionally dump raw
    size_t mappedSize = static_cast<size_t>(mapped.RowPitch) * static_cast<size_t>(height);
    size_t mappedNonZero = 0;
    const uint8_t* mappedBytes = static_cast<const uint8_t*>(mapped.pData);
    for (size_t i = 0; i < mappedSize; ++i) if (mappedBytes[i] != 0) mappedNonZero++;
    const char* dumpEnvFlag = std::getenv("CAPSCR_DUMP_FRAMES");
    if (dumpEnvFlag && dumpEnvFlag[0] != '\0') {
        fprintf(stderr, "event=dump_stat mappedSize=%zu mappedNonZero=%zu RowPitch=%u height=%u\n",
                mappedSize, mappedNonZero, static_cast<unsigned>(mapped.RowPitch), static_cast<unsigned>(height));
    }

    // Local helper: write raw buffer to CAPSCR_DUMP_DIR with key=value event
    auto write_raw_dump_local = [](const void* ptr, size_t size, const char* tag, unsigned w, unsigned h){
        if (!ptr || size==0) return;
        const char* dumpDir = std::getenv("CAPSCR_DUMP_DIR");
        if (!dumpDir || dumpDir[0] == '\0') return;
        char fname[1024] = {0};
        uintptr_t p = reinterpret_cast<uintptr_t>(ptr);
        sprintf_s(fname, sizeof(fname), "%s/%s_%zx_%ux%u.raw", dumpDir, tag, (size_t)p, w, h);
        FILE* f = nullptr;
        errno = 0;
        fopen_s(&f, fname, "wb");
        if (f) {
            size_t wrote = fwrite(ptr,1,size,f);
            int cres = fclose(f);
            fprintf(stderr, "event=raw_written file=%s tag=%s ptr=%zx size=%zu wrote=%zu fclose_res=%d\n", fname, tag, (size_t)p, size, wrote, cres);
        }
        else {
            int e = errno;
            DWORD gle = GetLastError();
            const char* estr = e ? strerror(e) : "(none)";
            fprintf(stderr, "event=raw_fail file=%s tag=%s errno=%d msg=\"%s\" GetLastError=0x%08x\n", fname, tag, e, estr, (unsigned)gle);
        }
    };

    // Print dump env for diagnostics (once per call)
    const char* ddir = std::getenv("CAPSCR_DUMP_DIR");
    const char* dflag = std::getenv("CAPSCR_DUMP_FRAMES");
    fprintf(stderr, "DUMP_ENV CAPSCR_DUMP_DIR=%s CAPSCR_DUMP_FRAMES=%s\n", ddir ? ddir : "(null)", dflag ? dflag : "(null)");

    // Prepare output frame (we want deterministic BGRA32 output)
    outFrame.width = static_cast<int>(width);
    outFrame.height = static_cast<int>(height);
    outFrame.format = PixelFormat::BGRA32; // normalize to BGRA32
    outFrame.stride = width * 4; // 4 bytes per pixel for BGRA32
    outFrame.data.resize(static_cast<size_t>(outFrame.stride) * static_cast<size_t>(height));

    // Determine source pixel format from DXGI format
    D3D11_TEXTURE2D_DESC desc;
    stagingTexture->GetDesc(&desc);
    PixelFormat srcFormat = PixelFormat::BGRA32; // default
    switch (desc.Format) {
        case DXGI_FORMAT_R8G8B8A8_UNORM:
            srcFormat = PixelFormat::RGBA32; break;
        case DXGI_FORMAT_B8G8R8A8_UNORM:
            srcFormat = PixelFormat::BGRA32; break;
        case DXGI_FORMAT_B8G8R8X8_UNORM:
            srcFormat = PixelFormat::BGRX32; break;
        default:
            // leave default and attempt best-effort copy
            srcFormat = PixelFormat::BGRA32; break;
    }

    // Copy into a temporary tightly-packed buffer matching source format
    int srcBpp = ImageUtils::getBytesPerPixel(srcFormat);
    int srcStride = static_cast<int>(width) * srcBpp;
    std::vector<uint8_t> tightSrc(static_cast<size_t>(srcStride) * static_cast<size_t>(height));
    const uint8_t* mappedData = static_cast<const uint8_t*>(mapped.pData);
    // Diagnostic logging (forced once for diagnosis)
    fprintf(stderr, "DUMP_INFO desc.Format=%u mapped.RowPitch=%u srcBpp=%d srcStride=%d outStride=%d\n",
        static_cast<unsigned>(desc.Format), static_cast<unsigned>(mapped.RowPitch), srcBpp, srcStride, outFrame.stride);
    // print first 64 bytes of mapped memory (if available)
    int mapShow = 64;
    int mappedSizeInt = static_cast<int>(mapped.RowPitch) * static_cast<int>(height);
    int toShowMap = (mapShow < mappedSizeInt) ? mapShow : mappedSizeInt;
    fprintf(stderr, "DUMP_MAPPED_PREFIX %p %dx%d :", (void*)mappedData, outFrame.width, outFrame.height);
    for (int i = 0; i < toShowMap; ++i) fprintf(stderr, " %02x", mappedData[i]);
    fprintf(stderr, "\n");
    for (UINT y = 0; y < height; ++y) {
        const uint8_t* srcRow = mappedData + static_cast<size_t>(y) * mapped.RowPitch;
        uint8_t* dstRow = tightSrc.data() + static_cast<size_t>(y) * srcStride;
        size_t rp = static_cast<size_t>(mapped.RowPitch);
        size_t st = static_cast<size_t>(srcStride);
        size_t copyBytes = (rp < st) ? rp : st;
        memcpy(dstRow, srcRow, copyBytes);
        if (copyBytes < st) memset(dstRow + copyBytes, 0, st - copyBytes);
        // Log per-row copyBytes for the first few rows
        if (y < 3) {
            fprintf(stderr, "DUMP_ROW y=%u copyBytes=%zu rp=%zu st=%zu\n", y, copyBytes, rp, st);
        }
    }

    // Quick diagnostic: count non-zero bytes in tightSrc
    size_t tightNonZero = 0;
    for (size_t i = 0; i < tightSrc.size(); ++i) if (tightSrc[i] != 0) tightNonZero++;
    fprintf(stderr, "DUMP_STAT tightSrc_nonzero=%zu total=%zu\n", tightNonZero, tightSrc.size());

    // Convert to target BGRA32 using ImageUtils helper
    bool convOk = ImageUtils::convertPixelFormat(tightSrc.data(), outFrame.data.data(), static_cast<int>(width), static_cast<int>(height), srcStride, outFrame.stride, srcFormat, outFrame.format);
    if (!convOk) {
        // If conversion unsupported, fallback to copying first min(bytes)
        const uint8_t* srcData = tightSrc.data();
        uint8_t* dstData = outFrame.data.data();
        for (UINT y = 0; y < height; ++y) {
            const uint8_t* srcRow = srcData + static_cast<size_t>(y) * srcStride;
            uint8_t* dstRow = dstData + static_cast<size_t>(y) * outFrame.stride;
            size_t rp = static_cast<size_t>(srcStride);
            size_t st = static_cast<size_t>(outFrame.stride);
            size_t copyBytes = (rp < st) ? rp : st;
            memcpy(dstRow, srcRow, copyBytes);
            if (copyBytes < st) memset(dstRow + copyBytes, 0, st - copyBytes);
        }
    }

    // Diagnostic: count non-zero bytes in outFrame and print small prefixes
    size_t outNonZero = 0;
    for (size_t i = 0; i < outFrame.data.size(); ++i) if (outFrame.data[i] != 0) outNonZero++;
    fprintf(stderr, "DUMP_STAT outFrame_nonzero=%zu total=%zu convOk=%d srcFormat=%d\n", outNonZero, outFrame.data.size(), convOk ? 1 : 0, static_cast<int>(srcFormat));
    // print first 32 bytes of tightSrc and outFrame for quick inspection
    int show = 32;
    int tshow = (show < static_cast<int>(tightSrc.size())) ? show : static_cast<int>(tightSrc.size());
    fprintf(stderr, "DUMP_TIGHT_HEAD :");
    for (int i = 0; i < tshow; ++i) fprintf(stderr, " %02x", tightSrc[i]);
    fprintf(stderr, "\n");
    int oshow = (show < static_cast<int>(outFrame.data.size())) ? show : static_cast<int>(outFrame.data.size());
    fprintf(stderr, "DUMP_OUT_HEAD :");
    for (int i = 0; i < oshow; ++i) fprintf(stderr, " %02x", outFrame.data[i]);
    fprintf(stderr, "\n");

    // Optional: dump small hex prefix for debugging when requested (stderr to avoid logger path)
    const char* dumpEnv = std::getenv("CAPSCR_DUMP_FRAMES");
    if (dumpEnv && dumpEnv[0] != '\0') {
        const uint8_t* p = outFrame.data.data();
        int prod = static_cast<int>(outFrame.data.size());
        int toShow = (64 < prod) ? 64 : prod;
    fprintf(stderr, "DUMP_MAP_PREFIX %p %dx%d :", (void*)p, outFrame.width, outFrame.height);
    for (int i = 0; i < toShow; ++i) fprintf(stderr, " %02x", p[i]);
    fprintf(stderr, "\n");
    // Also print tightSrc first bytes for comparison
    int toShowTight = (64 < static_cast<int>(tightSrc.size())) ? 64 : static_cast<int>(tightSrc.size());
    fprintf(stderr, "DUMP_TIGHT_PREFIX %p :", (void*)tightSrc.data());
    for (int i = 0; i < toShowTight; ++i) fprintf(stderr, " %02x", tightSrc[i]);
    fprintf(stderr, "\n");
    }

    // If diagnostics enabled, write raw diagnostics for mapped, tight, and out
    const char* dumpEnvAll = std::getenv("CAPSCR_DUMP_FRAMES");
    if (dumpEnvAll && dumpEnvAll[0] != '\0') {
        const char* dumpDirEnvLocal = std::getenv("CAPSCR_DUMP_DIR");
        if (dumpDirEnvLocal && dumpDirEnvLocal[0] != '\0') {
            // If mapped or out frame are zero (black), write raw dumps for offline analysis
            if (mappedNonZero == 0) {
                write_raw_dump_local(mapped.pData, mappedSize, "mapped_if_zero", width, height);
            }
            if (tightNonZero == 0) {
                write_raw_dump_local(tightSrc.data(), tightSrc.size(), "tight_if_zero", width, height);
            }
            if (outNonZero == 0) {
                write_raw_dump_local(outFrame.data.data(), outFrame.data.size(), "out_if_zero", outFrame.width, outFrame.height);
            }
        } else {
            fprintf(stderr, "event=dump_no_dir msg=\"CAPSCR_DUMP_DIR not set\"\n");
        }
    }
    // If requested, write full raw buffers for mapped, tightSrc and outFrame to aid diagnosis
    const char* dumpDirEnv2 = std::getenv("CAPSCR_DUMP_DIR");
    if (dumpEnv && dumpEnv[0] != '\0' && dumpDirEnv2 && dumpDirEnv2[0] != '\0') {
        char fname[1024];
        // mapped raw
        uintptr_t pmap = reinterpret_cast<uintptr_t>(mapped.pData);
        sprintf_s(fname, sizeof(fname), "%s/capscr_mapped_%zx_%ux%u.raw", dumpDirEnv2, (size_t)pmap, (unsigned)width, (unsigned)height);
        FILE* f = nullptr;
        fopen_s(&f, fname, "wb");
        if (f) {
            fwrite(mapped.pData, 1, mappedSize, f);
            fclose(f);
            fprintf(stderr, "DUMP_RAW_WRITTEN %s\n", fname);
        } else {
            fprintf(stderr, "DUMP_RAW_FAIL %s\n", fname);
        }

        // tightSrc raw
        uintptr_t ptight = reinterpret_cast<uintptr_t>(tightSrc.data());
        sprintf_s(fname, sizeof(fname), "%s/capscr_tight_%zx_%ux%u.raw", dumpDirEnv2, (size_t)ptight, (unsigned)width, (unsigned)height);
        f = nullptr;
        fopen_s(&f, fname, "wb");
        if (f) {
            fwrite(tightSrc.data(), 1, tightSrc.size(), f);
            fclose(f);
            fprintf(stderr, "DUMP_RAW_WRITTEN %s\n", fname);
        } else {
            fprintf(stderr, "DUMP_RAW_FAIL %s\n", fname);
        }

        // outFrame raw
        uintptr_t pout = reinterpret_cast<uintptr_t>(outFrame.data.data());
        sprintf_s(fname, sizeof(fname), "%s/capscr_out_%zx_%ux%u.raw", dumpDirEnv2, (size_t)pout, (unsigned)width, (unsigned)height);
        f = nullptr;
        fopen_s(&f, fname, "wb");
        if (f) {
            fwrite(outFrame.data.data(), 1, outFrame.data.size(), f);
            fclose(f);
            fprintf(stderr, "DUMP_RAW_WRITTEN %s\n", fname);
        } else {
            fprintf(stderr, "DUMP_RAW_FAIL %s\n", fname);
        }
    }

    context_->Unmap(stagingTexture.Get(), 0);
    
    CAPSCR_LOG_TRACE("Successfully copied texture data");
    
    return Result::Ok;
}

std::string DXGITexture::hrToString(HRESULT hr) {
    char buf[512] = {0};
    DWORD flags = FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS;
    DWORD len = FormatMessageA(flags, NULL, static_cast<DWORD>(hr), 
                              MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), 
                              buf, static_cast<DWORD>(sizeof(buf)), NULL);
    
    if (len == 0) {
        char tmp[64];
        sprintf_s(tmp, sizeof(tmp), "0x%08x", hr);
        return std::string(tmp);
    }
    
    while (len > 0 && (buf[len-1] == '\n' || buf[len-1] == '\r')) {
        buf[--len] = '\0';
    }
    
    return std::string(buf);
}

bool DXGITexture::validateTextureDesc(const D3D11_TEXTURE2D_DESC& desc) {
    // Check basic constraints
    if (desc.Width == 0 || desc.Height == 0) {
        CAPSCR_LOG_ERROR("Texture dimensions cannot be zero");
        return false;
    }

    if (desc.Width > 16384 || desc.Height > 16384) {
        CAPSCR_LOG_WARN("Texture dimensions are very large");
        // Continue anyway, let D3D decide
    }

    if (desc.MipLevels == 0) {
        CAPSCR_LOG_ERROR("MipLevels cannot be zero");
        return false;
    }

    if (desc.ArraySize == 0) {
        CAPSCR_LOG_ERROR("ArraySize cannot be zero");
        return false;
    }

    if (desc.SampleDesc.Count == 0) {
        CAPSCR_LOG_ERROR("SampleDesc.Count cannot be zero");
        return false;
    }

    return true;
}

} // namespace capscr

#endif // _WIN32
