# Standalone logging test executable
add_executable(standalone_logging_test
    standalone_logging_test.cpp
)

target_link_libraries(standalone_logging_test
    PRIVATE
    capscr_win
)

target_include_directories(standalone_logging_test 
    PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../src
)

target_compile_definitions(standalone_logging_test PRIVATE "WIN32_LEAN_AND_MEAN")

# Set output directory
set_target_properties(standalone_logging_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
)
