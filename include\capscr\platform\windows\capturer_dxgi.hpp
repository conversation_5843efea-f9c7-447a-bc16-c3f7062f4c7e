// capturer_dxgi.hpp - DXGI (Desktop Duplication) backend declaration
#pragma once

#include "../../capture.hpp"

#ifdef _WIN32
#include <windows.h>
#include <wrl.h>
#include <d3d11.h>

using Microsoft::WRL::ComPtr;

namespace capscr {

class DXGICapturer : public ICapturer {
public:
    DXGICapturer();
    ~DXGICapturer() override;

    Result init(CaptureTargetType type, const std::string& displayId = "", const Rect* optTarget = nullptr, const std::string& optWindowId = "") override;
    std::vector<DisplayInfo> listDisplays() override;
    BackendCapability capabilities() const override;
    Result setTarget(CaptureTargetType type, const std::string& displayId = "", const Rect* optTarget = nullptr, const std::string& optWindowId = "") override;
    Rect getTarget() const override;
    Result capture(Frame& outFrame) override;
    
    // Enhanced capture with image processing (resize + format conversion)
    Result capture(Frame& outFrame, const CaptureParams& params) override;
    
    // Zero-copy GPU texture capture
    Result captureGpu(GpuTexture& outTexture) override;
    bool isGpuCaptureAvailable() const override;
    // Zero-copy RAII capture
    Result captureZeroCopy(ZeroCopyFrame& outFrame, const ZeroCopyOptions& opts = {}, const CaptureParams* processingParams = nullptr) override;
    
    // Black frame handling
    void setBlackFrameConfig(const BlackFrameConfig& config) override;
    BlackFrameConfig getBlackFrameConfig() const override;
    
    void shutdown() override;

    // Called by WinEvent callback to notify instance that a window moved/resized
    void handleWinEventUpdate(HWND hwnd);

private:
    // Internal capture method for retry logic
    Result captureInternal(Frame& outFrame);
    
    // GPU processing methods
    Result captureWithGpuProcessing(Frame& outFrame, const CaptureParams& params);
    Result captureWithCpuProcessing(Frame& outFrame, const CaptureParams& params);
    Result captureGpuUnlocked(GpuTexture& outTexture);  // For use when mutex is already locked
    Result copyProcessedTextureToFrame(ComPtr<ID3D11Texture2D> texture, 
                                      const CaptureParams& params, 
                                      Frame& outFrame);
    
    // Implementation details hidden in .cpp
    struct Impl;
    Impl* pimpl = nullptr;
};

// Factory helper used by the overall createBestCapturer factory.
std::unique_ptr<ICapturer> createDXGICapturer();

} // namespace capscr

#endif // _WIN32
