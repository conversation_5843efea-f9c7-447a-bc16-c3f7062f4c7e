#pragma once
#include "test_window_helper.hpp"
#include <gtest/gtest.h>
#include <memory>
#include <vector>

/**
 * Global test environment for managing shared test windows across all tests
 * This avoids creating/destroying windows for each test, improving performance
 */
class CaptureTestEnvironment : public ::testing::Environment {
public:
    static CaptureTestEnvironment* Instance();
    
    // Called once before all tests
    void SetUp() override;
    
    // Called once after all tests
    void TearDown() override;
    
    // Get shared test windows
    TestWindow* GetPrimaryTestWindow() const { return primaryWindow_.get(); }
    TestWindow* GetSecondaryTestWindow() const { return secondaryWindow_.get(); }
    TestWindow* GetDynamicTestWindow() const { return dynamicWindow_.get(); }
    
    // Multi-monitor support
    struct MonitorInfo {
        HMONITOR handle;
        RECT rect;
        bool isPrimary;
        std::string deviceName;
    };
    
    std::vector<MonitorInfo> GetMonitors() const { return monitors_; }
    bool HasMultipleMonitors() const { return monitors_.size() > 1; }
    
    // Move window to specific monitor
    void MoveWindowToMonitor(TestWindow* window, int monitorIndex);
    
    // Minimize/restore window for testing
    void MinimizeWindow(TestWindow* window);
    void RestoreWindow(TestWindow* window);
    void CloseWindow(TestWindow* window);
    
    // Get desktop dimensions
    RECT GetPrimaryDesktopRect() const;
    RECT GetVirtualDesktopRect() const; // All monitors combined
    
    // Public constructor for Google Test
    CaptureTestEnvironment() = default;

private:
    static CaptureTestEnvironment* instance_;
    
    void EnumerateMonitors();
    static BOOL CALLBACK MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData);
    
    std::unique_ptr<TestWindow> primaryWindow_;   // Main test window
    std::unique_ptr<TestWindow> secondaryWindow_; // Second window for multi-window tests
    std::unique_ptr<TestWindow> dynamicWindow_;   // Window for movement/resize tests
    
    std::vector<MonitorInfo> monitors_;
    bool initialized_ = false;
};
