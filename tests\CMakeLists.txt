# Unit tests for capscr library

if(WIN32)
    # Test executable for Windows DXGI/GDI capture
    add_executable(capscr_tests
        test_main.cpp
        test_window_detection.cpp
    test_dxgi_zerocopy.cpp
        test_gdi_capture.cpp
        test_frame_utils.cpp
        test_window_helper.cpp
        test_comprehensive_capture.cpp
        capture_test_environment.cpp
        test_gpu_capture.cpp
    test_enhanced_capture_processing.cpp
    test_gdi_processing.cpp
    test_gpu_format_convert.cpp
    test_dxgi_capturer_nv12_zerocopy.cpp
    )
    
    target_link_libraries(capscr_tests
        PRIVATE
        capscr_win
        gtest
        gtest_main
        user32
        gdi32
        dxgi
        d3d11
    )
    
    target_include_directories(capscr_tests 
        PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../src
    )
    
    target_compile_definitions(capscr_tests PRIVATE "WIN32_LEAN_AND_MEAN")
    
    # Register tests with CTest
    include(GoogleTest)
    gtest_discover_tests(capscr_tests)
    
    # Ensure runtime shaders are available to tests. Copy HLSL shader sources to the tests runtime dir.
    file(GLOB CAPSCR_HLSL_SOURCES "${CMAKE_SOURCE_DIR}/src/windows/shaders/*.hlsl")
    
    if(CAPSCR_HLSL_SOURCES)
        # For tests, we'll copy HLSL sources directly since we don't need CSO compilation for fallback
        add_custom_command(TARGET capscr_tests POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:capscr_tests>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_HLSL_SOURCES} "$<TARGET_FILE_DIR:capscr_tests>/shaders"
            COMMENT "Copying HLSL shaders to tests runtime directory"
        )
    endif()
    
    # Standalone logging test executable (independent of main test environment)
    add_executable(standalone_logging_test
        standalone_logging_test.cpp
    )
    
    target_link_libraries(standalone_logging_test
        PRIVATE
        capscr_win
    )
    
    target_include_directories(standalone_logging_test 
        PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../src
    )
    
    target_compile_definitions(standalone_logging_test PRIVATE "WIN32_LEAN_AND_MEAN")
    
    # Set output directory
    set_target_properties(standalone_logging_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    # GPU/CPU processing validation test
    add_executable(test_gpu_cpu_processing
        test_gpu_cpu_processing.cpp
    )
    
    target_link_libraries(test_gpu_cpu_processing
        PRIVATE
        capscr_win
        user32
        gdi32
        dxgi
        d3d11
        d3dcompiler
    )
    
    target_include_directories(test_gpu_cpu_processing 
        PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../src
    )
    
    target_compile_definitions(test_gpu_cpu_processing PRIVATE "WIN32_LEAN_AND_MEAN")
    
    # Set output directory
    set_target_properties(test_gpu_cpu_processing PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    # Copy shaders for GPU processing test
    if(CAPSCR_HLSL_SOURCES)
        add_custom_command(TARGET test_gpu_cpu_processing POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:test_gpu_cpu_processing>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_HLSL_SOURCES} "$<TARGET_FILE_DIR:test_gpu_cpu_processing>/shaders"
            COMMENT "Copying HLSL shaders to GPU processing test runtime directory"
        )
    endif()
    
    # Simple diagnostic test
    add_executable(simple_test
        simple_test.cpp
    )
    
    target_link_libraries(simple_test
        PRIVATE
        capscr_win
        user32
        gdi32
        dxgi
        d3d11
        d3dcompiler
    )
    
    target_include_directories(simple_test 
        PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../src
    )
    
    target_compile_definitions(simple_test PRIVATE "WIN32_LEAN_AND_MEAN")
    
    # Set output directory
    set_target_properties(simple_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    # Copy shaders for simple test
    if(CAPSCR_HLSL_SOURCES)
        add_custom_command(TARGET simple_test POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:simple_test>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_HLSL_SOURCES} "$<TARGET_FILE_DIR:simple_test>/shaders"
            COMMENT "Copying HLSL shaders to simple test runtime directory"
        )
    endif()
    
    # Enhanced features validation test
    add_executable(validate_enhancements
        validate_enhancements.cpp
    )
    
    target_link_libraries(validate_enhancements
        PRIVATE
        capscr_win
        user32
        gdi32
        dxgi
        d3d11
        d3dcompiler
    )
    
    target_include_directories(validate_enhancements 
        PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../src
    )
    
    target_compile_definitions(validate_enhancements PRIVATE "WIN32_LEAN_AND_MEAN")
    
    # Set output directory
    set_target_properties(validate_enhancements PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
    )
    
    # Copy shaders for enhancement validation test
    if(CAPSCR_HLSL_SOURCES)
        add_custom_command(TARGET validate_enhancements POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:validate_enhancements>/shaders"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CAPSCR_HLSL_SOURCES} "$<TARGET_FILE_DIR:validate_enhancements>/shaders"
            COMMENT "Copying HLSL shaders to enhancement validation test runtime directory"
        )
    endif()
    
elseif(UNIX)
    # Placeholder for Linux/macOS tests
    message(STATUS "Unit tests for Unix platforms not yet implemented")
endif()
