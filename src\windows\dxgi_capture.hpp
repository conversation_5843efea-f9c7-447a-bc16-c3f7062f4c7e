#pragma once
#ifdef _WIN32

#include <windows.h>
#include <dxgi1_2.h>
#include <d3d11.h>
#include <wrl.h>
#include <string>
#include <memory>
#include <mutex>
#include <unordered_map>

#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/logging.hpp"
#include "dxgi_device.hpp"
#include "dxgi_texture.hpp"

using Microsoft::WRL::ComPtr;

namespace capscr {

/**
 * Manages the capture process, window tracking, and error recovery
 */
class DXGICapture {
public:
    DXGICapture();
    ~DXGICapture();

    // Initialize capture for target
    Result initialize(CaptureTargetType type, const std::string& displayId, 
                     const Rect* optTarget, const std::string& optWindowId);

    // Perform capture operation
    Result capture(Frame& outFrame);

    // GPU capture (zero-copy)
    Result captureGpu(GpuTexture& outTexture);
    // Zero-copy RAII capture (returns a ZeroCopyFrame that must be released)
    // Optional processingParams allows requesting a GPU/CPU processing pass
    // (resize/format-convert) and is allowed to allocate a new texture.
    Result captureZeroCopy(ZeroCopyFrame& outFrame, const ZeroCopyOptions& opts = {}, const CaptureParams* processingParams = nullptr);
    // Create a shared texture handle suitable for cross-process consumption
    Result captureSharedTexture(SharedTextureInfo& outInfo, const ZeroCopyOptions& opts = {});

    // Check if GPU capture is available
    bool isGpuCaptureAvailable() const;

    // Update target (for region capture)
    Result setTarget(CaptureTargetType type, const std::string& displayId, 
                    const Rect* optTarget, const std::string& optWindowId);

    // Get current target rect
    Rect getTarget() const;

    // Get capabilities
    BackendCapability getCapabilities() const;

    // Clean up resources
    void shutdown();

    // Handle window event updates (called by global callback)
    void handleWinEventUpdate(HWND hwnd);

private:
    // Core components
    std::unique_ptr<DXGIDevice> device_;
    std::unique_ptr<DXGITexture> textureManager_;
    
    // Duplication interface
    ComPtr<IDXGIOutputDuplication> duplication_;
    
    // Capture state
    CaptureTargetType targetType_;
    std::string displayId_;
    Rect targetRect_;
    int adapterIndex_;
    int outputIndex_;
    
    // Window tracking
    HWND targetHwnd_;
    HWINEVENTHOOK winEventHook_;
    int winEventCount_;
    
    // Thread safety
    mutable std::mutex mutex_;
    mutable std::mutex targetMutex_;

    // Error recovery
    static const int MAX_RECREATE_ATTEMPTS = 3;
    
    // Helper methods
    Result setupWindowTarget(const std::string& windowId);
    Result updateWindowRect();
    Result acquireFrame(DXGI_OUTDUPL_FRAME_INFO& frameInfo, ComPtr<IDXGIResource>& resource);
    Result handleDeviceLost();
    bool recreateDuplication();
    
    // Window utilities
    HWND findWindow(const std::string& windowId);
    Rect getWindowRect(HWND hwnd);
    bool isWindowValid(HWND hwnd);
    
    // GDI fallback
    Result gdiCapture(const Rect& region, Frame& outFrame);
    
    // Window event management
    void registerWindowEvents(HWND hwnd);
    void unregisterWindowEvents();
};

// Global window event handling
void RegisterGlobalWinEventHook();
void UnregisterGlobalWinEventHook();

} // namespace capscr

#endif // _WIN32
