#include <iostream>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main() {
    std::cout << "=== Minimal JSON Test ===" << std::endl;
    
    try {
        json test_json = {
            {"name", "test"},
            {"value", 42}
        };
        
        std::cout << "JSON created successfully: " << test_json.dump() << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cout << "Exception: " << e.what() << std::endl;
        return 1;
    }
}
