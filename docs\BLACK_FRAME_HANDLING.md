# DXGI 黑帧问题处理指南

## 问题背景

DXGI Desktop Duplication API 在某些情况下会返回全零（黑色）的帧数据，这是一个已知的 Windows 平台问题。黑帧通常由以下原因引起：

- **GPU 状态变化**：显卡驱动状态切换或重置
- **DWM 合成器问题**：Windows 桌面窗口管理器的瞬时性问题  
- **系统负载**：高 CPU/GPU 负载导致的帧获取延迟
- **窗口状态变化**：目标窗口被遮挡、最小化或失去焦点
- **显示器配置改变**：分辨率变化、多显示器切换等

## 黑帧检测机制

capscr 库实现了多层次的黑帧检测：

### 1. 早期检测（Tiny Probe）
```cpp
// 在完整帧处理前，先读取 1x1 像素样本
event=blackchk_acquire stage=tiny mapped_nonzero=0 total=128
```

### 2. 映射阶段检测
```cpp
// 检查 GPU 映射后的原始数据
BLACKCHK_MAPPED small mapped_nonzero=0 total=1128192
```

### 3. 完整帧验证
```cpp
// 验证最终输出帧的完整性
BLACKCHK_OUT small out_nonzero=0 total=564096
```

## API 使用指南

### 基本配置

```cpp
#include <capscr/capture.hpp>

auto capturer = capscr::createBestCapturer();

// 配置黑帧处理
capscr::BlackFrameConfig config;
config.enable_detection = true;        // 启用检测
config.enable_auto_retry = true;       // 自动重试
config.max_retries = 3;               // 最大重试次数
config.retry_delay_ms = 50;           // 重试间隔（毫秒）
config.black_threshold = 0.01f;       // 黑帧阈值（1%）
config.log_black_frames = true;       // 记录日志

capturer->setBlackFrameConfig(config);
```

### 处理捕获结果

```cpp
capscr::Frame frame;
capscr::Result result = capturer->capture(frame);

switch (result) {
    case capscr::Result::Ok:
        // 正常帧，继续处理
        processFrame(frame);
        break;
        
    case capscr::Result::BlackFrameDetected:
        // 检测到黑帧，但重试已耗尽
        // 可以选择跳过此帧或采取其他策略
        handleBlackFrame();
        break;
        
    case capscr::Result::RetryExhausted:
        // 所有重试尝试都失败了
        // 可能需要重置捕获器或降低帧率
        handleCaptureFailure();
        break;
        
    case capscr::Result::WindowMinimized:
        // 目标窗口被最小化
        waitForWindowRestore();
        break;
        
    // ... 其他错误处理
}
```

## 推荐配置策略

### 实时应用（如直播、录屏）
```cpp
config.enable_auto_retry = true;
config.max_retries = 2;              // 快速重试
config.retry_delay_ms = 33;          // 保持帧率
config.black_threshold = 0.005f;     // 更严格的检测
```

### 批处理应用（如截图工具）
```cpp
config.enable_auto_retry = true;
config.max_retries = 5;              // 更多重试
config.retry_delay_ms = 100;         // 允许更长延迟
config.black_threshold = 0.02f;      // 相对宽松的检测
```

### 高可靠性应用
```cpp
config.enable_auto_retry = true;
config.max_retries = 3;
config.retry_delay_ms = 50;
config.consecutive_failures_reset = 3; // 连续失败后重置
config.validate_tiny_probe = true;     // 启用所有验证
config.validate_full_frame = true;
```

## 错误恢复策略

### 1. 自动重试
库会自动处理瞬时性黑帧问题：
```cpp
// 内部实现会自动重试
for (int attempt = 0; attempt <= max_retries; ++attempt) {
    if (capture_successful && !is_black_frame) {
        return Result::Ok;
    }
    if (attempt < max_retries) {
        Sleep(retry_delay_ms);
        continue;
    }
}
return Result::BlackFrameDetected;
```

### 2. 捕获器重置
连续多次失败时重置 DXGI 资源：
```cpp
void resetCapturer() {
    capturer->shutdown();
    capturer = createBestCapturer();
    capturer->setBlackFrameConfig(config);
    capturer->init(targetType, displayId, &target, windowId);
}
```

### 3. 降级策略
DXGI 失败时使用 GDI 后备：
```cpp
if (result == Result::RetryExhausted) {
    // 内部会自动尝试 GDI 捕获
    // 或者应用层可以切换到其他捕获方法
}
```

## 性能优化建议

### 1. 调整检测灵敏度
```cpp
// 对于要求不太严格的场景，可以提高阈值
config.black_threshold = 0.05f;  // 5% 而不是 1%
```

### 2. 优化重试策略
```cpp
// 根据应用场景调整重试参数
if (isLowLatencyRequired) {
    config.max_retries = 1;
    config.retry_delay_ms = 16;  // 约 60 FPS
} else {
    config.max_retries = 3;
    config.retry_delay_ms = 50;
}
```

### 3. 监控和统计
```cpp
// 记录黑帧统计信息
struct CaptureStats {
    int totalFrames = 0;
    int blackFrames = 0;
    int retries = 0;
    
    float getBlackFrameRate() const {
        return totalFrames > 0 ? (float)blackFrames / totalFrames : 0.0f;
    }
};
```

## 调试和诊断

### 1. 启用详细日志
```cpp
config.log_black_frames = true;
```

输出示例：
```
Black frame detected (attempt 1/3), consecutive=1
event=raw_written file=C:/dumps/mapped_small_if_zero_1e5668c0000_339x339.raw
```

### 2. 原始数据分析
设置环境变量以保存原始帧数据：
```bash
export CAPSCR_DUMP_FRAMES=1
export CAPSCR_DUMP_DIR="/path/to/dumps"
```

生成的文件：
- `acquire_tiny_mapped_*.raw` - 1x1 探测数据
- `mapped_small_if_zero_*.raw` - 映射的原始数据
- `out_small_if_zero_*.raw` - 处理后的输出数据

### 3. 性能监控
```cpp
auto start = std::chrono::steady_clock::now();
Result result = capturer->capture(frame);
auto duration = std::chrono::steady_clock::now() - start;

if (duration > std::chrono::milliseconds(100)) {
    // 捕获时间过长，可能有性能问题
}
```

## 常见问题解决

### Q: 黑帧频率过高怎么办？
A: 
1. 检查系统负载，降低其他应用的 GPU 使用
2. 增加重试次数和延迟
3. 考虑降低捕获帧率
4. 检查是否有显卡驱动问题

### Q: 重试延迟影响实时性怎么办？
A:
1. 减少 `max_retries` 和 `retry_delay_ms`
2. 提高 `black_threshold` 以减少误检
3. 使用异步捕获模式（如果可用）

### Q: 如何确定最佳配置参数？
A:
1. 在目标环境中运行测试，收集统计数据
2. 根据应用的延迟要求调整参数
3. 监控黑帧率和重试成功率
4. 进行 A/B 测试找到最佳平衡点

## 最佳实践总结

1. **始终启用黑帧检测**：现代 DXGI 应用应该默认处理黑帧问题
2. **根据场景调整参数**：实时应用vs批处理应用需要不同的配置
3. **实施监控和统计**：跟踪黑帧频率以优化配置
4. **准备降级策略**：在 DXGI 不可用时有备选方案
5. **测试多种环境**：不同的硬件和系统配置可能有不同表现
6. **定期更新驱动**：保持显卡驱动程序为最新版本

通过正确配置和使用这些功能，应用可以有效地处理 DXGI 黑帧问题，提供稳定可靠的屏幕捕获体验。
