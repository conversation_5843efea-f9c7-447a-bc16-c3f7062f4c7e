#include <gtest/gtest.h>
#include <memory>
#include <chrono>
#include <thread>
#include "capscr/capture.hpp"
#include "capscr/logging.hpp"
#ifdef _WIN32
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include <d3d11.h>
#include <dxgi1_2.h>
#endif

class GpuCaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Don't initialize logging here since it's handled by global environment
        
        // Create DXGI capturer for testing
#ifdef _WIN32
        capturer = capscr::createDXGICapturer();
        if (capturer) {
            auto result = capturer->init(capscr::CaptureTargetType::FullScreen, "", nullptr, "");
            init_successful = (result == capscr::Result::Ok);
        }
#endif
    }

    void TearDown() override {
        capturer.reset();
        // Don't shutdown logging here since it's shared
    }

    std::unique_ptr<capscr::ICapturer> capturer;
    bool init_successful = false;
};

#ifdef _WIN32

// Test GPU capture availability detection
TEST_F(GpuCaptureTest, GpuCaptureAvailability) {
    if (!capturer) {
        GTEST_SKIP() << "DXGI capturer not available on this system";
    }
    
    if (!init_successful) {
        GTEST_SKIP() << "Failed to initialize DXGI capturer";
    }
    
    // Test availability check
    bool available = capturer->isGpuCaptureAvailable();
    
    // On systems with DXGI support, GPU capture should be available after init
    EXPECT_TRUE(available) << "GPU capture should be available after successful DXGI initialization";
}

// Test GPU capture before initialization
TEST_F(GpuCaptureTest, GpuCaptureBeforeInit) {
    auto fresh_capturer = capscr::createDXGICapturer();
    if (!fresh_capturer) {
        GTEST_SKIP() << "DXGI capturer not available";
    }
    
    // Before init, GPU capture should not be available
    EXPECT_FALSE(fresh_capturer->isGpuCaptureAvailable());
    
    // Attempt to capture should fail gracefully
    capscr::GpuTexture texture;
    auto result = fresh_capturer->captureGpu(texture);
    EXPECT_NE(result, capscr::Result::Ok);
}

// Test basic GPU texture capture
TEST_F(GpuCaptureTest, BasicGpuCapture) {
    if (!capturer || !init_successful) {
        GTEST_SKIP() << "DXGI capturer not properly initialized";
    }
    
    if (!capturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available on this system";
    }
    
    capscr::GpuTexture gpu_texture;
    auto result = capturer->captureGpu(gpu_texture);
    
    // Note: Result might be Error if no screen updates, which is normal
    if (result == capscr::Result::Ok) {
        // Verify GPU texture properties
        EXPECT_GT(gpu_texture.width, 0);
        EXPECT_GT(gpu_texture.height, 0);
        EXPECT_NE(gpu_texture.d3d11_device, nullptr);
        EXPECT_NE(gpu_texture.d3d11_context, nullptr);
        EXPECT_NE(gpu_texture.d3d11_texture, nullptr);
        EXPECT_TRUE(gpu_texture.is_valid());
        EXPECT_EQ(gpu_texture.format, capscr::PixelFormat::BGRA32);
        
        // Clean up GPU resources
        if (gpu_texture.d3d11_texture) gpu_texture.d3d11_texture->Release();
        if (gpu_texture.d3d11_context) gpu_texture.d3d11_context->Release();
        if (gpu_texture.d3d11_device) gpu_texture.d3d11_device->Release();
        
        CAPSCR_LOG_INFO("GPU texture capture test completed successfully");
    } else {
        CAPSCR_LOG_WARN("GPU capture returned non-OK result, which may be normal if no screen updates");
        // This is not necessarily a failure - just means no new frame was available
    }
}

// Test GPU texture validation
TEST_F(GpuCaptureTest, GpuTextureValidation) {
    capscr::GpuTexture empty_texture;
    
    // Empty texture should not be valid
    EXPECT_FALSE(empty_texture.is_valid());
    EXPECT_EQ(empty_texture.d3d11_device, nullptr);
    EXPECT_EQ(empty_texture.d3d11_context, nullptr);
    EXPECT_EQ(empty_texture.d3d11_texture, nullptr);
    EXPECT_EQ(empty_texture.width, 0);
    EXPECT_EQ(empty_texture.height, 0);
}

// Test GPU capture with screen activity
TEST_F(GpuCaptureTest, GpuCaptureWithActivity) {
    if (!capturer || !init_successful || !capturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available";
    }
    
    // Try to generate some screen activity by moving cursor (if possible)
    // This is a best-effort attempt to trigger a screen update
    
    capscr::GpuTexture gpu_texture;
    
    // Try capture multiple times to increase chance of catching a screen update
    capscr::Result best_result = capscr::Result::Error;
    for (int attempt = 0; attempt < 5; ++attempt) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        auto result = capturer->captureGpu(gpu_texture);
        if (result == capscr::Result::Ok) {
            best_result = result;
            break;
        }
        
        // Update best_result if we get a better error code
        if (static_cast<int>(result) < static_cast<int>(best_result)) {
            best_result = result;
        }
    }
    
    if (best_result == capscr::Result::Ok) {
        EXPECT_TRUE(gpu_texture.is_valid());
        EXPECT_GT(gpu_texture.width, 0);
        EXPECT_GT(gpu_texture.height, 0);
        
        // Clean up
        if (gpu_texture.d3d11_texture) gpu_texture.d3d11_texture->Release();
        if (gpu_texture.d3d11_context) gpu_texture.d3d11_context->Release();
        if (gpu_texture.d3d11_device) gpu_texture.d3d11_device->Release();
    } else {
        CAPSCR_LOG_DEBUG("No screen updates captured during test, result: " + std::to_string(static_cast<int>(best_result)));
        // This is acceptable - screen might not have updates during test
    }
}

// Test ZeroCopyGpu capability reporting
TEST_F(GpuCaptureTest, ZeroCopyCapability) {
    if (!capturer || !init_successful) {
        GTEST_SKIP() << "DXGI capturer not properly initialized";
    }
    
    auto capabilities = capturer->capabilities();
    
    // DXGI capturer should support zero-copy GPU if properly initialized
    bool has_zero_copy = capscr::backendHas(capabilities, capscr::BackendCapability::ZeroCopyGpu);
    
    if (capturer->isGpuCaptureAvailable()) {
        EXPECT_TRUE(has_zero_copy) << "Zero-copy GPU capability should be reported when GPU capture is available";
    }
}

// Test multiple GPU captures
TEST_F(GpuCaptureTest, MultipleGpuCaptures) {
    if (!capturer || !init_successful || !capturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available";
    }
    
    // Perform multiple captures to test stability
    int successful_captures = 0;
    
    for (int i = 0; i < 3; ++i) {
        capscr::GpuTexture gpu_texture;
        auto result = capturer->captureGpu(gpu_texture);
        
        if (result == capscr::Result::Ok) {
            successful_captures++;
            EXPECT_TRUE(gpu_texture.is_valid());
            
            // Clean up
            if (gpu_texture.d3d11_texture) gpu_texture.d3d11_texture->Release();
            if (gpu_texture.d3d11_context) gpu_texture.d3d11_context->Release();
            if (gpu_texture.d3d11_device) gpu_texture.d3d11_device->Release();
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    CAPSCR_LOG_INFO("Completed " + std::to_string(successful_captures) + " successful GPU captures out of 3 attempts");
    
    // At least the interface should work without crashing
    // (successful captures depend on screen activity)
}

// Test GPU capture vs traditional capture comparison
TEST_F(GpuCaptureTest, GpuVsTraditionalCapture) {
    if (!capturer || !init_successful) {
        GTEST_SKIP() << "DXGI capturer not properly initialized";
    }
    
    // Try traditional capture first
    capscr::Frame traditional_frame;
    auto traditional_result = capturer->capture(traditional_frame);
    
    if (traditional_result != capscr::Result::Ok) {
        GTEST_SKIP() << "Traditional capture failed, cannot compare";
    }
    
    EXPECT_GT(traditional_frame.width, 0);
    EXPECT_GT(traditional_frame.height, 0);
    EXPECT_GT(traditional_frame.data.size(), 0);
    
    // Try GPU capture
    if (capturer->isGpuCaptureAvailable()) {
        capscr::GpuTexture gpu_texture;
        auto gpu_result = capturer->captureGpu(gpu_texture);
        
        if (gpu_result == capscr::Result::Ok) {
            // Dimensions should match between traditional and GPU capture
            EXPECT_EQ(gpu_texture.width, traditional_frame.width);
            EXPECT_EQ(gpu_texture.height, traditional_frame.height);
            EXPECT_EQ(gpu_texture.format, traditional_frame.format);
            
            // Clean up
            if (gpu_texture.d3d11_texture) gpu_texture.d3d11_texture->Release();
            if (gpu_texture.d3d11_context) gpu_texture.d3d11_context->Release();
            if (gpu_texture.d3d11_device) gpu_texture.d3d11_device->Release();
        }
    }
}

#else

// Placeholder tests for non-Windows platforms
TEST_F(GpuCaptureTest, GpuCaptureNotAvailableOnNonWindows) {
    // On non-Windows platforms, GPU capture should not be available
    EXPECT_FALSE(capturer);
}

#endif
