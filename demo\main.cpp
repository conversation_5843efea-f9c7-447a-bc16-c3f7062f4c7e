// -*- coding: utf-8 -*-
// capscr ImGui Demo - UI Interface Only
// This version focuses on UI design and interaction; capscr library is not required
// Implements a full parameters panel and preview area per design

#include <iostream>
#include <vector>
#include <string>
#include <chrono>
#include <memory>
#include <map>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <climits>

// capscr (only when available)
#ifdef HAVE_CAPSCR
#include "capscr/capture.hpp"
#endif

#ifdef _WIN32
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>
#include <psapi.h>
#include <gdiplus.h>
#pragma comment(lib, "gdiplus")
#endif

// Forward-declare helper (defined later) - only after windows.h so HWND is defined
#ifdef _WIN32
static void SetWindowIconFromExeDir(HWND hWnd);
#endif

// ImGui / GLFW 
#include <GLFW/glfw3.h>
#include "imgui.h"
#include "imgui_impl_glfw.h"

#ifdef _WIN32
#include <d3d11.h>
#include "imgui_impl_dx11.h"
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3native.h>
#pragma comment(lib, "d3d11")
#pragma comment(lib, "dxgi")
#else
#include "imgui_impl_opengl3.h"
#endif

// 模拟数据结构
struct Rect {
    int x, y, width, height;
};

struct VirtualScreen {
    std::string name;
    int width, height;
    int x, y;
    bool selected;
};

struct WindowInfo {
    std::string title;
    std::string process;
    int pid;
    bool selected;
#ifdef _WIN32
    HWND hwnd;  // 添加窗口句柄用于精确识别
#endif
};

// 全局UI状态
struct UIState {
    // 参数设置
    bool preferGpuZeroCopy = false;
    std::string backendCaps = "0x00000001";
    std::string status = "Showing primary screen (auto)";
    
    // Capture methods (single-choice)
    int captureMethod = 0; // default to 0 (GDI) — platform-specific UI will expose appropriate options
    
    // Virtual screens
    std::vector<VirtualScreen> virtualScreens;
    int selectedScreen = 0;
    
    // Selection box related
    bool isDraggingSelection = false;
    bool hasSelection = false;
    Rect selectionBox = {0, 0, 0, 0}; // Selection box in virtual-screen coordinates
    ImVec2 dragStartPos = {0, 0}; // Drag start position in UI coordinates
    // Click detection (used to differentiate click vs drag)
    bool potentialClick = false;
    ImVec2 clickPos = {0,0}; // Click position relative to the canvas
    // Record the starting screen index during drag to constrain selection to one screen
    int dragScreenIndex = -1;
    // Index of the screen owning the selection box (persisted after mouse release)
    int selectionScreenIndex = -1;
    
    // Window list
    std::vector<WindowInfo> windows;
    int selectedWindow = -1;
    
    // Window list refresh timer
    std::chrono::steady_clock::time_point lastWindowRefresh = std::chrono::steady_clock::now();
    float windowRefreshInterval = 3.0f; // 每3秒刷新一次窗口列表
    
    // Preview state
    std::string previewText = "Preview (shows only the selected content: screen/window/region)";
    std::string fpsInfo = "FPS: 59.0 | Res: 1920x1080 | GPU: 12%";
    
    // Performance statistics
    float captureTime = 0.0f;
    int frameCount = 0;
    
    // GDI capture related
    bool isCapturing = false;
    std::vector<uint8_t> currentFrame; // RGBA frame data
    int frameWidth = 0;
    int frameHeight = 0;
    bool previewTextureNeedsUpdate = false;
} g_uiState;

#ifdef _WIN32
// D3D11 数据
static ID3D11Device*            g_pd3dDevice = nullptr;
static ID3D11DeviceContext*     g_pd3dDeviceContext = nullptr;
static IDXGISwapChain*          g_pSwapChain = nullptr;
static ID3D11RenderTargetView*  g_mainRenderTargetView = nullptr;

// 预览纹理
static ID3D11Texture2D*         g_previewTexture = nullptr;
static ID3D11ShaderResourceView* g_previewSRV = nullptr;

// GDI Capture Implementation using capscr library
#ifdef HAVE_CAPSCR
static std::unique_ptr<capscr::ICapturer> g_capturer;
static capscr::Frame g_currentFrame;
static bool g_frameReady = false;
static std::chrono::steady_clock::time_point g_lastCaptureTime;

bool InitializeCapturer() {
    if (!g_capturer) {
    // Create the appropriate capturer based on UI selection
        #ifdef _WIN32
        if (g_uiState.captureMethod == 0) {
            // GDI
            g_capturer = capscr::createGdiCapturer();
            std::cout << "Using GDI capturer" << std::endl;
        } else if (g_uiState.captureMethod == 1) {
            // DXGI
            g_capturer = capscr::createDxgiCapturer();
            std::cout << "Using DXGI capturer" << std::endl;
        } else {
            // fallback to best
            g_capturer = capscr::createBestCapturer();
            std::cout << "Using best available capturer" << std::endl;
        }
        #elif defined(__linux__)
        if (g_uiState.captureMethod == 2) {
            // X11
            g_capturer = capscr::createX11Capturer();
            std::cout << "Using X11 capturer" << std::endl;
        } else if (g_uiState.captureMethod == 3) {
            // Wayland
            g_capturer = capscr::createWaylandCapturer();
            std::cout << "Using Wayland capturer" << std::endl;
        } else {
            g_capturer = capscr::createBestCapturer();
            std::cout << "Using best available capturer" << std::endl;
        }
        #elif defined(__APPLE__)
        if (g_uiState.captureMethod == 4) {
            // Quartz
            g_capturer = capscr::createQuartzCapturer();
            std::cout << "Using Quartz capturer" << std::endl;
        } else {
            g_capturer = capscr::createBestCapturer();
            std::cout << "Using best available capturer" << std::endl;
        }
        #else
        g_capturer = capscr::createBestCapturer();
        std::cout << "Using best available capturer" << std::endl;
        #endif
        
        if (!g_capturer) {
            std::cout << "Failed to create capturer" << std::endl;
            return false;
        }
    }
    return true;
}

bool SetupCaptureTarget() {
    if (!g_capturer) return false;
    
    capscr::CaptureTargetType targetType;
    std::string displayId;
    capscr::Rect targetRect{};
    std::string windowId;
    
    // 根据UI状态确定捕获目标
    if (g_uiState.hasSelection && g_uiState.selectionScreenIndex >= 0) {
        // 区域捕获
        targetType = capscr::CaptureTargetType::Region;
        if (g_uiState.selectionScreenIndex < g_uiState.virtualScreens.size()) {
            const auto& screen = g_uiState.virtualScreens[g_uiState.selectionScreenIndex];
            displayId = screen.name;
            targetRect.x = screen.x + g_uiState.selectionBox.x;
            targetRect.y = screen.y + g_uiState.selectionBox.y;
            targetRect.width = g_uiState.selectionBox.width;
            targetRect.height = g_uiState.selectionBox.height;
        }
    } else if (g_uiState.selectedWindow >= 0) {
        // 窗口捕获
        targetType = capscr::CaptureTargetType::Window;
        if (g_uiState.selectedWindow < g_uiState.windows.size()) {
            // 使用窗口句柄作为windowId
#ifdef _WIN32
            HWND hwnd = g_uiState.windows[g_uiState.selectedWindow].hwnd;
            std::ostringstream oss;
            oss << "0x" << std::hex << (uintptr_t)hwnd;
            windowId = oss.str();
            std::cout << "Targeting window: " << g_uiState.windows[g_uiState.selectedWindow].title 
                     << " (HWND: " << windowId << ")" << std::endl;
#else
            // 对于非Windows平台，仍使用标题
            windowId = g_uiState.windows[g_uiState.selectedWindow].title;
#endif
        }
    } else {
        // 全屏捕获
        targetType = capscr::CaptureTargetType::FullScreen;
    // Find the selected virtual screen
        for (size_t i = 0; i < g_uiState.virtualScreens.size(); ++i) {
            const auto& screen = g_uiState.virtualScreens[i];
            if (screen.selected) {
                displayId = screen.name;
                std::cout << "Targeting display: " << displayId 
                         << " (" << screen.width << "x" << screen.height 
                         << " at " << screen.x << "," << screen.y << ")" << std::endl;
                break;
            }
        }
        if (displayId.empty() && !g_uiState.virtualScreens.empty()) {
            // 如果没有选中的屏幕，默认使用第一个
            displayId = g_uiState.virtualScreens[0].name;
            std::cout << "No display selected, using first display: " << displayId << std::endl;
        }
    }
    
    auto result = g_capturer->init(targetType, displayId, 
                                  targetType == capscr::CaptureTargetType::Region ? &targetRect : nullptr,
                                  windowId);
    
    if (result != capscr::Result::Ok) {
        std::cout << "Failed to initialize capture target: " << (int)result << std::endl;
        return false;
    }
    
    std::cout << "Capture target initialized successfully" << std::endl;
    return true;
}

bool CaptureFrame() {
    if (!g_capturer) return false;
    
    auto result = g_capturer->capture(g_currentFrame);
    if (result == capscr::Result::Ok) {
        g_frameReady = true;
        g_lastCaptureTime = std::chrono::steady_clock::now();
        
        // 更新UI状态
        g_uiState.frameWidth = g_currentFrame.width;
        g_uiState.frameHeight = g_currentFrame.height;
        g_uiState.previewTextureNeedsUpdate = true;
        
        return true;
    } else {
        std::cout << "Capture failed: " << (int)result << std::endl;
        return false;
    }
}
#else
// Fallback for non-capscr builds
bool InitializeCapturer() { return false; }
bool SetupCaptureTarget() { return false; }
bool CaptureFrame() { return false; }
#endif

bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
void UpdatePreviewTexture();
void CleanupPreviewTexture();
#endif

static void glfw_error_callback(int error, const char* description)
{
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// 开始捕获
void StartCapture() {
    if (g_uiState.isCapturing) return;
    
#ifdef HAVE_CAPSCR
    // 重新创建捕获器以确保使用最新选择的方法
    g_capturer.reset();
    
    if (!InitializeCapturer()) {
        g_uiState.status = "Failed to initialize capturer";
        return;
    }
    
    if (!SetupCaptureTarget()) {
        g_uiState.status = "Failed to setup capture target";
        return;
    }
    
    g_uiState.isCapturing = true;
    g_uiState.status = "Capturing...";
    std::cout << "Started capscr capture" << std::endl;
#else
    g_uiState.status = "capscr not available";
#endif
}

// 停止捕获
void StopCapture() {
    if (!g_uiState.isCapturing) return;
    
    g_uiState.isCapturing = false;
    g_uiState.status = "Capture stopped";
    g_uiState.currentFrame.clear();
    g_uiState.frameWidth = 0;
    g_uiState.frameHeight = 0;
    
#ifdef HAVE_CAPSCR
    g_frameReady = false;
#endif
    
    std::cout << "Stopped capture" << std::endl;
}

// 执行一次捕获并更新性能统计
bool ExecuteCapture() {
    if (!g_uiState.isCapturing) return false;
    
#ifdef HAVE_CAPSCR
    auto start = std::chrono::high_resolution_clock::now();
    bool success = CaptureFrame();
    auto end = std::chrono::high_resolution_clock::now();
    
    if (success) {
        g_uiState.captureTime = std::chrono::duration<float, std::milli>(end - start).count();
        g_uiState.frameCount++;
        
        // 更新FPS信息
        static auto lastUpdate = std::chrono::high_resolution_clock::now();
        static int framesSinceUpdate = 0;
        framesSinceUpdate++;
        
        auto now = std::chrono::high_resolution_clock::now();
        auto elapsed = std::chrono::duration<float>(now - lastUpdate).count();
        if (elapsed >= 1.0f) {
            float fps = framesSinceUpdate / elapsed;
            g_uiState.fpsInfo = "FPS: " + std::to_string((int)fps) + 
                              " | Res: " + std::to_string(g_uiState.frameWidth) + "x" + std::to_string(g_uiState.frameHeight) + 
                              " | Cap: " + std::to_string(g_uiState.captureTime) + "ms";
            framesSinceUpdate = 0;
            lastUpdate = now;
        }
    }
    
    return success;
#else
    return false;
#endif
}

// 保存当前帧为PNG
void SaveCurrentFrameAsPNG() {
#ifdef HAVE_CAPSCR
    if (!g_frameReady || g_currentFrame.data.empty()) {
        std::cout << "No frame to save" << std::endl;
        return;
    }
    
    // 使用GDI+保存PNG
    static bool gdiPlusInitialized = false;
    static ULONG_PTR gdiplusToken = 0;
    
    if (!gdiPlusInitialized) {
        Gdiplus::GdiplusStartupInput gdiplusStartupInput;
        if (Gdiplus::GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL) == Gdiplus::Ok) {
            gdiPlusInitialized = true;
        } else {
            std::cout << "Failed to initialize GDI+" << std::endl;
            return;
        }
    }
    
    // 创建时间戳文件名
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << "capscr_capture_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S") 
        << "_" << std::setfill('0') << std::setw(3) << ms.count() << ".png";
    std::string filename = oss.str();
    
    // 创建Bitmap
    Gdiplus::Bitmap bitmap(g_currentFrame.width, g_currentFrame.height, PixelFormat32bppARGB);
    Gdiplus::BitmapData bitmapData;
    Gdiplus::Rect rect(0, 0, g_currentFrame.width, g_currentFrame.height);
    
    if (bitmap.LockBits(&rect, Gdiplus::ImageLockModeWrite, PixelFormat32bppARGB, &bitmapData) == Gdiplus::Ok) {
        // 复制像素数据 (BGRA -> ARGB for GDI+)
        uint8_t* src = g_currentFrame.data.data();
        uint8_t* dst = (uint8_t*)bitmapData.Scan0;
        
        for (int y = 0; y < g_currentFrame.height; ++y) {
            for (int x = 0; x < g_currentFrame.width; ++x) {
                int srcIdx = (y * g_currentFrame.width + x) * 4;
                int dstIdx = (y * bitmapData.Stride) + (x * 4);
                
                // capscr Frame is BGRA, GDI+ expects ARGB
                dst[dstIdx + 0] = src[srcIdx + 0]; // B
                dst[dstIdx + 1] = src[srcIdx + 1]; // G  
                dst[dstIdx + 2] = src[srcIdx + 2]; // R
                dst[dstIdx + 3] = src[srcIdx + 3]; // A
            }
        }
        
        bitmap.UnlockBits(&bitmapData);
        
        // 保存为PNG
        CLSID pngClsid;
        CLSIDFromString(L"{557CF406-1A04-11D3-9A73-0000F81EF32E}", &pngClsid);
        
        std::wstring wfilename(filename.begin(), filename.end());
        if (bitmap.Save(wfilename.c_str(), &pngClsid) == Gdiplus::Ok) {
            std::cout << "Saved screenshot: " << filename << std::endl;
            g_uiState.status = "Saved " + filename;
        } else {
            std::cout << "Failed to save screenshot" << std::endl;
            g_uiState.status = "Failed to save PNG";
        }
    }
#else
    std::cout << "capscr not available" << std::endl;
#endif
}

// 初始化模拟数据
void InitializeSimulatedData() {
    // Initialize simulated virtual screens
    g_uiState.virtualScreens = {
        {"Screen 1 (selected)", 3440, 1440, 0, 0, true},
        {"Screen 2", 1920, 1080, 3440, 0, false}
    };
    
    // 初始化窗口列表
    g_uiState.windows = {
        {"Calculator.exe", "Calculator", 12345, false},
        {"Browser - capscr example", "Chrome", 67890, false},
        {"Explorer", "Explorer", 11111, false},
        {"Notepad", "Notepad", 22222, false}
    };
}

// Populate virtualScreens from capscr listDisplays() and enumerate top-level windows on Windows
void EnumerateDisplaysAndWindows() {
    // Save current virtual-screen selection state
    std::vector<bool> prevScreenSelected;
    for (const auto& vs : g_uiState.virtualScreens) {
        prevScreenSelected.push_back(vs.selected);
    }
    
    // Clear current lists
    g_uiState.virtualScreens.clear();
    g_uiState.windows.clear();

#ifdef HAVE_CAPSCR
    // Use global capturer for consistency
    if (!g_capturer) {
        g_capturer = capscr::createBestCapturer();
    }

    if (g_capturer) {
        try {
            auto displays = g_capturer->listDisplays();
            for (size_t i = 0; i < displays.size(); ++i) {
                const auto &d = displays[i];
                capscr::Rect b = d.bounds;
                VirtualScreen vs;
                vs.name = d.name.empty() ? d.id : d.name;
                vs.width = b.width;
                vs.height = b.height;
                vs.x = b.x;
                vs.y = b.y;
                vs.selected = (i < prevScreenSelected.size()) ? prevScreenSelected[i] : (i == 0);
                g_uiState.virtualScreens.push_back(vs);
            }
        } catch (...) {
            std::cout << "Failed to enumerate displays, using fallback" << std::endl;
            // ignore enumeration errors and fall back to simulated
        }
    }
#endif

#ifdef _WIN32
    // Enumerate top-level windows (simple approach)
    struct EnumData { std::vector<WindowInfo>* windows; } ed;
    ed.windows = &g_uiState.windows;

    auto enumProc = [](HWND hwnd, LPARAM lparam) -> BOOL {
        EnumData* d = reinterpret_cast<EnumData*>(lparam);
        // Skip invisible or cloaked windows
        if (!IsWindowVisible(hwnd)) return TRUE;

        // Use wide-character APIs to correctly handle Unicode window titles
        wchar_t wtitle[512] = {0};
        GetWindowTextW(hwnd, wtitle, (int)_countof(wtitle));
        if (wcslen(wtitle) == 0) return TRUE;

        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);

        std::wstring wprocessName;
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION | PROCESS_VM_READ, FALSE, pid);
        if (hProcess) {
            HMODULE hMod = NULL;
            DWORD cbNeeded = 0;
            if (EnumProcessModules(hProcess, &hMod, sizeof(hMod), &cbNeeded)) {
                wchar_t wproc[256] = {0};
                // Try wide version first
                if (GetModuleBaseNameW(hProcess, hMod, wproc, (int)_countof(wproc)) > 0) {
                    wprocessName = wproc;
                }
            }
            CloseHandle(hProcess);
        }

        // Convert wide strings to UTF-8 for ImGui
        auto WideToUtf8 = [](const std::wstring &ws) -> std::string {
            if (ws.empty()) return std::string();
            // Use -1 to include the terminating null so WideCharToMultiByte returns required buffer size
            int n = WideCharToMultiByte(CP_UTF8, 0, ws.c_str(), -1, NULL, 0, NULL, NULL);
            if (n <= 0) return std::string();
            std::string out(n, '\0');
            int written = WideCharToMultiByte(CP_UTF8, 0, ws.c_str(), -1, &out[0], n, NULL, NULL);
            if (written > 0) {
                // remove the terminating null from std::string length
                out.resize(written - 1);
            } else {
                out.clear();
            }
            return out;
        };

        WindowInfo wi;
        wi.title = WideToUtf8(std::wstring(wtitle));
        if (!wprocessName.empty()) wi.process = WideToUtf8(wprocessName);
        else wi.process = std::string("Unknown");
        wi.pid = (int)pid;
        wi.selected = false;
#ifdef _WIN32
        wi.hwnd = hwnd;  // 保存窗口句柄
#endif
        d->windows->push_back(wi);
        return TRUE;
    };

    EnumWindows((WNDENUMPROC)enumProc, (LPARAM)&ed);
#endif

    // Fallback: if nothing found, keep simulated data
    if (g_uiState.virtualScreens.empty()) {
        InitializeSimulatedData();
    }
    if (g_uiState.windows.empty()) {
        // keep whatever simulated windows existed
    }
}

int main(int argc, char** argv)
{
    // 初始化模拟数据
    InitializeSimulatedData();

    // 初始化 GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit())
        return 1;

#ifdef _WIN32
    // 对于Windows，我们不需要OpenGL上下文，因为将使用D3D11
    glfwWindowHint(GLFW_CLIENT_API, GLFW_NO_API);
#else
    // 对于其他平台，使用OpenGL
    const char* glsl_version = "#version 330";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
#endif

    // 创建窗口 (固定大小，不可调整)
    glfwWindowHint(GLFW_RESIZABLE, GLFW_FALSE);
    int window_width = 1280;
    int window_height = 720;
    // 创建窗口
    GLFWwindow* window = glfwCreateWindow(window_width, window_height, "capscr Demo - UI Interface", NULL, NULL);
    if (window == NULL) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        return 1;
    }

#ifdef _WIN32
    // 初始化D3D11
    if (!CreateDeviceD3D(glfwGetWin32Window(window))) {
        CleanupDeviceD3D();
        glfwDestroyWindow(window);
        glfwTerminate();
        return 1;
    }

    // 尝试为窗口设置自定义图标（如果可用）
    SetWindowIconFromExeDir(glfwGetWin32Window(window));

    // 设置ImGui上下文
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Try loading a system default CJK/Unicode font as fallback to avoid title garbling
    // 首先尝试常见路径（Windows），若失败则使用默认字体
    ImFont* addedFont = nullptr;
    #ifdef _WIN32
    {
    // Common CJK font path on Windows
        const char* fn = "C:/Windows/Fonts/msyh.ttc"; // 微软雅黑
        if (GetFileAttributesA(fn) != INVALID_FILE_ATTRIBUTES) {
            ImFontConfig cfg;
            cfg.FontDataOwnedByAtlas = true;
            // Specify glyph ranges to include CJK characters
            static const ImWchar ranges[] = {
                0x0020, 0x00FF, // Basic Latin + Latin Supplement
                0x2000, 0x206F, // General Punctuation
                0x3000, 0x30FF, // CJK Symbols and Punctuation, Hiragana, Katakana
                0x31F0, 0x31FF, // Katakana Phonetic Extensions
                0xFF00, 0xFFEF, // Half-width characters
                0x4e00, 0x9FAF, // CJK Ideograms
                0,
            };
            cfg.GlyphRanges = ranges;
            addedFont = io.Fonts->AddFontFromFileTTF(fn, 16.0f, &cfg);
        }
    }
    #endif
    if (!addedFont) {
        // fallback: load default font with Chinese glyphs
        ImFontConfig cfg;
        cfg.GlyphRanges = io.Fonts->GetGlyphRangesChineseFull();
        addedFont = io.Fonts->AddFontDefault(&cfg);
    }

    // If we loaded a font, set it as default so ImGui renders using it
    if (addedFont) {
        io.FontDefault = addedFont;
    }

    // 设置ImGui样式
    ImGui::StyleColorsDark();

    // 设置Platform/Renderer后端
    ImGui_ImplGlfw_InitForOther(window, true);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);
    // Ensure DX11 backend uploads font texture (needed for custom loaded fonts)
    ImGui_ImplDX11_CreateDeviceObjects();
#else
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // Enable vsync

    // 设置ImGui上下文
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // 设置ImGui样式
    ImGui::StyleColorsDark();

    // 设置Platform/Renderer后端
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);
    // Ensure OpenGL backend uploads font texture (needed for custom loaded fonts)
    ImGui_ImplOpenGL3_CreateDeviceObjects();
#endif

    // 主循环变量
    ImVec4 clear_color = ImVec4(0.15f, 0.15f, 0.15f, 1.00f);
    auto lastTime = std::chrono::high_resolution_clock::now();
    
    std::cout << "capscr Demo UI Interface initialized successfully!" << std::endl;
    #ifdef HAVE_CAPSCR
    std::cout << "HAVE_CAPSCR is defined at compile time." << std::endl;
    #else
    std::cout << "HAVE_CAPSCR is NOT defined; running in simulated mode." << std::endl;
    #endif

    // Enumerate displays and windows using capscr (or platform APIs)
    EnumerateDisplaysAndWindows();

    // Diagnostic: print counts after enumeration
    std::cout << "After enumeration: virtualScreens=" << g_uiState.virtualScreens.size()
              << ", windows=" << g_uiState.windows.size() << std::endl;

    // 主循环
    while (!glfwWindowShouldClose(window))
    {
        glfwPollEvents();

        // 检查是否需要刷新窗口列表
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration<float>(now - g_uiState.lastWindowRefresh).count();
        if (elapsed >= g_uiState.windowRefreshInterval) {
            // 保存当前选中的窗口
            int prevSelectedWindow = g_uiState.selectedWindow;
            std::string prevSelectedTitle;
            if (prevSelectedWindow >= 0 && prevSelectedWindow < g_uiState.windows.size()) {
                prevSelectedTitle = g_uiState.windows[prevSelectedWindow].title;
            }
            
            // 刷新窗口列表
            EnumerateDisplaysAndWindows();
            g_uiState.lastWindowRefresh = now;
            
            // 尝试恢复之前选中的窗口
            if (!prevSelectedTitle.empty()) {
                g_uiState.selectedWindow = -1; // 默认无选择
                for (size_t i = 0; i < g_uiState.windows.size(); ++i) {
                    if (g_uiState.windows[i].title == prevSelectedTitle) {
                        g_uiState.selectedWindow = (int)i;
                        break;
                    }
                }
            } else {
                g_uiState.selectedWindow = -1;
            }
            
            // 静默刷新，不输出日志
        }

        // 捕获一帧 (如果正在捕获)
        if (g_uiState.isCapturing) {
            ExecuteCapture();
        }

        // 更新预览纹理
        if (g_uiState.previewTextureNeedsUpdate) {
#ifdef _WIN32
            UpdatePreviewTexture();
#endif
        }

        // 开始ImGui帧
#ifdef _WIN32
        ImGui_ImplDX11_NewFrame();
#else
        ImGui_ImplOpenGL3_NewFrame();
#endif
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // 获取窗口大小并计算布局
        int window_width, window_height;
        glfwGetWindowSize(window, &window_width, &window_height);
        
        // 计算响应式布局尺寸
        float left_panel_width = window_width * 0.3f; // 左侧面板占30%
        if (left_panel_width < 250) left_panel_width = 250; // 最小宽度
        if (left_panel_width > 400) left_panel_width = 400; // 最大宽度
        
        float right_panel_width = window_width - left_panel_width;
        float status_bar_height = 30;
        float main_content_height = window_height - status_bar_height;

        // 左侧参数面板
        {
            ImGui::SetNextWindowPos(ImVec2(0, 0));
            ImGui::SetNextWindowSize(ImVec2(left_panel_width, main_content_height));
            ImGui::Begin("Parameters", nullptr, ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse);

            // GPU零拷贝设置
            ImGui::Checkbox("Prefer GPU zero-copy", &g_uiState.preferGpuZeroCopy);

            // ...existing code...

            // Backend capabilities and status: not shown in UI anymore (internal fields retained)

            // Capture methods (platform-filtered display)
            ImGui::Text("Capture methods");
            ImGui::Indent();
            #ifdef _WIN32
            ImGui::Text("Windows: ");
            ImGui::SameLine();
            ImGui::RadioButton("GDI", &g_uiState.captureMethod, 0);
            ImGui::SameLine();
            ImGui::RadioButton("DXGI", &g_uiState.captureMethod, 1);
            #endif

            #ifdef __linux__
            ImGui::Text("Linux: ");
            ImGui::SameLine();
            ImGui::RadioButton("X11", &g_uiState.captureMethod, 2);
            ImGui::SameLine();
            ImGui::RadioButton("Wayland", &g_uiState.captureMethod, 3);
            #endif

            #ifdef __APPLE__
            ImGui::Text("macOS: ");
            ImGui::SameLine();
            ImGui::RadioButton("quartz", &g_uiState.captureMethod, 4);
            #endif

            ImGui::Unindent();
            ImGui::Separator();

            // Virtual screens
            ImGui::Text("Virtual screens");
            
            // Compute responsive height for the virtual-screen area
            float virtual_screen_height = ImGui::GetContentRegionAvail().y * 0.4f; // 占可用空间的40%
            if (virtual_screen_height < 120) virtual_screen_height = 120; // 最小高度
            if (virtual_screen_height > 200) virtual_screen_height = 200; // 最大高度
            
            // Draw virtual-screen layout
            ImGui::BeginChild("VirtualScreens", ImVec2(0, virtual_screen_height), true);

            // 获取当前鼠标位置和绘制区域
            ImDrawList* draw_list = ImGui::GetWindowDrawList();
            ImVec2 canvas_p0 = ImGui::GetCursorScreenPos();
            ImVec2 canvas_sz = ImGui::GetContentRegionAvail();
            ImVec2 mouse_pos = ImGui::GetMousePos();
            bool mouse_in_canvas = mouse_pos.x >= canvas_p0.x && mouse_pos.y >= canvas_p0.y &&
                                  mouse_pos.x < canvas_p0.x + canvas_sz.x && mouse_pos.y < canvas_p0.y + canvas_sz.y;

            // 计算响应式缩放：基于实际显示器布局的边界来计算
            int min_x = INT_MAX, min_y = INT_MAX;
            int max_x = INT_MIN, max_y = INT_MIN;
            for (const auto &vs : g_uiState.virtualScreens) {
                if (vs.x < min_x) min_x = vs.x;
                if (vs.y < min_y) min_y = vs.y;
                if (vs.x + vs.width > max_x) max_x = vs.x + vs.width;
                if (vs.y + vs.height > max_y) max_y = vs.y + vs.height;
            }
            
            float display_aspect_scale = 1.0f;
            if (min_x != INT_MAX && min_y != INT_MAX) {
                int total_width = max_x - min_x;
                int total_height = max_y - min_y;
                float available_w = canvas_sz.x - 40.0f; // 更多边距用于负坐标显示器
                float available_h = canvas_sz.y - 40.0f;
                float scale_x = available_w / (float)total_width;
                float scale_y = available_h / (float)total_height;
                display_aspect_scale = (scale_x < scale_y) ? scale_x : scale_y;
                if (display_aspect_scale < 0.02f) display_aspect_scale = 0.02f;
                if (display_aspect_scale > 2.0f) display_aspect_scale = 2.0f;
            } else {
                display_aspect_scale = (canvas_sz.x - 30) / 210.0f;
                if (display_aspect_scale < 0.5f) display_aspect_scale = 0.5f;
                if (display_aspect_scale > 1.5f) display_aspect_scale = 1.5f;
            }

            // 处理鼠标按下（可能是单击或拖拽的开始）
            if (mouse_in_canvas && ImGui::IsMouseClicked(ImGuiMouseButton_Left)) {
                g_uiState.isDraggingSelection = true;
                g_uiState.dragStartPos = ImVec2(mouse_pos.x - canvas_p0.x, mouse_pos.y - canvas_p0.y);
                g_uiState.potentialClick = true;
                g_uiState.clickPos = g_uiState.dragStartPos;
                // 记录起始所处屏幕索引
                g_uiState.dragScreenIndex = -1;
            }

            // 删除键处理
            if (mouse_in_canvas && ImGui::IsKeyPressed(ImGuiKey_Delete)) {
                g_uiState.hasSelection = false;
                g_uiState.selectionScreenIndex = -1;
                // 清除屏幕选中状态并恢复主屏幕为选中（如果存在）
                for (auto &vs : g_uiState.virtualScreens) vs.selected = false;
                if (!g_uiState.virtualScreens.empty()) g_uiState.virtualScreens[0].selected = true;
            }

            // 布局每个屏幕的UI矩形，基于实际显示器的相对位置
            ImVec2 base_pos = ImVec2(canvas_p0.x + 20, canvas_p0.y + 20);
            struct UIScreenLayout { ImVec2 pos; ImVec2 size; };
            std::vector<UIScreenLayout> layouts;
            layouts.reserve(g_uiState.virtualScreens.size());
            
            // 计算最小坐标作为原点偏移
            int offset_x = min_x;
            int offset_y = min_y;
            
            for (size_t si = 0; si < g_uiState.virtualScreens.size(); ++si) {
                const auto &vs = g_uiState.virtualScreens[si];
                ImVec2 screen_size((float)vs.width * display_aspect_scale, (float)vs.height * display_aspect_scale);
                
                // 根据实际显示器位置计算UI位置
                float ui_x = base_pos.x + (float)(vs.x - offset_x) * display_aspect_scale;
                float ui_y = base_pos.y + (float)(vs.y - offset_y) * display_aspect_scale;
                ImVec2 pos(ui_x, ui_y);
                
                layouts.push_back({pos, screen_size});
            }

            // Handle dragging updates and release (selection is confined to a single screen)
            if (g_uiState.isDraggingSelection) {
                ImVec2 current_pos = ImVec2(mouse_pos.x - canvas_p0.x, mouse_pos.y - canvas_p0.y);
                if (ImGui::IsMouseDragging(ImGuiMouseButton_Left)) {
                    // 发生拖拽
                    g_uiState.potentialClick = false;
                    // 确定拖拽起始在哪个屏幕（如果尚未确定）
                    if (g_uiState.dragScreenIndex == -1) {
                        for (size_t si = 0; si < layouts.size(); ++si) {
                            const auto &l = layouts[si];
                            if (g_uiState.dragStartPos.x >= l.pos.x - canvas_p0.x && g_uiState.dragStartPos.x <= l.pos.x - canvas_p0.x + l.size.x &&
                                g_uiState.dragStartPos.y >= l.pos.y - canvas_p0.y && g_uiState.dragStartPos.y <= l.pos.y - canvas_p0.y + l.size.y) {
                                g_uiState.dragScreenIndex = (int)si;
                                break;
                            }
                        }
                    }

                    // 如果没有落在任何屏幕（出界），则取消选择行为
                    if (g_uiState.dragScreenIndex == -1) {
                        g_uiState.hasSelection = false;
                    } else {
                        // Compute selection box only within the dragScreenIndex screen (in local UI coords)
                        const auto &l = layouts[g_uiState.dragScreenIndex];
                        float local_start_x = g_uiState.dragStartPos.x - (l.pos.x - canvas_p0.x);
                        float local_start_y = g_uiState.dragStartPos.y - (l.pos.y - canvas_p0.y);
                        float local_cur_x = current_pos.x - (l.pos.x - canvas_p0.x);
                        float local_cur_y = current_pos.y - (l.pos.y - canvas_p0.y);
                        float min_x = (local_start_x < local_cur_x) ? local_start_x : local_cur_x;
                        float min_y = (local_start_y < local_cur_y) ? local_start_y : local_cur_y;
                        float max_x = (local_start_x > local_cur_x) ? local_start_x : local_cur_x;
                        float max_y = (local_start_y > local_cur_y) ? local_start_y : local_cur_y;

                        // 将 UI 局部坐标映射回显示坐标（ui -> display）
                        float ui_to_display = 1.0f / display_aspect_scale;
                        // 限制在屏幕范围内
                        if (min_x < 0) min_x = 0;
                        if (min_y < 0) min_y = 0;
                        if (max_x > l.size.x) max_x = l.size.x;
                        if (max_y > l.size.y) max_y = l.size.y;

                        g_uiState.selectionBox.x = (int)(min_x * ui_to_display);
                        g_uiState.selectionBox.y = (int)(min_y * ui_to_display);
                        g_uiState.selectionBox.width = (int)((max_x - min_x) * ui_to_display);
                        g_uiState.selectionBox.height = (int)((max_y - min_y) * ui_to_display);
                        // Persist the selection's owning screen index (so it remains after mouse release)
                        g_uiState.selectionScreenIndex = g_uiState.dragScreenIndex;
                        g_uiState.hasSelection = true;

                        // 互斥：取消窗口选择和虚拟屏选择（选区与屏幕/窗口选择互斥）
                        g_uiState.selectedWindow = -1;
                        for (size_t vi = 0; vi < g_uiState.virtualScreens.size(); ++vi) g_uiState.virtualScreens[vi].selected = false;
                    }
                }

                if (ImGui::IsMouseReleased(ImGuiMouseButton_Left)) {
                    g_uiState.isDraggingSelection = false;
                    if (g_uiState.potentialClick) {
                        // 单击处理：切换点击所在屏幕
                        ImVec2 relClick = g_uiState.clickPos;
                        bool clickedOnScreen = false;
                        for (size_t si = 0; si < layouts.size(); ++si) {
                            const auto &l = layouts[si];
                            if (relClick.x >= l.pos.x - canvas_p0.x && relClick.x <= l.pos.x - canvas_p0.x + l.size.x &&
                                relClick.y >= l.pos.y - canvas_p0.y && relClick.y <= l.pos.y - canvas_p0.y + l.size.y) {
                                // 清除所有选择状态，仅选中单击的屏幕
                                for (size_t vi = 0; vi < g_uiState.virtualScreens.size(); ++vi) g_uiState.virtualScreens[vi].selected = false;
                                g_uiState.virtualScreens[si].selected = true;
                                g_uiState.selectedWindow = -1;
                                g_uiState.hasSelection = false;
                                g_uiState.selectionScreenIndex = -1;
                                clickedOnScreen = true;
                                break;
                            }
                        }
                        g_uiState.potentialClick = false;
                    } else {
                        // Drag end: if selection too small, cancel and restore primary screen selection
                        if (!(g_uiState.selectionBox.width > 10 && g_uiState.selectionBox.height > 10)) {
                            g_uiState.hasSelection = false;
                            g_uiState.selectionScreenIndex = -1;
                            for (auto &vs : g_uiState.virtualScreens) vs.selected = false;
                            if (!g_uiState.virtualScreens.empty()) g_uiState.virtualScreens[0].selected = true;
                        }
                    }
                    g_uiState.dragScreenIndex = -1;
                }
            }

            // 绘制所有虚拟屏（基于真实分辨率比例）
            for (size_t si = 0; si < g_uiState.virtualScreens.size(); ++si) {
                const auto &vs = g_uiState.virtualScreens[si];
                const auto &l = layouts[si];
                ImU32 color = vs.selected ? IM_COL32(100,150,255,255) : IM_COL32(100,100,100,255);
                draw_list->AddRect(l.pos, ImVec2(l.pos.x + l.size.x, l.pos.y + l.size.y), color, 0.0f, 0, 2.0f);
                if (vs.selected) {
                    draw_list->AddRectFilled(l.pos, ImVec2(l.pos.x + l.size.x, l.pos.y + l.size.y), IM_COL32(100,150,255,30));
                    ImVec2 dot_pos(l.pos.x + l.size.x/2 - 3, l.pos.y + l.size.y/2 - 3);
                    draw_list->AddCircleFilled(dot_pos, 3, IM_COL32(100,150,255,255));
                }
                // 标签 - id 文本改为仅在右侧信息框显示，避免在每个屏幕下重复显示
            }

            // 绘制选择框（映射回 UI）
            if (g_uiState.hasSelection && g_uiState.selectionScreenIndex >= 0 && g_uiState.selectionScreenIndex < (int)layouts.size()) {
                const auto &l = layouts[g_uiState.selectionScreenIndex];
                float ui_x = l.pos.x + g_uiState.selectionBox.x * display_aspect_scale;
                float ui_y = l.pos.y + g_uiState.selectionBox.y * display_aspect_scale;
                float ui_w = g_uiState.selectionBox.width * display_aspect_scale;
                float ui_h = g_uiState.selectionBox.height * display_aspect_scale;
                draw_list->AddRect(ImVec2(ui_x, ui_y), ImVec2(ui_x + ui_w, ui_y + ui_h), IM_COL32(255,255,0,255), 0.0f, 0, 2.0f);
                draw_list->AddRectFilled(ImVec2(ui_x, ui_y), ImVec2(ui_x + ui_w, ui_y + ui_h), IM_COL32(255,255,0,50));
            }

            // 如果有选中的虚拟屏，右侧在 VirtualScreens 区域显示其 id（只有被选中时才显示）
            {
                int selectedIndex = -1;
                for (size_t si = 0; si < g_uiState.virtualScreens.size(); ++si) {
                    if (g_uiState.virtualScreens[si].selected) { selectedIndex = (int)si; break; }
                }
                if (selectedIndex >= 0) {
                    const auto &vs = g_uiState.virtualScreens[selectedIndex];
                    std::string label = vs.name + " (Display " + std::to_string(selectedIndex) + ")";
                    float info_h = 28.0f;
                    // Place the info box above the virtual screens area (near the red box position)
                    float max_w = canvas_sz.x - 24.0f;
                    float info_w = 220.0f;
                    if (info_w > max_w) info_w = max_w;
                    float info_x = base_pos.x; // align with left origin of the screens layout
                    // position slightly above base_pos.y so it doesn't overlap the screen rectangles
                    float info_y = base_pos.y - info_h - 6.0f;
                    if (info_y < canvas_p0.y + 2.0f) info_y = canvas_p0.y + 2.0f;
                    ImU32 bg = IM_COL32(40,40,40,200);
                    ImU32 border = IM_COL32(80,80,80,255);
                    // 背景与边框
                    draw_list->AddRectFilled(ImVec2(info_x, info_y), ImVec2(info_x + info_w, info_y + info_h), bg, 6.0f);
                    draw_list->AddRect(ImVec2(info_x, info_y), ImVec2(info_x + info_w, info_y + info_h), border, 6.0f, 0, 1.0f);
                    // 文本
                    ImVec2 text_pos = ImVec2(info_x + 8.0f, info_y + 10.0f);
                    draw_list->AddText(text_pos, IM_COL32(230,230,230,255), label.c_str());
                }
            }

            ImGui::SetCursorScreenPos(ImVec2(canvas_p0.x + 10, canvas_p0.y + 120));
            if (g_uiState.hasSelection) {
                ImGui::TextWrapped("Selection active. Press Del to delete selection");
            } else {
                ImGui::TextWrapped("Drag on a virtual screen to create/move/resize selection. Del to delete selection");
            }

            ImGui::EndChild();
            ImGui::Separator();

            // 窗口列表
            ImGui::Text("Windows");
            
            // 刷新按钮
            ImGui::SameLine();
            if (ImGui::SmallButton("Refresh")) {
                // 手动刷新窗口列表
                int prevSelectedWindow = g_uiState.selectedWindow;
                std::string prevSelectedTitle;
                if (prevSelectedWindow >= 0 && prevSelectedWindow < g_uiState.windows.size()) {
                    prevSelectedTitle = g_uiState.windows[prevSelectedWindow].title;
                }
                
                EnumerateDisplaysAndWindows();
                g_uiState.lastWindowRefresh = std::chrono::steady_clock::now();
                
                // 尝试恢复之前选中的窗口
                if (!prevSelectedTitle.empty()) {
                    g_uiState.selectedWindow = -1;
                    for (size_t i = 0; i < g_uiState.windows.size(); ++i) {
                        if (g_uiState.windows[i].title == prevSelectedTitle) {
                            g_uiState.selectedWindow = (int)i;
                            break;
                        }
                    }
                } else {
                    g_uiState.selectedWindow = -1;
                }
                
                std::cout << "Manual refresh: " << g_uiState.windows.size() << " windows found" << std::endl;
            }
            
            ImGui::BeginChild("WindowsList", ImVec2(0, 0), true);
            
            for (size_t i = 0; i < g_uiState.windows.size(); ++i) {
                const auto& win = g_uiState.windows[i];
                bool selected = (i == g_uiState.selectedWindow);
                
                if (ImGui::Selectable(win.title.c_str(), selected)) {
                    // 选择一个窗口时，清除虚拟屏和区域选择（互斥）
                    g_uiState.selectedWindow = (int)i;
                    g_uiState.hasSelection = false;
                    for (auto &vs : g_uiState.virtualScreens) vs.selected = false;
                }
            }
            
            if (g_uiState.windows.empty()) {
                ImGui::Text("No windows found");
            } else {
                ImGui::Separator();
                ImGui::Text("Select a window to set capture target");
            }
            
            ImGui::EndChild();

            ImGui::End();
        }

        // 右侧预览面板
        {
            ImGui::SetNextWindowPos(ImVec2(left_panel_width, 0));
            ImGui::SetNextWindowSize(ImVec2(right_panel_width, main_content_height));
            ImGui::Begin("Preview", nullptr, ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse);

            // 顶部FPS信息
            ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
            ImGui::Button(g_uiState.fpsInfo.c_str(), ImVec2(-1, 0));
            ImGui::PopStyleColor();

            // 预览区域
            ImGui::BeginChild("PreviewArea", ImVec2(0, -40), true, ImGuiWindowFlags_AlwaysVerticalScrollbar);
            
            // 预览内容
            ImVec2 preview_size = ImGui::GetContentRegionAvail();
            ImDrawList* preview_draw = ImGui::GetWindowDrawList();
            ImVec2 preview_pos = ImGui::GetCursorScreenPos();
            
            // 绘制背景
            preview_draw->AddRectFilled(preview_pos, ImVec2(preview_pos.x + preview_size.x, preview_pos.y + preview_size.y), IM_COL32(30, 30, 30, 255));
            
#ifdef _WIN32
            // 如果有捕获的纹理，显示它
            if (g_previewSRV && g_uiState.isCapturing) {
                // 计算纹理显示大小，保持宽高比
                float aspect = (float)g_uiState.frameWidth / (float)g_uiState.frameHeight;
                ImVec2 tex_size = preview_size;
                
                if (preview_size.x / preview_size.y > aspect) {
                    // 预览区域更宽，以高度为准
                    tex_size.x = preview_size.y * aspect;
                } else {
                    // 预览区域更高，以宽度为准
                    tex_size.y = preview_size.x / aspect;
                }
                
                // 居中显示
                ImVec2 tex_pos = ImVec2(
                    preview_pos.x + (preview_size.x - tex_size.x) * 0.5f,
                    preview_pos.y + (preview_size.y - tex_size.y) * 0.5f
                );
                
                // 绘制纹理
                preview_draw->AddImage((void*)g_previewSRV, tex_pos, ImVec2(tex_pos.x + tex_size.x, tex_pos.y + tex_size.y));
            } else {
#endif
                // // 显示模拟内容或提示
                // if (!g_uiState.isCapturing) {
                //     // 添加一些模拟的"桌面"内容
                //     preview_draw->AddRectFilled(
                //         ImVec2(preview_pos.x + 50, preview_pos.y + 50),
                //         ImVec2(preview_pos.x + 200, preview_pos.y + 150),
                //         IM_COL32(60, 80, 120, 255)
                //     );
                //     preview_draw->AddText(ImVec2(preview_pos.x + 60, preview_pos.y + 90), IM_COL32(255, 255, 255, 255), "Simulated Window");
                    
                //     preview_draw->AddRectFilled(
                //         ImVec2(preview_pos.x + 250, preview_pos.y + 100),
                //         ImVec2(preview_pos.x + 400, preview_pos.y + 200),
                //         IM_COL32(120, 80, 60, 255)
                //     );
                //     preview_draw->AddText(ImVec2(preview_pos.x + 260, preview_pos.y + 140), IM_COL32(255, 255, 255, 255), "Another Window");
                // }
                
                // 中央提示文本
                ImVec2 text_size = ImGui::CalcTextSize(g_uiState.previewText.c_str());
                ImVec2 text_pos = ImVec2(
                    preview_pos.x + (preview_size.x - text_size.x) * 0.5f,
                    preview_pos.y + (preview_size.y - text_size.y) * 0.5f
                );
                preview_draw->AddText(text_pos, IM_COL32(150, 150, 150, 255), g_uiState.previewText.c_str());
#ifdef _WIN32
            }
#endif
            
            ImGui::Dummy(preview_size);
            ImGui::EndChild();

            // 底部控制按钮：Start / Stop / Reset / Save PNG
            ImGui::BeginGroup();
            if (ImGui::Button("Start", ImVec2(80, 30))) {
#ifdef _WIN32
                StartCapture();
#else
                g_uiState.status = "Capture not supported on this platform";
#endif
            }
            ImGui::SameLine();
            if (ImGui::Button("Stop", ImVec2(80, 30))) {
#ifdef _WIN32
                StopCapture();
#else
                g_uiState.status = "Capture stopped";
#endif
            }
            ImGui::SameLine();
            if (ImGui::Button("Reset", ImVec2(80, 30))) {
                g_uiState.status = "Reset to default";
                g_uiState.hasSelection = false;
                g_uiState.selectedWindow = -1;
                for (auto &vs : g_uiState.virtualScreens) vs.selected = false;
                if (!g_uiState.virtualScreens.empty()) g_uiState.virtualScreens[0].selected = true;
                // 恢复默认设置
                g_uiState.preferGpuZeroCopy = false;
                // 平台默认 captureMethod
                #ifdef _WIN32
                g_uiState.captureMethod = 0; // GDI
                #elif defined(__linux__)
                g_uiState.captureMethod = 2; // X11
                #elif defined(__APPLE__)
                g_uiState.captureMethod = 4; // quartz
                #else
                g_uiState.captureMethod = 0;
                #endif
            }
            ImGui::SameLine();
            ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.6f, 0.2f, 1.0f));
            if (ImGui::Button("Save PNG", ImVec2(100, 30))) {
#ifdef _WIN32
                SaveCurrentFrameAsPNG();
#else
                std::cout << "Save PNG clicked (not supported on this platform)" << std::endl;
                g_uiState.status = "Save PNG not supported";
#endif
            }
            ImGui::PopStyleColor();
            ImGui::EndGroup();

            ImGui::End();
        }

        // 状态栏
        {
            ImGui::SetNextWindowPos(ImVec2(0, main_content_height));
            ImGui::SetNextWindowSize(ImVec2(window_width, status_bar_height));
            ImGui::Begin("StatusBar", nullptr, ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoTitleBar);
            
            ImGui::Text("Left: controls/targets. Right: preview. Overlay: live stats. GPU path uses SRV on Windows.");
            
            ImGui::End();
        }

        // 渲染
        ImGui::Render();

#ifdef _WIN32
        const float clear_color_with_alpha[4] = { clear_color.x * clear_color.w, clear_color.y * clear_color.w, clear_color.z * clear_color.w, clear_color.w };
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color_with_alpha);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        // Present
        g_pSwapChain->Present(1, 0); // Present with vsync
#else
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);
        glViewport(0, 0, display_w, display_h);
        glClearColor(clear_color.x * clear_color.w, clear_color.y * clear_color.w, clear_color.z * clear_color.w, clear_color.w);
        glClear(GL_COLOR_BUFFER_BIT);
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
        glfwSwapBuffers(window);
#endif
    }

    // 清理
    std::cout << "Cleaning up..." << std::endl;

#ifdef _WIN32
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
#else
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();
#endif

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}

void UpdatePreviewTexture() {
#ifdef HAVE_CAPSCR
    if (!g_frameReady || g_currentFrame.data.empty()) return;
    
    // 清理旧纹理
    CleanupPreviewTexture();
    
    // 创建新纹理
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = g_currentFrame.width;
    desc.Height = g_currentFrame.height;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = DXGI_FORMAT_B8G8R8A8_UNORM; // capscr Frame is BGRA
    desc.SampleDesc.Count = 1;
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
    desc.CPUAccessFlags = 0;
    
    D3D11_SUBRESOURCE_DATA initData = {};
    initData.pSysMem = g_currentFrame.data.data();
    initData.SysMemPitch = g_currentFrame.stride;
    
    HRESULT hr = g_pd3dDevice->CreateTexture2D(&desc, &initData, &g_previewTexture);
    if (SUCCEEDED(hr)) {
        // 创建SRV
        D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
        srvDesc.Format = desc.Format;
        srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
        srvDesc.Texture2D.MipLevels = 1;
        
        hr = g_pd3dDevice->CreateShaderResourceView(g_previewTexture, &srvDesc, &g_previewSRV);
        if (FAILED(hr)) {
            g_previewTexture->Release();
            g_previewTexture = nullptr;
        }
    }
    
    g_uiState.previewTextureNeedsUpdate = false;
#endif
}

void CleanupPreviewTexture() {
    if (g_previewSRV) {
        g_previewSRV->Release();
        g_previewSRV = nullptr;
    }
    if (g_previewTexture) {
        g_previewTexture->Release();
        g_previewTexture = nullptr;
    }
}

#ifdef _WIN32
// D3D11 Helper functions
bool CreateDeviceD3D(HWND hWnd)
{
    // Setup swap chain
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };
    HRESULT res = D3D11CreateDeviceAndSwapChain(NULL, D3D_DRIVER_TYPE_HARDWARE, NULL, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res == DXGI_ERROR_UNSUPPORTED) // Try high-performance WARP software driver if hardware is not available.
        res = D3D11CreateDeviceAndSwapChain(NULL, D3D_DRIVER_TYPE_WARP, NULL, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void CleanupDeviceD3D()
{
    CleanupPreviewTexture();
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = nullptr; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = nullptr; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = nullptr; }
}

void CreateRenderTarget()
{
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget()
{
    if (g_mainRenderTargetView) { g_mainRenderTargetView->Release(); g_mainRenderTargetView = nullptr; }
}

// Windows: try to load an icon named "icon.ico" from the executable directory and set it on the window
static void SetWindowIconFromExeDir(HWND hWnd)
{
#ifdef _WIN32
    if (!hWnd) return;
    wchar_t exePath[MAX_PATH] = {0};
    if (GetModuleFileNameW(NULL, exePath, MAX_PATH) == 0) return;
    std::wstring path(exePath);
    size_t pos = path.find_last_of(L"\\/");
    std::wstring dir = (pos == std::wstring::npos) ? std::wstring(L".") : path.substr(0, pos);
    std::wstring icoPath = dir + L"\\icon.ico";

    // Try to load the icon file from disk
    HICON hIcon = (HICON)LoadImageW(NULL, icoPath.c_str(), IMAGE_ICON, 0, 0, LR_LOADFROMFILE | LR_DEFAULTSIZE);
    if (hIcon) {
        // Set both big and small icons
        SendMessageW(hWnd, WM_SETICON, ICON_BIG, (LPARAM)hIcon);
        SendMessageW(hWnd, WM_SETICON, ICON_SMALL, (LPARAM)hIcon);
        // Note: do not destroy the icon here; the system will manage it while the window exists.
    }
#endif
}
#endif
