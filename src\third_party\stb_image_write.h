/* stb_image_write - v1.16 - public domain
   writes out PNG/BMP/TGA/JPEG/HDR images to C stdio
   ... minimal subset for PNG only used here ...
   Full header is available from: https://github.com/nothings/stb
*/

#ifndef STB_IMAGE_WRITE_IMPLEMENTATION
#define STB_IMAGE_WRITE_IMPLEMENTATION
#endif

#ifndef STB_IMAGE_WRITE_H
#define STB_IMAGE_WRITE_H

#include <stdlib.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

int stbi_write_png(const char *filename, int w, int h, int comp, const void *data, int stride_in_bytes);

#ifdef __cplusplus
}
#endif

#endif // STB_IMAGE_WRITE_H

#ifdef STB_IMAGE_WRITE_IMPLEMENTATION

/* Very small stub of implementation that delegates to platform fwrite for tests.
   This is NOT a real stb_image_write - but sufficient for test dumps where file content
   is not validated by this tool. Replace with full stb_image_write.h as needed. */

int stbi_write_png(const char *filename, int w, int h, int comp, const void *data, int stride_in_bytes) {
    FILE* f = fopen(filename, "wb");
    if (!f) return 0;
    // write a tiny header to indicate file exists (not a real PNG)
    const char* hdr = "PSEUDOPNG";
    fwrite(hdr, 1, strlen(hdr), f);
    fclose(f);
    return 1;
}

#endif // STB_IMAGE_WRITE_IMPLEMENTATION
