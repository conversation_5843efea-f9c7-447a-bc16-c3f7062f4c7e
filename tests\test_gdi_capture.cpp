#include <gtest/gtest.h>
#include "capscr/capture.hpp"
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include "test_window_helper.hpp"
#include <windows.h>
#include <memory>

using namespace capscr;

// This test focuses on testing GDI fallback behavior through the DXGI capturer
// since GDI capturer is not exposed as a separate public API

class GDICaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create controlled test window with different pattern than DXGI test
        testWindow = std::make_unique<TestWindow>();
        
        TestWindow::TestPattern pattern;
        pattern.width = 350;
        pattern.height = 250;
        pattern.backgroundColor = RGB(0, 128, 255); // Light blue background
        pattern.testRect = {30, 40, 80, 60, RGB(255, 255, 0)}; // Yellow rect
        pattern.testCircle = {180, 80, 60, 60, RGB(255, 0, 255)}; // Magenta circle
        pattern.text = "GDI_TEST_CONTENT";
        
        bool created = testWindow->Create(pattern);
        ASSERT_TRUE(created) << "Failed to create test window for GDI testing";
        
        // Note: We test GDI functionality through DXGI capturer's fallback mechanism
        capturer = createDXGICapturer(); // Will fallback to GDI if DXGI fails
        ASSERT_NE(capturer, nullptr);
    }
    
    void TearDown() override {
        if (capturer) {
            capturer->shutdown();
        }
        if (testWindow) {
            testWindow->Destroy();
        }
    }
    
    std::unique_ptr<TestWindow> testWindow;
    std::unique_ptr<ICapturer> capturer;
};

TEST_F(GDICaptureTest, CanCreateCapturer) {
    auto capturer = createDXGICapturer(); // Tests GDI fallback
    EXPECT_NE(capturer, nullptr);
    
    if (capturer) {
        capturer->shutdown();
    }
}

TEST_F(GDICaptureTest, InitializationWithControlledWindow) {
    HWND hwnd = testWindow->GetHandle();
    ASSERT_NE(hwnd, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(hwnd));
    Result result = capturer->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    // GDI fallback should almost always succeed
    EXPECT_TRUE(result == Result::Ok || result == Result::Error);
    
    if (result == Result::Ok) {
        std::cout << "Initialization successful (DXGI or GDI fallback)" << std::endl;
    }
}

TEST_F(GDICaptureTest, InitializationWithInvalidWindow) {
    HWND invalidWindow = reinterpret_cast<HWND>(0x12345678);
    
    Result result = capturer->init(CaptureTargetType::Window, "", nullptr, std::to_string(reinterpret_cast<uintptr_t>(invalidWindow)));
    
    // Should handle invalid window gracefully (could be Error, WindowNotFound, etc.)
    EXPECT_TRUE(result == Result::Error || result == Result::WindowNotFound);
}

TEST_F(GDICaptureTest, CaptureAfterSuccessfulInit) {
    if (!testWindow) {
        GTEST_SKIP() << "No suitable test window found";
    }
    
    Result initResult = capturer->init(CaptureTargetType::Window, "", nullptr, std::to_string(reinterpret_cast<uintptr_t>(testWindow->GetHandle())));
    ASSERT_EQ(initResult, Result::Ok);
    
    Frame frame;
    Result captureResult = capturer->capture(frame);
    
    EXPECT_EQ(captureResult, Result::Ok);
    
    if (captureResult == Result::Ok) {
        // Verify frame properties
        EXPECT_GT(frame.width, 0);
        EXPECT_GT(frame.height, 0);
        EXPECT_GT(frame.stride, 0);
        EXPECT_EQ(frame.format, PixelFormat::BGRA32);
        EXPECT_EQ(frame.data.size(), frame.stride * frame.height);
        
        std::cout << "GDI captured frame: " << frame.width << "x" << frame.height << std::endl;
    }
}

TEST_F(GDICaptureTest, CaptureDataIsValid) {
    if (!testWindow) {
        GTEST_SKIP() << "No suitable test window found";
    }
    
    Result initResult = capturer->init(CaptureTargetType::Window, "", nullptr, std::to_string(reinterpret_cast<uintptr_t>(testWindow->GetHandle())));
    ASSERT_EQ(initResult, Result::Ok);
    
    Frame frame;
    Result captureResult = capturer->capture(frame);
    ASSERT_EQ(captureResult, Result::Ok);
    
    // Check that we have actual pixel data
    EXPECT_FALSE(frame.data.empty());
    
    // Check for non-zero pixels (assuming window has some content)
    bool hasNonZeroPixels = false;
    for (size_t i = 0; i < frame.data.size(); i += 4) {
        if (frame.data[i] != 0 || frame.data[i+1] != 0 || frame.data[i+2] != 0) {
            hasNonZeroPixels = true;
            break;
        }
    }
    
    // Most windows should have some non-black pixels
    // (though a completely black window is valid too)
    if (!hasNonZeroPixels) {
        std::cout << "Warning: Captured frame appears to be all black" << std::endl;
    }
}

TEST_F(GDICaptureTest, MultipleCapturesWork) {
    if (!testWindow) {
        GTEST_SKIP() << "No suitable test window found";
    }
    
    Result initResult = capturer->init(CaptureTargetType::Window, "", nullptr, std::to_string(reinterpret_cast<uintptr_t>(testWindow->GetHandle())));
    ASSERT_EQ(initResult, Result::Ok);
    
    Frame frame1, frame2, frame3;
    
    EXPECT_EQ(capturer->capture(frame1), Result::Ok);
    EXPECT_EQ(capturer->capture(frame2), Result::Ok);
    EXPECT_EQ(capturer->capture(frame3), Result::Ok);
    
    // All frames should have same dimensions
    EXPECT_EQ(frame1.width, frame2.width);
    EXPECT_EQ(frame1.width, frame3.width);
    EXPECT_EQ(frame1.height, frame2.height);
    EXPECT_EQ(frame1.height, frame3.height);
}

TEST_F(GDICaptureTest, CaptureBeforeInit) {
    auto uninitializedCapturer = createDXGICapturer();
    
    Frame frame;
    Result result = uninitializedCapturer->capture(frame);
    
    // Should fail gracefully
    EXPECT_EQ(result, Result::Error);
    
    uninitializedCapturer->shutdown();
}

TEST_F(GDICaptureTest, WindowMovementHandling) {
    if (!testWindow) {
        GTEST_SKIP() << "No suitable test window found";
    }
    
    Result initResult = capturer->init(CaptureTargetType::Window, "", nullptr, std::to_string(reinterpret_cast<uintptr_t>(testWindow->GetHandle())));
    ASSERT_EQ(initResult, Result::Ok);
    
    // Capture before any movement
    Frame frame1;
    EXPECT_EQ(capturer->capture(frame1), Result::Ok);
    
    // Get original window position
    RECT originalRect;
    GetWindowRect(testWindow->GetHandle(), &originalRect);
    
    // Try to move window slightly (this might fail if we don't own the window)
    SetWindowPos(testWindow->GetHandle(), nullptr, 
                 originalRect.left + 10, originalRect.top + 10,
                 0, 0, SWP_NOSIZE | SWP_NOZORDER);
    
    // Capture after movement
    Frame frame2;
    EXPECT_EQ(capturer->capture(frame2), Result::Ok);
    
    // Restore original position
    SetWindowPos(testWindow->GetHandle(), nullptr,
                 originalRect.left, originalRect.top,
                 0, 0, SWP_NOSIZE | SWP_NOZORDER);
    
    // Both captures should succeed regardless of window movement
    EXPECT_GT(frame1.width, 0);
    EXPECT_GT(frame2.width, 0);
}
