#include "../include/capscr/platform/windows/capturer_dxgi.hpp"
#include <iostream>

using namespace capscr;

int main() {
    std::cout << "Testing enhanced capture processing features..." << std::endl;
    
    // Test DXGI capturer initialization
    DXGICapturer capturer;
    Result init_result = capturer.init(CaptureTargetType::FullScreen);
    
    if (init_result != Result::Ok) {
        std::cout << "❌ DXGI capturer initialization failed" << std::endl;
        return 1;
    }
    std::cout << "✅ DXGI capturer initialized successfully" << std::endl;
    
    // Test 1: Parameter validation for negative dimensions
    std::cout << "\n🔍 Testing parameter validation..." << std::endl;
    CaptureParams invalid_params;
    invalid_params.output_width = -100;
    invalid_params.output_height = 200;
    
    Frame result_frame;
    Result result = capturer.capture(result_frame, invalid_params);
    
    if (result != Result::Ok) {
        std::cout << "✅ Correctly rejected negative width parameter" << std::endl;
    } else {
        std::cout << "❌ Failed to reject negative width parameter" << std::endl;
    }
    
    // Test 2: Parameter validation for zero dimensions
    invalid_params.output_width = 0;
    invalid_params.output_height = 0;
    result = capturer.capture(result_frame, invalid_params);
    
    if (result != Result::Ok) {
        std::cout << "✅ Correctly rejected zero dimensions" << std::endl;
    } else {
        std::cout << "❌ Failed to reject zero dimensions" << std::endl;
    }
    
    // Test 3: GPU resize with valid parameters
    std::cout << "\n🖥️ Testing GPU resize functionality..." << std::endl;
    CaptureParams gpu_params;
    gpu_params.output_width = 800;
    gpu_params.output_height = 600;
    gpu_params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    gpu_params.use_gpu_processing = true;
    
    result = capturer.capture(result_frame, gpu_params);
    
    if (result == Result::Ok) {
        std::cout << "✅ GPU resize successful: " << result_frame.width << "x" << result_frame.height << std::endl;
        if (result_frame.width == 800 && result_frame.height == 600) {
            std::cout << "✅ Output dimensions correct" << std::endl;
        } else {
            std::cout << "❌ Output dimensions incorrect: expected 800x600, got " 
                      << result_frame.width << "x" << result_frame.height << std::endl;
        }
    } else {
        std::cout << "❌ GPU resize failed" << std::endl;
    }
    
    // Test 4: CPU fallback
    std::cout << "\n💻 Testing CPU fallback functionality..." << std::endl;
    CaptureParams cpu_params;
    cpu_params.output_width = 640;
    cpu_params.output_height = 480;
    cpu_params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    cpu_params.use_gpu_processing = false;
    
    result = capturer.capture(result_frame, cpu_params);
    
    if (result == Result::Ok) {
        std::cout << "✅ CPU fallback successful: " << result_frame.width << "x" << result_frame.height << std::endl;
        if (result_frame.width == 640 && result_frame.height == 480) {
            std::cout << "✅ Output dimensions correct" << std::endl;
        } else {
            std::cout << "❌ Output dimensions incorrect: expected 640x480, got " 
                      << result_frame.width << "x" << result_frame.height << std::endl;
        }
    } else {
        std::cout << "❌ CPU fallback failed" << std::endl;
    }
    
    // Test 5: Format conversion
    std::cout << "\n🎨 Testing format conversion..." << std::endl;
    CaptureParams format_params;
    format_params.output_width = 320;
    format_params.output_height = 240;
    format_params.output_format = PixelFormat::RGBA32;
    format_params.use_gpu_processing = true;
    
    result = capturer.capture(result_frame, format_params);
    
    if (result == Result::Ok) {
        std::cout << "✅ Format conversion successful to " << static_cast<int>(result_frame.format) << std::endl;
        if (result_frame.format == PixelFormat::RGBA32) {
            std::cout << "✅ Output format correct" << std::endl;
        } else {
            std::cout << "❌ Output format incorrect" << std::endl;
        }
    } else {
        std::cout << "❌ Format conversion failed" << std::endl;
    }
    
    std::cout << "\n🎯 All enhanced capture processing tests completed!" << std::endl;
    return 0;
}
