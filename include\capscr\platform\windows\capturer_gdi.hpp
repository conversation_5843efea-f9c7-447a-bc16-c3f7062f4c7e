#pragma once
#include "../../capture.hpp"

namespace capscr {
    class GdiCapturer : public ICapturer {
    public:
        // 构造函数使用默认实现，不需要显式声明
        ~GdiCapturer() override;

        Result init(CaptureTargetType type, const std::string& displayId = "", const Rect* optTarget = nullptr, const std::string& optWindowId = "") override;
        Result setTarget(CaptureTargetType type, const std::string& displayId = "", const Rect* optTarget = nullptr, const std::string& optWindowId = "") override;
    Rect getTarget() const override;
    std::vector<DisplayInfo> listDisplays() override;
    BackendCapability capabilities() const override;
        Result capture(Frame& outFrame) override;
        Result capture(Frame& outFrame, const CaptureParams& params) override;
        Result captureGpu(GpuTexture& outTexture) override;
    bool isGpuCaptureAvailable() const override;
        BlackFrameConfig getBlackFrameConfig() const override;
        void setBlackFrameConfig(const BlackFrameConfig& config) override;
    void shutdown() override;
    };
}
