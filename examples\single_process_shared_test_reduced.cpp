#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <iostream>
#include <vector>
#include <cstdint>
#include <d3d11sdklayers.h>

using Microsoft::WRL::ComPtr;

static void printHR(const char* msg, HRESULT hr) {
    std::cerr << msg << " hr=0x" << std::hex << hr << std::dec << std::endl;
}

int main() {
    std::cout << "Single-process reduced shared handle test\n";

    ComPtr<ID3D11Device> devA;
    ComPtr<ID3D11DeviceContext> ctxA;
    D3D_FEATURE_LEVEL flA;
    bool haveDebug = false;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &devA, &flA, &ctxA);
    if (SUCCEEDED(hr)) { haveDebug = true; std::cout << "Created device A with D3D11_CREATE_DEVICE_DEBUG" << std::endl; }
    else { hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &devA, &flA, &ctxA); if (FAILED(hr)) { printHR("CreateDevice A failed", hr); return 1; } }

    // Candidate combinations derived from debug messages: prefer D3D11_RESOURCE_MISC_SHARED (+ optional KEYEDMUTEX)
    struct MiscCase { UINT misc; const char* name; } miscCases[] = {
        { D3D11_RESOURCE_MISC_SHARED, "SHARED" },
        { D3D11_RESOURCE_MISC_SHARED | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX, "SHARED|KEYEDMUTEX" },
        { D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX, "KEYEDMUTEX" }
    };

    struct AccessCase { DWORD access; const char* name; } accessCases[] = {
        { 0, "ACCESS_0" },
        { GENERIC_READ, "GENERIC_READ" }
    };

    struct FormatCase { DXGI_FORMAT fmt; const char* name; } formats[] = {
        { DXGI_FORMAT_R8G8B8A8_UNORM, "R8G8B8A8_UNORM" },
        { DXGI_FORMAT_B8G8R8A8_UNORM, "B8G8R8A8_UNORM" }
    };

    struct BindCase { UINT bind; const char* name; } binds[] = {
        { D3D11_BIND_SHADER_RESOURCE, "BIND_SHADER_RESOURCE" },
        { D3D11_BIND_RENDER_TARGET, "BIND_RENDER_TARGET" },
        { 0, "BIND_NONE" }
    };

    for (auto m : miscCases) for (auto a : accessCases) {
        std::cout << "\n--- Test misc=" << m.name << " access=" << a.name << " (usage=USAGE_DEFAULT only) ---\n";
        for (auto f : formats) for (auto b : binds) {
            std::cout << "--> format=" << f.name << " usage=USAGE_DEFAULT bind=" << b.name << "\n";
            D3D11_TEXTURE2D_DESC desc = {};
            desc.Width = 128; desc.Height = 64; desc.MipLevels = 1; desc.ArraySize = 1;
            desc.Format = f.fmt; desc.SampleDesc.Count = 1;
            desc.Usage = D3D11_USAGE_DEFAULT; desc.BindFlags = b.bind;
            desc.CPUAccessFlags = 0; desc.MiscFlags = m.misc;

            std::vector<uint8_t> bmp(desc.Width * desc.Height * 4);
            for (int y = 0; y < (int)desc.Height; ++y) for (int x = 0; x < (int)desc.Width; ++x) {
                int i = (y*desc.Width + x)*4; bmp[i+0] = (uint8_t)x; bmp[i+1] = (uint8_t)y; bmp[i+2] = 0; bmp[i+3] = 255;
            }
            D3D11_SUBRESOURCE_DATA sd = {}; sd.pSysMem = bmp.data(); sd.SysMemPitch = desc.Width * 4;
            ComPtr<ID3D11Texture2D> texA;
            hr = devA->CreateTexture2D(&desc, &sd, &texA);
            if (FAILED(hr) || !texA) { printHR("CreateTexture2D A failed", hr); continue; }

            ComPtr<IDXGIResource1> res1; if (FAILED(texA.As(&res1))) { printHR("QI IDXGIResource1 failed", E_FAIL); continue; }
            HANDLE h = nullptr; SECURITY_ATTRIBUTES sa = {}; sa.nLength = sizeof(sa); sa.bInheritHandle = FALSE;
            hr = res1->CreateSharedHandle(&sa, a.access, nullptr, &h);
            std::cout << "CreateSharedHandle returned hr=0x" << std::hex << hr << std::dec;
            if (SUCCEEDED(hr) && h) std::cout << " handle=0x" << std::hex << reinterpret_cast<uintptr_t>(h) << std::dec;
            std::cout << std::endl;

            // Create device B on same adapter
            ComPtr<IDXGIDevice> dxDevA; ComPtr<IDXGIAdapter> adapterA;
            if (SUCCEEDED(devA.As(&dxDevA))) { if (FAILED(dxDevA->GetAdapter(&adapterA))) adapterA.Reset(); }
            ComPtr<ID3D11Device> devB; ComPtr<ID3D11DeviceContext> ctxB; D3D_FEATURE_LEVEL flB;
            if (adapterA) hr = D3D11CreateDevice(adapterA.Get(), D3D_DRIVER_TYPE_UNKNOWN, nullptr, (haveDebug?D3D11_CREATE_DEVICE_DEBUG:0), nullptr, 0, D3D11_SDK_VERSION, &devB, &flB, &ctxB);
            else hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, (haveDebug?D3D11_CREATE_DEVICE_DEBUG:0), nullptr, 0, D3D11_SDK_VERSION, &devB, &flB, &ctxB);
            if (FAILED(hr)) { printHR("CreateDevice B failed", hr); if (h) CloseHandle(h); continue; }

            ComPtr<ID3D11Texture2D> texB;
            if (h) hr = devB->OpenSharedResource(h, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(texB.GetAddressOf())); else hr = E_INVALIDARG;
            std::cout << "OpenSharedResource returned hr=0x" << std::hex << hr << std::dec << std::endl;

            if (texB) { D3D11_TEXTURE2D_DESC descB; texB->GetDesc(&descB); std::cout << "Opened resource: " << descB.Width << "x" << descB.Height << " fmt=" << descB.Format << std::endl; }

            if (haveDebug) {
                ComPtr<ID3D11InfoQueue> iq;
                if (SUCCEEDED(devA.As(&iq))) {
                    UINT64 n = iq->GetNumStoredMessagesAllowedByRetrievalFilter(); if (n>0) std::cout << "ID3D11InfoQueue messages (devA): " << n << std::endl;
                    for (UINT64 i = 0; i < n; ++i) { SIZE_T msgLen=0; iq->GetMessage(i, nullptr, &msgLen); D3D11_MESSAGE* msg=(D3D11_MESSAGE*)malloc(msgLen); if (msg) { iq->GetMessage(i,msg,&msgLen); std::cout<<"  D3D11 msg: "<<msg->pDescription<<std::endl; free(msg);} }
                }
            }

            if (h) CloseHandle(h);
        }
    }

    std::cout << "Reduced combinations tested" << std::endl;
    return 0;
}
#else
int main() { return 0; }
#endif
