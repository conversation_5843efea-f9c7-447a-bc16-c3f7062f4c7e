#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <wrl.h>
#include <iostream>
#include <vector>

using Microsoft::WRL::ComPtr;

// Minimal example: create a texture on the same D3D11 device and return ComPtr to consumer
int main() {
    // Create device
    ComPtr<ID3D11Device> dev; ComPtr<ID3D11DeviceContext> ctx; D3D_FEATURE_LEVEL fl;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &dev, &fl, &ctx);
    if (FAILED(hr)) { std::cout << "CreateDevice failed hr=" << std::hex << hr << std::dec << "\n"; return 1; }

    // Create a BGRA texture that producer will write and consumer will read
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = 320; desc.Height = 240; desc.MipLevels = 1; desc.ArraySize = 1;
    desc.Format = DXGI_FORMAT_B8G8R8A8_UNORM; desc.SampleDesc.Count = 1;
    desc.Usage = D3D11_USAGE_DEFAULT; desc.BindFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_RENDER_TARGET; desc.CPUAccessFlags = 0;
    desc.MiscFlags = 0;

    std::vector<uint8_t> data(desc.Width * desc.Height * 4, 128);
    D3D11_SUBRESOURCE_DATA sd = {}; sd.pSysMem = data.data(); sd.SysMemPitch = desc.Width * 4;

    ComPtr<ID3D11Texture2D> tex;
    hr = dev->CreateTexture2D(&desc, &sd, &tex);
    if (FAILED(hr) || !tex) { std::cout << "CreateTexture2D failed hr=" << std::hex << hr << std::dec << "\n"; return 2; }

    // Producer: we could render/compute into tex. Here we simply leave initial data.
    // GPU sync: flush to ensure GPU commands issued (none here) are submitted
    ctx->Flush();

    // Consumer: directly use tex as SRV or copy to CPU
    ComPtr<ID3D11Texture2D> staging;
    D3D11_TEXTURE2D_DESC sdesc = desc; sdesc.Usage = D3D11_USAGE_STAGING; sdesc.BindFlags = 0; sdesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ; sdesc.MiscFlags = 0;
    hr = dev->CreateTexture2D(&sdesc, nullptr, &staging);
    if (FAILED(hr) || !staging) { std::cout << "Create staging failed hr=" << std::hex << hr << std::dec << "\n"; return 3; }

    // Copy GPU->staging then Map
    ctx->CopyResource(staging.Get(), tex.Get());
    // Ensure the copy is executed
    ctx->Flush();

    D3D11_MAPPED_SUBRESOURCE mapped;
    hr = ctx->Map(staging.Get(), 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) { std::cout << "Map failed hr=" << std::hex << hr << std::dec << "\n"; return 4; }

    // Sample: print first 8 bytes
    uint8_t* ptr = reinterpret_cast<uint8_t*>(mapped.pData);
    std::cout << "First bytes:";
    for (int i = 0; i < 8; ++i) std::cout << " " << (int)ptr[i];
    std::cout << "\n";

    ctx->Unmap(staging.Get(), 0);

    std::cout << "In-process zero-copy demo completed\n";
    return 0;
}
#else
int main(){return 0;}
#endif
