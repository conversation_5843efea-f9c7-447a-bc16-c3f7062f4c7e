// black_frame_handling_example.cpp
// 演示如何在应用层正确处理 DXGI 黑帧问题

#include <capscr/capture.hpp>
#include <iostream>
#include <chrono>
#include <thread>

#ifdef _WIN32
#include <windows.h>
#endif

using namespace capscr;

class RobustCaptureSession {
private:
    std::unique_ptr<ICapturer> capturer_;
    BlackFrameConfig blackFrameConfig_;
    int consecutiveErrors_ = 0;
    static const int MAX_CONSECUTIVE_ERRORS = 10;
    static const int RESET_THRESHOLD = 5;

public:
    bool initialize() {
        capturer_ = createBestCapturer();
        if (!capturer_) {
            std::cerr << "Failed to create capturer" << std::endl;
            return false;
        }

        // 配置黑帧处理策略
        blackFrameConfig_.enable_detection = true;
        blackFrameConfig_.enable_auto_retry = true;
        blackFrameConfig_.max_retries = 3;
        blackFrameConfig_.retry_delay_ms = 50;
        blackFrameConfig_.black_threshold = 0.01f;  // 1% 非零像素阈值
        blackFrameConfig_.log_black_frames = true;
        blackFrameConfig_.validate_tiny_probe = true;
        blackFrameConfig_.validate_full_frame = true;
        blackFrameConfig_.consecutive_failures_reset = RESET_THRESHOLD;

        capturer_->setBlackFrameConfig(blackFrameConfig_);

        // 初始化捕获（窗口捕获示例）
        Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, "Test Capture Window");
        if (result != Result::Ok) {
            std::cerr << "Failed to initialize capturer: " << static_cast<int>(result) << std::endl;
            return false;
        }

        std::cout << "Capture session initialized with black frame protection" << std::endl;
        return true;
    }

    // 强健的帧捕获方法
    Result captureFrame(Frame& outFrame) {
        Result result = capturer_->capture(outFrame);
        
        switch (result) {
            case Result::Ok:
                consecutiveErrors_ = 0;
                return Result::Ok;
                
            case Result::BlackFrameDetected:
                std::cout << "Warning: Black frame detected after retries" << std::endl;
                consecutiveErrors_++;
                
                // 检查是否需要重置捕获器
                if (consecutiveErrors_ >= RESET_THRESHOLD) {
                    std::cout << "Too many consecutive black frames, attempting reset..." << std::endl;
                    return resetCapturer();
                }
                return result;
                
            case Result::RetryExhausted:
                std::cout << "Error: All retry attempts exhausted" << std::endl;
                consecutiveErrors_++;
                return result;
                
            case Result::WindowMinimized:
                std::cout << "Window is minimized, waiting..." << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                return result;
                
            case Result::WindowClosed:
            case Result::WindowNotFound:
                std::cout << "Window unavailable: " << static_cast<int>(result) << std::endl;
                return result;
                
            case Result::Error:
                consecutiveErrors_++;
                if (consecutiveErrors_ >= MAX_CONSECUTIVE_ERRORS) {
                    std::cout << "Too many consecutive errors, giving up" << std::endl;
                    return result;
                }
                
                // 短暂延迟后重试
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                return result;
                
            default:
                return result;
        }
    }

private:
    Result resetCapturer() {
        std::cout << "Resetting capturer..." << std::endl;
        
        // 获取当前配置
        auto currentTarget = capturer_->getTarget();
        
        // 重新初始化
        capturer_->shutdown();
        capturer_ = createBestCapturer();
        if (!capturer_) {
            return Result::Error;
        }
        
        capturer_->setBlackFrameConfig(blackFrameConfig_);
        Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, "Test Capture Window");
        
        if (result == Result::Ok) {
            consecutiveErrors_ = 0;
            std::cout << "Capturer reset successfully" << std::endl;
        }
        
        return result;
    }
};

// 自适应捕获循环示例
void adaptiveCaptureLoop(RobustCaptureSession& session, int maxFrames = 100) {
    Frame frame;
    int successCount = 0;
    int blackFrameCount = 0;
    int errorCount = 0;
    
    auto startTime = std::chrono::steady_clock::now();
    
    for (int i = 0; i < maxFrames; ++i) {
        Result result = session.captureFrame(frame);
        
        switch (result) {
            case Result::Ok:
                successCount++;
                std::cout << "Frame " << i + 1 << ": Success (" << frame.width << "x" << frame.height 
                         << ", " << frame.data.size() << " bytes)" << std::endl;
                break;
                
            case Result::BlackFrameDetected:
                blackFrameCount++;
                std::cout << "Frame " << i + 1 << ": Black frame detected" << std::endl;
                // 继续尝试下一帧
                break;
                
            case Result::WindowMinimized:
                std::cout << "Frame " << i + 1 << ": Window minimized, skipping..." << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                i--; // 重试这一帧
                continue;
                
            case Result::RetryExhausted:
            case Result::Error:
                errorCount++;
                std::cout << "Frame " << i + 1 << ": Error occurred" << std::endl;
                
                // 动态调整延迟
                int delay = std::min(1000, 50 * errorCount);
                std::this_thread::sleep_for(std::chrono::milliseconds(delay));
                break;
                
            case Result::WindowClosed:
            case Result::WindowNotFound:
                std::cout << "Target window lost, stopping capture" << std::endl;
                return;
                
            default:
                std::cout << "Frame " << i + 1 << ": Unexpected result " << static_cast<int>(result) << std::endl;
                break;
        }
        
        // 短暂延迟以避免过度占用 CPU
        std::this_thread::sleep_for(std::chrono::milliseconds(33)); // ~30 FPS
    }
    
    auto endTime = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    std::cout << "\n=== Capture Statistics ===" << std::endl;
    std::cout << "Total frames attempted: " << maxFrames << std::endl;
    std::cout << "Successful captures: " << successCount << std::endl;
    std::cout << "Black frames detected: " << blackFrameCount << std::endl;
    std::cout << "Errors encountered: " << errorCount << std::endl;
    std::cout << "Success rate: " << (100.0f * successCount / maxFrames) << "%" << std::endl;
    std::cout << "Duration: " << duration.count() << "ms" << std::endl;
    
    if (successCount > 0) {
        float avgFps = 1000.0f * successCount / duration.count();
        std::cout << "Average FPS: " << avgFps << std::endl;
    }
}

int main() {
    std::cout << "=== DXGI Black Frame Handling Example ===" << std::endl;
    std::cout << "This example demonstrates robust handling of DXGI black frame issues.\n" << std::endl;
    
    RobustCaptureSession session;
    if (!session.initialize()) {
        std::cerr << "Failed to initialize capture session" << std::endl;
        return 1;
    }
    
    std::cout << "\nStarting adaptive capture loop..." << std::endl;
    std::cout << "The system will automatically handle black frames and retry as needed.\n" << std::endl;
    
    try {
        adaptiveCaptureLoop(session, 50); // 捕获 50 帧
    } catch (const std::exception& e) {
        std::cerr << "Exception during capture: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\nCapture completed successfully!" << std::endl;
    return 0;
}
