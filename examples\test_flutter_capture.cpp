#include <iostream>
#include <windows.h>
#include <vector>

// Try different capture methods for Flutter window (no BMP fallback save helper)

int main() {
    HWND hwnd = FindWindowA(nullptr, "Char As Gem");
    if (!hwnd) {
        std::cout << "Window not found\n";
        return 1;
    }
    
    RECT rect;
    GetWindowRect(hwnd, &rect);
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    
    std::cout << "Trying different capture methods for Flutter window...\n";
    std::cout << "Window size: " << width << "x" << height << "\n";
    
    // Method 1: Print window (works better for some apps)
    std::cout << "\nMethod 1: PrintWindow\n";
    HDC hScreenDC = GetDC(nullptr);
    HDC hMemDC = CreateCompatibleDC(hScreenDC);
    HBITMAP hBitmap = CreateCompatibleBitmap(hScreenDC, width, height);
    HBITMAP hOldBitmap = (HBITMAP)SelectObject(hMemDC, hBitmap);
    
    BOOL result = PrintWindow(hwnd, hMemDC, PW_RENDERFULLCONTENT);
    std::cout << "PrintWindow result: " << (result ? "Success" : "Failed") << "\n";
    
    if (result) {
        // Check if we got actual content by sampling some pixels
        COLORREF pixel1 = GetPixel(hMemDC, 10, 10);
        COLORREF pixel2 = GetPixel(hMemDC, width/2, height/2);
        COLORREF pixel3 = GetPixel(hMemDC, width-10, height-10);
        
        std::cout << "Sample pixels: " << std::hex << pixel1 << ", " << pixel2 << ", " << pixel3 << std::dec << "\n";
        
        if (pixel1 != 0 || pixel2 != 0 || pixel3 != 0) {
            std::cout << "Got non-black pixels - content captured!\n";
        } else {
            std::cout << "All sampled pixels are black - might be empty\n";
        }
    }
    
    // Method 2: Try with client area
    std::cout << "\nMethod 2: PrintWindow with client area\n";
    HDC hMemDC2 = CreateCompatibleDC(hScreenDC);
    HBITMAP hBitmap2 = CreateCompatibleBitmap(hScreenDC, width, height);
    SelectObject(hMemDC2, hBitmap2);
    
    result = PrintWindow(hwnd, hMemDC2, PW_CLIENTONLY);
    std::cout << "PrintWindow (client only) result: " << (result ? "Success" : "Failed") << "\n";
    
    // Method 3: Bring to front and try again
    std::cout << "\nMethod 3: Bring to front and capture\n";
    SetForegroundWindow(hwnd);
    BringWindowToTop(hwnd);
    Sleep(100);
    
    HDC hMemDC3 = CreateCompatibleDC(hScreenDC);
    HBITMAP hBitmap3 = CreateCompatibleBitmap(hScreenDC, width, height);
    SelectObject(hMemDC3, hBitmap3);
    
    result = BitBlt(hMemDC3, 0, 0, width, height, hScreenDC, rect.left, rect.top, SRCCOPY);
    std::cout << "BitBlt (after bringing to front) result: " << (result ? "Success" : "Failed") << "\n";
    
    // Cleanup
    SelectObject(hMemDC, hOldBitmap);
    DeleteObject(hBitmap);
    DeleteDC(hMemDC);
    DeleteObject(hBitmap2);
    DeleteDC(hMemDC2);
    DeleteObject(hBitmap3);
    DeleteDC(hMemDC3);
    ReleaseDC(nullptr, hScreenDC);
    
    return 0;
}
