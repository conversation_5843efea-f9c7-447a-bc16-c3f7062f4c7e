#include <iostream>
#include <vector>
#include <cstring>

// 简单的堆损坏测试
int main() {
    // 模拟像素格式转换中的操作
    int width = 160;
    int height = 120;
    int sourceStride = 640;  // BGRA32: 160 * 4
    int targetStride = 480;  // RGB24: 160 * 3
    
    std::vector<uint8_t> source(height * sourceStride);
    std::vector<uint8_t> target(height * targetStride);
    
    // 填充源数据
    for (size_t i = 0; i < source.size(); i += 4) {
        source[i] = 100;     // B
        source[i+1] = 150;   // G  
        source[i+2] = 200;   // R
        source[i+3] = 255;   // A
    }
    
    std::cout << "Starting conversion test..." << std::endl;
    std::cout << "Source buffer size: " << source.size() << std::endl;
    std::cout << "Target buffer size: " << target.size() << std::endl;
    
    // 执行4->3字节转换
    for (int y = 0; y < height; ++y) {
        const uint8_t* srow = source.data() + static_cast<size_t>(y) * sourceStride;
        uint8_t* trow = target.data() + static_cast<size_t>(y) * targetStride;
        for (int x = 0; x < width; ++x) {
            const uint8_t* sp = srow + x * 4;
            uint8_t* tp = trow + x * 3;
            
            // 边界检查
            size_t required_bytes = static_cast<size_t>(y) * targetStride + x * 3 + 3;
            size_t total_buffer_size = static_cast<size_t>(height) * targetStride;
            if (required_bytes > total_buffer_size) {
                std::cout << "Bounds violation at y=" << y << " x=" << x << std::endl;
                break;
            }
            
            // BGRA32 -> RGB24 转换
            tp[0] = sp[2]; // R
            tp[1] = sp[1]; // G
            tp[2] = sp[0]; // B
        }
    }
    
    std::cout << "Conversion completed successfully!" << std::endl;
    
    // 验证一些像素
    for (int i = 0; i < 10; i++) {
        std::cout << "Pixel " << i << ": R=" << (int)target[i*3] 
                  << " G=" << (int)target[i*3+1] 
                  << " B=" << (int)target[i*3+2] << std::endl;
    }
    
    return 0;
}
