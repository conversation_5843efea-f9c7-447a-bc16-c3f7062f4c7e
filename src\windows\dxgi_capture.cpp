#ifdef _WIN32
#include "dxgi_capture.hpp"
#include "dxgi_compute.hpp"
#include <iostream>

namespace capscr {

// Global window event tracking
static std::unordered_map<HWND, DXGICapture*> g_windowMap;
static std::mutex g_windowMapMutex;

// Global window event hook procedure
static void CALLBACK GlobalWinEventProc(HWINEVENTHOOK hWinEventHook, DWORD event, HWND hwnd,
                                        LONG idObject, LONG idChild, 
                                        DWORD dwEventThread, DWORD dwmsEventTime) {
    if (idObject != OBJID_WINDOW) return;
    
    std::lock_guard<std::mutex> lock(g_windowMapMutex);
    auto it = g_windowMap.find(hwnd);
    if (it != g_windowMap.end() && it->second) {
        it->second->handleWinEventUpdate(hwnd);
    }
}

DXGICapture::DXGICapture()
    : targetType_(CaptureTargetType::FullScreen)
    , adapterIndex_(-1)
    , outputIndex_(-1)
    , targetHwnd_(nullptr)
    , winEventHook_(nullptr)
    , winEventCount_(0) {
    
    device_ = std::make_unique<DXGIDevice>();
    
    CAPSCR_LOG_DEBUG("DXGICapture constructed");
}

DXGICapture::~DXGICapture() {
    shutdown();
}

Result DXGICapture::initialize(CaptureTargetType type, const std::string& displayId,
                              const Rect* optTarget, const std::string& optWindowId) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    CAPSCR_LOG_INFO("Initializing DXGI capture");

    // Initialize device first
    Result result = device_->initialize();
    if (result != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to initialize DXGI device");
        return result;
    }

    // Create texture manager
    textureManager_ = std::make_unique<DXGITexture>(device_->getDevice(), device_->getContext());

    // Store target settings
    targetType_ = type;
    displayId_ = displayId;
    targetRect_ = {};
    
    if (optTarget) {
        targetRect_ = *optTarget;
    }

    // Handle window target setup
    if (type == CaptureTargetType::Window && !optWindowId.empty()) {
        result = setupWindowTarget(optWindowId);
        if (result != Result::Ok) {
            return result;
        }
    }

    // Create duplication
    if (!recreateDuplication()) {
        CAPSCR_LOG_ERROR("Failed to create DXGI output duplication");
        return Result::Unsupported;
    }

    CAPSCR_LOG_INFO("DXGI capture initialized successfully");
    return Result::Ok;
}

Result DXGICapture::capture(Frame& outFrame) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!duplication_) {
        CAPSCR_LOG_ERROR("No duplication available");
        return Result::Error;
    }

    // Validate window if capturing window
    if (targetType_ == CaptureTargetType::Window && targetHwnd_) {
        if (!isWindowValid(targetHwnd_)) {
            CAPSCR_LOG_WARN("Target window is no longer valid");
            return Result::WindowNotFound;
        }
    }

    // Try to acquire frame
    DXGI_OUTDUPL_FRAME_INFO frameInfo;
    ComPtr<IDXGIResource> resource;
    Result result = acquireFrame(frameInfo, resource);
    
    if (result != Result::Ok) {
        // Try device recovery if needed
        if (result == Result::Error) { // Changed from DeviceLost to Error
            result = handleDeviceLost();
            if (result != Result::Ok) {
                CAPSCR_LOG_ERROR("Device recovery failed, trying GDI fallback");
                return gdiCapture(targetRect_, outFrame);
            }
            // Retry capture after recovery
            result = acquireFrame(frameInfo, resource);
        }
        
        if (result != Result::Ok) {
            CAPSCR_LOG_WARN("Failed to acquire frame, trying GDI fallback");
            return gdiCapture(targetRect_, outFrame);
        }
    }

    // Convert resource to texture
    ComPtr<ID3D11Texture2D> acquiredTexture;
    HRESULT hr = resource->QueryInterface(__uuidof(ID3D11Texture2D),
                                         reinterpret_cast<void**>(acquiredTexture.GetAddressOf()));
    if (FAILED(hr)) {
        duplication_->ReleaseFrame();
        CAPSCR_LOG_ERROR("Failed to get ID3D11Texture2D from acquired resource");
        return Result::Error;
    }

    // Determine capture region
    Rect captureRegion = targetRect_;
    if (targetType_ == CaptureTargetType::FullScreen) {
        D3D11_TEXTURE2D_DESC desc;
        acquiredTexture->GetDesc(&desc);
        captureRegion = {0, 0, static_cast<int>(desc.Width), static_cast<int>(desc.Height)};
    } else if (targetType_ == CaptureTargetType::Window && targetHwnd_) {
        // Update window rect at capture time
        updateWindowRect();
        std::lock_guard<std::mutex> targetLock(targetMutex_);
        captureRegion = targetRect_;
    }

    // Copy texture data
    if (captureRegion.width <= 0 || captureRegion.height <= 0) {
        duplication_->ReleaseFrame();
        CAPSCR_LOG_ERROR("Invalid capture region");
        return Result::Error;
    }

    // Use full texture copy or subregion copy
    D3D11_TEXTURE2D_DESC textureDesc;
    acquiredTexture->GetDesc(&textureDesc);
    
    if (captureRegion.x == 0 && captureRegion.y == 0 &&
        captureRegion.width == static_cast<int>(textureDesc.Width) &&
        captureRegion.height == static_cast<int>(textureDesc.Height)) {
        // Full texture copy
        result = textureManager_->copyFullTexture(acquiredTexture, outFrame);
    } else {
        // Subregion copy
        result = textureManager_->copySubregion(acquiredTexture, captureRegion, outFrame);
    }

    duplication_->ReleaseFrame();
    
    if (result != Result::Ok) {
        CAPSCR_LOG_WARN("Texture copy failed, trying GDI fallback");
        return gdiCapture(captureRegion, outFrame);
    }

    return Result::Ok;
}

Result DXGICapture::captureGpu(GpuTexture& outTexture) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!duplication_ || !device_->isValid()) {
        CAPSCR_LOG_ERROR("GPU capture not available");
        return Result::Unsupported;
    }

    DXGI_OUTDUPL_FRAME_INFO frameInfo;
    ComPtr<IDXGIResource> resource;
    Result result = acquireFrame(frameInfo, resource);
    
    if (result != Result::Ok) {
        return result;
    }

    ComPtr<ID3D11Texture2D> acquiredTexture;
    HRESULT hr = resource->QueryInterface(__uuidof(ID3D11Texture2D),
                                         reinterpret_cast<void**>(acquiredTexture.GetAddressOf()));
    if (FAILED(hr)) {
        duplication_->ReleaseFrame();
        return Result::Error;
    }

    D3D11_TEXTURE2D_DESC desc;
    acquiredTexture->GetDesc(&desc);

    // Fill GPU texture structure
    outTexture.d3d11_device = device_->getDevice().Get();
    outTexture.d3d11_context = device_->getContext().Get();
    outTexture.d3d11_texture = acquiredTexture.Get();
    outTexture.width = static_cast<int>(desc.Width);
    outTexture.height = static_cast<int>(desc.Height);
    outTexture.format = PixelFormat::BGRA32;
    // Add references to prevent cleanup while caller holds the texture
    device_->getDevice()->AddRef();
    device_->getContext()->AddRef();
    acquiredTexture->AddRef();

    // Provide a release callback to allow the caller to relinquish the
    // acquired DXGI frame and release COM references. This makes the
    // zero-copy semantics explicit: caller must call outTexture.release()
    // when finished using the texture. The callback is idempotent.
    ComPtr<IDXGIOutputDuplication> dupRef = duplication_;
    ComPtr<ID3D11Texture2D> texRef = acquiredTexture;
    ComPtr<ID3D11Device> devRef = device_->getDevice();
    ComPtr<ID3D11DeviceContext> ctxRef = device_->getContext();

    outTexture.release_callback = [dupRef, texRef, devRef, ctxRef]() mutable {
        // Release DXGI frame if duplication still exists
        if (dupRef) {
            dupRef->ReleaseFrame();
        }
        // Release COM references by resetting smart pointers
        try {
            texRef.Reset();
            ctxRef.Reset();
            devRef.Reset();
        } catch (...) {}
    };

    return Result::Ok;
}

Result DXGICapture::captureZeroCopy(ZeroCopyFrame& outFrame, const ZeroCopyOptions& opts, const CaptureParams* processingParams) {
    std::lock_guard<std::mutex> lock(mutex_);

    // Diagnostic logging: duplication_ and device validity can explain why
    // this method returns Unsupported in some runtime environments.
    if (duplication_) {
        CAPSCR_LOG_INFO("captureZeroCopy: duplication_ is present");
    } else {
        CAPSCR_LOG_INFO("captureZeroCopy: duplication_ is null");
    }
    if (device_) {
        if (device_->isValid()) CAPSCR_LOG_INFO("captureZeroCopy: device is valid");
        else CAPSCR_LOG_INFO("captureZeroCopy: device is INVALID (GetDeviceRemovedReason)");
    } else {
        CAPSCR_LOG_INFO("captureZeroCopy: device_ is null");
    }

    if (!duplication_ || !device_->isValid()) {
        CAPSCR_LOG_WARN("Zero-copy GPU capture not available");
        return Result::Unsupported;
    }

    if (opts.prefer_shared_handle) {
        // Shared-handle path not implemented in this step
        CAPSCR_LOG_WARN("captureZeroCopy: shared-handle path not implemented yet");
        return Result::Unsupported;
    }

    DXGI_OUTDUPL_FRAME_INFO frameInfo;
    ComPtr<IDXGIResource> resource;
    Result result = acquireFrame(frameInfo, resource);
    if (result != Result::Ok) {
        return result;
    }

    ComPtr<ID3D11Texture2D> acquiredTexture;
    HRESULT hr = resource->QueryInterface(__uuidof(ID3D11Texture2D),
                                         reinterpret_cast<void**>(acquiredTexture.GetAddressOf()));
    if (FAILED(hr)) {
        duplication_->ReleaseFrame();
        CAPSCR_LOG_ERROR("captureZeroCopy: failed to get ID3D11Texture2D from resource");
        return Result::Error;
    }

    D3D11_TEXTURE2D_DESC desc;
    acquiredTexture->GetDesc(&desc);

#if 1
    // If processingParams is provided and GPU compute is available, attempt
    // to run GPU processing and return the processed texture instead of the
    // raw acquired texture. This is a convenience overload and may allocate
    // a new texture (i.e., not strictly zero-copy).
    if (processingParams != nullptr && device_ && device_->isValid()) {
        DXGICompute compute(device_->getDevice(), device_->getContext());
        if (compute.initialize() == Result::Ok) {
            CAPSCR_LOG_INFO("captureZeroCopy: attempting GPU processing via DXGICompute");
            ComPtr<ID3D11Texture2D> inputTex = acquiredTexture;
            D3D11_TEXTURE2D_DESC desc; acquiredTexture->GetDesc(&desc);
            const UINT requiredBind = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_UNORDERED_ACCESS;
            ComPtr<ID3D11Texture2D> compat;
            if ((desc.BindFlags & requiredBind) != requiredBind) {
                D3D11_TEXTURE2D_DESC compatDesc = desc;
                compatDesc.BindFlags = requiredBind;
                compatDesc.Usage = D3D11_USAGE_DEFAULT;
                compatDesc.CPUAccessFlags = 0;
                compatDesc.MiscFlags = 0;
                HRESULT chr = device_->getDevice()->CreateTexture2D(&compatDesc, nullptr, &compat);
                if (SUCCEEDED(chr) && compat) {
                    device_->getContext()->CopyResource(compat.Get(), acquiredTexture.Get());
                    inputTex = compat;
                } else {
                    CAPSCR_LOG_WARN("captureZeroCopy: failed to create compute-compatible texture");
                    compat.Reset();
                }
            }

            ComPtr<ID3D11Texture2D> processed;
            CaptureParams params = *processingParams;
            if (params.output_width == 0) params.output_width = static_cast<int>(desc.Width);
            if (params.output_height == 0) params.output_height = static_cast<int>(desc.Height);
            Result pres = compute.processTexture(inputTex, params, processed);
            if (pres == Result::Ok && processed) {
                D3D11_TEXTURE2D_DESC pdesc; processed->GetDesc(&pdesc);
                outFrame.d3d11_device = device_->getDevice().Get();
                outFrame.d3d11_context = device_->getContext().Get();
                outFrame.d3d11_texture = processed.Get();
                outFrame.width = static_cast<int>(pdesc.Width);
                outFrame.height = static_cast<int>(pdesc.Height);
                outFrame.format = params.output_format;
                outFrame.mode = ZeroCopyFrame::Mode::LocalBorrow;

                // AddRef to keep alive until release
                device_->getDevice()->AddRef();
                device_->getContext()->AddRef();
                processed->AddRef();

                ComPtr<IDXGIOutputDuplication> dupRef = duplication_;
                ComPtr<ID3D11Texture2D> texRef = processed;
                ComPtr<ID3D11Device> devRef = device_->getDevice();
                ComPtr<ID3D11DeviceContext> ctxRef = device_->getContext();

                outFrame.release_callback = [dupRef, texRef, devRef, ctxRef]() mutable {
                    if (dupRef) dupRef->ReleaseFrame();
                    try { texRef.Reset(); devRef.Reset(); ctxRef.Reset(); } catch(...) {}
                };

                return Result::Ok;
            } else {
                CAPSCR_LOG_WARN("captureZeroCopy: GPU processing failed, returning raw texture");
                // fallthrough to raw path below
            }
        }
    }
#endif

    // NV12 conversion support in captureZeroCopy
    if (opts.prefer_nv12) {
        DXGICompute compute(device_->getDevice(), device_->getContext());
        if (compute.initialize() == Result::Ok) {
            ComPtr<ID3D11Texture2D> yPlane, uvPlane;
            Result cret = compute.convertToNV12(acquiredTexture, static_cast<UINT>(desc.Width), static_cast<UINT>(desc.Height), yPlane, uvPlane);
            if (cret == Result::Ok && yPlane && uvPlane) {
                CAPSCR_LOG_INFO("captureZeroCopy: NV12 conversion successful");
                
                // Fill ZeroCopyFrame with NV12 dual-texture semantic
                outFrame.d3d11_device = device_->getDevice().Get();
                outFrame.d3d11_context = device_->getContext().Get();
                outFrame.d3d11_texture = yPlane.Get();        // Y plane
                outFrame.d3d11_texture_uv = uvPlane.Get();    // UV plane
                outFrame.width = static_cast<int>(desc.Width);
                outFrame.height = static_cast<int>(desc.Height);
                outFrame.format = PixelFormat::NV12;
                outFrame.mode = ZeroCopyFrame::Mode::LocalBorrow;

                // AddRef to keep alive until release
                device_->getDevice()->AddRef();
                device_->getContext()->AddRef();
                yPlane->AddRef();
                uvPlane->AddRef();

                // Capture references for release callback
                ComPtr<IDXGIOutputDuplication> dupRef = duplication_;
                ComPtr<ID3D11Texture2D> yRef = yPlane;
                ComPtr<ID3D11Texture2D> uvRef = uvPlane;
                ComPtr<ID3D11Device> devRef = device_->getDevice();
                ComPtr<ID3D11DeviceContext> ctxRef = device_->getContext();

                outFrame.release_callback = [dupRef, yRef, uvRef, devRef, ctxRef]() mutable {
                    if (dupRef) dupRef->ReleaseFrame();
                    try { yRef.Reset(); uvRef.Reset(); devRef.Reset(); ctxRef.Reset(); } catch(...) {}
                };

                return Result::Ok;
            } else {
                CAPSCR_LOG_WARN("captureZeroCopy: NV12 conversion failed, falling back to regular texture");
                // fallthrough to regular path below
            }
        } else {
            CAPSCR_LOG_DEBUG("captureZeroCopy: DXGICompute not available for NV12");
        }
    }

    // Fill ZeroCopyFrame with local-borrow semantic. Caller holds frame and
    // should call release() to ReleaseFrame when finished.
#ifdef _WIN32
    outFrame.d3d11_device = device_->getDevice().Get();
    outFrame.d3d11_context = device_->getContext().Get();
    outFrame.d3d11_texture = acquiredTexture.Get();
#endif
    outFrame.width = static_cast<int>(desc.Width);
    outFrame.height = static_cast<int>(desc.Height);
    outFrame.format = PixelFormat::BGRA32;
    outFrame.mode = ZeroCopyFrame::Mode::LocalBorrow;

    // AddRef to keep objects alive until release
    device_->getDevice()->AddRef();
    device_->getContext()->AddRef();
    acquiredTexture->AddRef();

    // Capture smart references for release callback
    ComPtr<IDXGIOutputDuplication> dupRef = duplication_;
    ComPtr<ID3D11Texture2D> texRef = acquiredTexture;
    ComPtr<ID3D11Device> devRef = device_->getDevice();
    ComPtr<ID3D11DeviceContext> ctxRef = device_->getContext();

    outFrame.release_callback = [dupRef, texRef, devRef, ctxRef]() mutable {
        if (dupRef) {
            dupRef->ReleaseFrame();
        }
        try { texRef.Reset(); devRef.Reset(); ctxRef.Reset(); } catch(...) {}
    };

    return Result::Ok;
}

Result DXGICapture::captureSharedTexture(SharedTextureInfo& outInfo, const ZeroCopyOptions& opts) {
    std::lock_guard<std::mutex> lock(mutex_);

    if (!duplication_ || !device_->isValid()) {
        CAPSCR_LOG_ERROR("Shared texture capture not available");
        return Result::Unsupported;
    }

    // Acquire a frame
    DXGI_OUTDUPL_FRAME_INFO frameInfo;
    ComPtr<IDXGIResource> resource;
    Result result = acquireFrame(frameInfo, resource);
    if (result != Result::Ok) {
        return result;
    }

    ComPtr<ID3D11Texture2D> acquiredTexture;
    HRESULT hr = resource->QueryInterface(__uuidof(ID3D11Texture2D),
                                         reinterpret_cast<void**>(acquiredTexture.GetAddressOf()));
    if (FAILED(hr) || !acquiredTexture) {
        duplication_->ReleaseFrame();
        CAPSCR_LOG_ERROR("captureSharedTexture: failed to get ID3D11Texture2D from resource");
        return Result::Error;
    }

    // Get description from source texture (initial)
    D3D11_TEXTURE2D_DESC srcDesc;
    acquiredTexture->GetDesc(&srcDesc);

    // If prefer_nv12 is requested, try NV12 GPU conversion
    if (opts.prefer_nv12) {
        // Try NV12 GPU conversion path
        DXGICompute compute(device_->getDevice(), device_->getContext());
        if (compute.initialize() == Result::Ok) {
            ComPtr<ID3D11Texture2D> yPlane, uvPlane;
            Result cret = compute.convertToNV12(acquiredTexture, static_cast<UINT>(srcDesc.Width), static_cast<UINT>(srcDesc.Height), yPlane, uvPlane);
            if (cret != Result::Ok || !yPlane || !uvPlane) {
                CAPSCR_LOG_WARN("NV12 conversion failed or unavailable, falling back");
                // fallthrough to BGRA path below
            } else {
                // Create shareable textures for Y and UV planes
                D3D11_TEXTURE2D_DESC yDesc; yPlane->GetDesc(&yDesc);
                D3D11_TEXTURE2D_DESC uvDesc; uvPlane->GetDesc(&uvDesc);

                // Ensure shareable flag and optionally enable keyed mutex for cross-process sync
                // When using SHARED_NTHANDLE the resource must also include D3D11_RESOURCE_MISC_SHARED or D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX
                yDesc.Usage = D3D11_USAGE_DEFAULT; yDesc.CPUAccessFlags = 0; yDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
                yDesc.MiscFlags |= D3D11_RESOURCE_MISC_SHARED | D3D11_RESOURCE_MISC_SHARED_NTHANDLE | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX;
                uvDesc.Usage = D3D11_USAGE_DEFAULT; uvDesc.CPUAccessFlags = 0; uvDesc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
                uvDesc.MiscFlags |= D3D11_RESOURCE_MISC_SHARED | D3D11_RESOURCE_MISC_SHARED_NTHANDLE | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX;

                ComPtr<ID3D11Texture2D> sharedY, sharedUV;
                HRESULT hrY = device_->getDevice()->CreateTexture2D(&yDesc, nullptr, &sharedY);
                HRESULT hrUV = device_->getDevice()->CreateTexture2D(&uvDesc, nullptr, &sharedUV);
                if (FAILED(hrY) || FAILED(hrUV) || !sharedY || !sharedUV) {
                    CAPSCR_LOG_ERROR("Failed to create shared NV12 plane textures");
                    // continue to fallback
                } else {
                    // Copy GPU->GPU
                    device_->getContext()->CopyResource(sharedY.Get(), yPlane.Get());
                    device_->getContext()->CopyResource(sharedUV.Get(), uvPlane.Get());
                    device_->getContext()->Flush();

                    // Create shared handles
                    ComPtr<IDXGIResource1> dy, duv;
                    if (SUCCEEDED(sharedY.As(&dy)) && SUCCEEDED(sharedUV.As(&duv))) {
                        HANDLE hY = nullptr; HANDLE hUV = nullptr;
                        // Use GENERIC_ALL to maximize compatibility for consumers opening the handle
                        HRESULT h1 = dy->CreateSharedHandle(nullptr, GENERIC_ALL, nullptr, &hY);
                        HRESULT h2 = duv->CreateSharedHandle(nullptr, GENERIC_ALL, nullptr, &hUV);
                        if (SUCCEEDED(h1) && SUCCEEDED(h2) && hY && hUV) {
                            outInfo.shared_handle = hY;
                            outInfo.shared_handle_uv = hUV;
                            outInfo.dxgi_format = DXGI_FORMAT_NV12;
                            outInfo.width = static_cast<int>(srcDesc.Width);
                            outInfo.height = static_cast<int>(srcDesc.Height);
                            // Indicate keyed mutex usage and provide default key (consumer must AcquireSync)
                            outInfo.use_keyed_mutex = true;
                            outInfo.keyed_mutex_key = 0;

                            // Optionally try to release the keyed mutex on the produced resources
                            // so consumers can immediately acquire. Not all drivers require this,
                            // but attempt if IDXGIKeyedMutex is available.
                            ComPtr<IDXGIKeyedMutex> ky, kuv;
                            if (SUCCEEDED(sharedY.As(&ky))) {
                                ky->ReleaseSync(0);
                            }
                            if (SUCCEEDED(sharedUV.As(&kuv))) {
                                kuv->ReleaseSync(0);
                            }

                            duplication_->ReleaseFrame();
                            CAPSCR_LOG_INFO("Created NV12 shared handles with keyed mutex");
                            return Result::Ok;
                        } else {
                            if (hY) CloseHandle(hY);
                            if (hUV) CloseHandle(hUV);
                            CAPSCR_LOG_ERROR("CreateSharedHandle failed for NV12 planes");
                        }
                    }
                }
            }
        } else {
            CAPSCR_LOG_DEBUG("DXGICompute not available for NV12");
        }
        // If we reach here, NV12 path failed - fall back to BGRA path below
    }

    // Always run a GPU processing pass to normalize/convert to BGRA if needed.
    ComPtr<ID3D11Texture2D> processingSource = acquiredTexture;
    ComPtr<ID3D11Texture2D> processedTexture;
    {
        DXGICompute compute(device_->getDevice(), device_->getContext());
        if (compute.initialize() == Result::Ok) {
            CaptureParams procParams;
            procParams.output_width = static_cast<int>(srcDesc.Width);
            procParams.output_height = static_cast<int>(srcDesc.Height);
            procParams.output_format = PixelFormat::BGRA32;
            procParams.use_gpu_processing = true;

            Result pres = compute.processTexture(processingSource, procParams, processedTexture);
            if (pres == Result::Ok && processedTexture) {
                // Use processedTexture as source for sharing
                processingSource = processedTexture;
                // update srcDesc to reflect processed texture
                processingSource->GetDesc(&srcDesc);
            } else {
                CAPSCR_LOG_DEBUG("GPU processing skipped or failed, falling back to original texture");
            }
        } else {
            CAPSCR_LOG_DEBUG("DXGICompute not available, skipping GPU processing");
        }
    }

    // Create a shared texture with matching properties (based on processingSource)
    D3D11_TEXTURE2D_DESC sharedDesc = srcDesc;
    sharedDesc.Usage = D3D11_USAGE_DEFAULT;
    // Ensure CPUAccessFlags = 0 for shareable resource
    sharedDesc.CPUAccessFlags = 0;
    // Allow shader resource / render target binding as needed
    sharedDesc.BindFlags = srcDesc.BindFlags;
    // Use the conservative D3D11_RESOURCE_MISC_SHARED flag only for maximum compatibility
    // Some drivers have strict requirements for NTHANDLE/keyed mutex combinations; start with simple SHARED.
    sharedDesc.MiscFlags |= D3D11_RESOURCE_MISC_SHARED;

    ComPtr<ID3D11Texture2D> sharedTexture;
    hr = device_->getDevice()->CreateTexture2D(&sharedDesc, nullptr, &sharedTexture);
    if (FAILED(hr) || !sharedTexture) {
        CAPSCR_LOG_ERROR("Failed to create shared texture: %s", hrToString(hr).c_str());
        duplication_->ReleaseFrame();
        return Result::Error;
    }

    // If GPU processing is required (resize/convert) we would invoke DXGICompute here
    // For now perform a GPU->GPU copy
    // Copy from the processing source (may be acquiredTexture or processedTexture)
    device_->getContext()->CopyResource(sharedTexture.Get(), processingSource.Get());
    device_->getContext()->Flush();

    // Obtain IDXGIResource1 to create shared handle
    ComPtr<IDXGIResource1> dxgiRes1;
    hr = sharedTexture.As(&dxgiRes1);
    if (FAILED(hr) || !dxgiRes1) {
        CAPSCR_LOG_ERROR("Failed to QI IDXGIResource1 for shared texture: %s", hrToString(hr).c_str());
        duplication_->ReleaseFrame();
        return Result::Error;
    }

    HANDLE sharedHandle = nullptr;
    // SECURITY_ATTRIBUTES can be null to create an unnamed handle
    // Use GENERIC_ALL for the access mask to improve cross-process compatibility
    hr = dxgiRes1->CreateSharedHandle(nullptr, GENERIC_ALL, nullptr, &sharedHandle);
    if (FAILED(hr) || !sharedHandle) {
        CAPSCR_LOG_ERROR("CreateSharedHandle failed: %s", hrToString(hr).c_str());
        duplication_->ReleaseFrame();
        return Result::Error;
    }

    // Fill out SharedTextureInfo and advertise keyed mutex usage
    outInfo.shared_handle = sharedHandle;
    outInfo.dxgi_format = srcDesc.Format;
    outInfo.width = static_cast<int>(srcDesc.Width);
    outInfo.height = static_cast<int>(srcDesc.Height);
    outInfo.use_keyed_mutex = true;
    outInfo.keyed_mutex_key = 0;

    // Try to release keyed mutex on the produced shared texture so consumers may AcquireSync immediately
    ComPtr<IDXGIKeyedMutex> prodKm;
    if (SUCCEEDED(sharedTexture.As(&prodKm))) {
        prodKm->ReleaseSync(0);
    }

    // Release the duplication frame immediately - consumer will open shared handle
    duplication_->ReleaseFrame();

    CAPSCR_LOG_INFO("Created shared handle for texture");
    return Result::Ok;
}

bool DXGICapture::isGpuCaptureAvailable() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return duplication_ && device_->isValid();
}

Result DXGICapture::setTarget(CaptureTargetType type, const std::string& displayId,
                             const Rect* optTarget, const std::string& optWindowId) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    targetType_ = type;
    displayId_ = displayId;
    targetRect_ = {};
    
    if (optTarget) {
        targetRect_ = *optTarget;
    }
    
    return Result::Ok;
}

Rect DXGICapture::getTarget() const {
    std::lock_guard<std::mutex> lock(targetMutex_);
    return targetRect_;
}

BackendCapability DXGICapture::getCapabilities() const {
    BackendCapability caps = BackendCapability::SupportFullScreen | 
                            BackendCapability::SupportRegion | 
                            BackendCapability::SupportWindow | 
                            BackendCapability::HighPerformance;
    
    if (isGpuCaptureAvailable()) {
        caps |= BackendCapability::ZeroCopyGpu;
    }
    
    return caps;
}

void DXGICapture::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    unregisterWindowEvents();
    
    duplication_.Reset();
    textureManager_.reset();
    
    if (device_) {
        device_->shutdown();
    }
    
    CAPSCR_LOG_DEBUG("DXGICapture shutdown complete");
}

void DXGICapture::handleWinEventUpdate(HWND hwnd) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (targetHwnd_ != hwnd) {
        return;
    }
    
    updateWindowRect();
    ++winEventCount_;
    
    CAPSCR_LOG_TRACE("Window event update handled");
}

Result DXGICapture::setupWindowTarget(const std::string& windowId) {
    targetHwnd_ = findWindow(windowId);
    if (!targetHwnd_) {
        CAPSCR_LOG_ERROR("Window not found");
        return Result::WindowNotFound;
    }
    
    if (updateWindowRect() != Result::Ok) {
        CAPSCR_LOG_ERROR("Failed to get window rectangle");
        return Result::Error;
    }
    
    registerWindowEvents(targetHwnd_);
    
    CAPSCR_LOG_INFO("Window target setup complete");
    return Result::Ok;
}

Result DXGICapture::updateWindowRect() {
    if (!targetHwnd_) {
        return Result::Error;
    }
    
    Rect windowRect = getWindowRect(targetHwnd_);
    if (windowRect.width <= 0 || windowRect.height <= 0) {
        return Result::Error;
    }
    
    std::lock_guard<std::mutex> targetLock(targetMutex_);
    targetRect_ = windowRect;
    
    return Result::Ok;
}

Result DXGICapture::acquireFrame(DXGI_OUTDUPL_FRAME_INFO& frameInfo, ComPtr<IDXGIResource>& resource) {
    HRESULT hr = duplication_->AcquireNextFrame(500, &frameInfo, &resource);
    
    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        return Result::Error; // No new frame available
    }
    
        if (hr == DXGI_ERROR_ACCESS_LOST) {
            return Result::Error; // Changed from DeviceLost to Error
        }    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("AcquireNextFrame failed");
        return Result::Error;
    }
    
    return Result::Ok;
}

Result DXGICapture::handleDeviceLost() {
    CAPSCR_LOG_INFO("Handling device lost/removed");
    
    duplication_.Reset();
    
    Result result = device_->recreateDevice();
    if (result != Result::Ok) {
        return result;
    }
    
    // Recreate texture manager with new device
    textureManager_ = std::make_unique<DXGITexture>(device_->getDevice(), device_->getContext());
    
    // Recreate duplication
    if (!recreateDuplication()) {
        return Result::Error;
    }
    
    CAPSCR_LOG_INFO("Device recovery successful");
    return Result::Ok;
}

bool DXGICapture::recreateDuplication() {
    return device_->createDuplication(displayId_, duplication_, adapterIndex_, outputIndex_, displayId_);
}

HWND DXGICapture::findWindow(const std::string& windowId) {
    // Try hex format (0x...)
    if (windowId.size() > 2 && windowId[0] == '0' && (windowId[1] == 'x' || windowId[1] == 'X')) {
        unsigned long long value = strtoull(windowId.c_str(), nullptr, 16);
        return reinterpret_cast<HWND>(value);
    }
    
    // Try decimal format
    char* end = nullptr;
    unsigned long long value = strtoull(windowId.c_str(), &end, 10);
    if (end && *end == '\\0') {
        return reinterpret_cast<HWND>(value);
    }
    
    // Try by window title
    return FindWindowA(nullptr, windowId.c_str());
}

Rect DXGICapture::getWindowRect(HWND hwnd) {
    RECT winRect;
    if (!GetWindowRect(hwnd, &winRect)) {
        return {};
    }
    
    // Get monitor info to convert to relative coordinates
    HMONITOR monitor = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONULL);
    if (monitor) {
        MONITORINFO monitorInfo = {};
        monitorInfo.cbSize = sizeof(MONITORINFO);
        if (GetMonitorInfoA(monitor, &monitorInfo)) {
            return {
                winRect.left - monitorInfo.rcMonitor.left,
                winRect.top - monitorInfo.rcMonitor.top,
                winRect.right - winRect.left,
                winRect.bottom - winRect.top
            };
        }
    }
    
    // Fallback to absolute coordinates
    return {
        winRect.left,
        winRect.top,
        winRect.right - winRect.left,
        winRect.bottom - winRect.top
    };
}

bool DXGICapture::isWindowValid(HWND hwnd) {
    return IsWindow(hwnd) && IsWindowVisible(hwnd) && !IsIconic(hwnd);
}

Result DXGICapture::gdiCapture(const Rect& region, Frame& outFrame) {
    // Simple GDI fallback implementation
    HDC screenDC = GetDC(nullptr);
    HDC memDC = CreateCompatibleDC(screenDC);
    
    if (!memDC) {
        ReleaseDC(nullptr, screenDC);
        return Result::Error;
    }
    
    HBITMAP bitmap = CreateCompatibleBitmap(screenDC, region.width, region.height);
    if (!bitmap) {
        DeleteDC(memDC);
        ReleaseDC(nullptr, screenDC);
        return Result::Error;
    }
    
    SelectObject(memDC, bitmap);
    
    BOOL success = BitBlt(memDC, 0, 0, region.width, region.height,
                         screenDC, region.x, region.y, SRCCOPY | CAPTUREBLT);
    
    if (!success) {
        DeleteObject(bitmap);
        DeleteDC(memDC);
        ReleaseDC(nullptr, screenDC);
        return Result::Error;
    }
    
    // Convert to frame format
    BITMAPINFO bmi = {};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = region.width;
    bmi.bmiHeader.biHeight = -region.height; // Top-down
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32;
    bmi.bmiHeader.biCompression = BI_RGB;
    
    outFrame.width = region.width;
    outFrame.height = region.height;
    outFrame.format = PixelFormat::BGRA32;
    outFrame.stride = region.width * 4;
    outFrame.data.resize(outFrame.stride * region.height);
    
    int scanlines = GetDIBits(memDC, bitmap, 0, region.height,
                             outFrame.data.data(), &bmi, DIB_RGB_COLORS);
    
    DeleteObject(bitmap);
    DeleteDC(memDC);
    ReleaseDC(nullptr, screenDC);
    
    if (scanlines <= 0) {
        return Result::Error;
    }
    
        CAPSCR_LOG_DEBUG("GDI fallback capture successful");
    return Result::Ok;
}

void DXGICapture::registerWindowEvents(HWND hwnd) {
    if (winEventHook_) {
        return; // Already registered
    }
    
    {
        std::lock_guard<std::mutex> lock(g_windowMapMutex);
        g_windowMap[hwnd] = this;
    }
    
    winEventHook_ = SetWinEventHook(
        EVENT_OBJECT_LOCATIONCHANGE, EVENT_OBJECT_LOCATIONCHANGE,
        nullptr, GlobalWinEventProc,
        0, 0, WINEVENT_OUTOFCONTEXT | WINEVENT_SKIPOWNPROCESS
    );
    
    if (!winEventHook_) {
        CAPSCR_LOG_WARN("Failed to register window event hook");
        std::lock_guard<std::mutex> lock(g_windowMapMutex);
        g_windowMap.erase(hwnd);
    } else {
        CAPSCR_LOG_DEBUG("Window event hook registered");
    }
}

void DXGICapture::unregisterWindowEvents() {
    if (winEventHook_) {
        UnhookWinEvent(winEventHook_);
        winEventHook_ = nullptr;
    }
    
    if (targetHwnd_) {
        std::lock_guard<std::mutex> lock(g_windowMapMutex);
        g_windowMap.erase(targetHwnd_);
        targetHwnd_ = nullptr;
    }
}

} // namespace capscr

#endif // _WIN32
