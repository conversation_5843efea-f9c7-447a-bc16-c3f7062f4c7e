#pragma once
#ifdef _WIN32

#include <windows.h>
#include <dxgi1_2.h>
#include <d3d11.h>
#include <wrl.h>
#include <string>
#include <memory>

#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/logging.hpp"

using Microsoft::WRL::ComPtr;

namespace capscr {

/**
 * Manages D3D11 device creation, destruction, and recovery
 */
class DXGIDevice {
public:
    DXGIDevice();
    ~DXGIDevice();

    // Initialize D3D11 device and context
    Result initialize();
    
    // Clean up device resources
    void shutdown();
    
    // Try to recreate device after device lost/removed
    Result recreateDevice();
    
    // Check if device is valid and ready
    bool isValid() const;
    
    // Get device and context
    ComPtr<ID3D11Device> getDevice() const { return device_; }
    ComPtr<ID3D11DeviceContext> getContext() const { return context_; }
    
    // Try to create duplication for specified display
    bool createDuplication(const std::string& displayId, 
                          ComPtr<IDXGIOutputDuplication>& outDup,
                          int& outAdapter, 
                          int& outOutput, 
                          std::string& outMatchedId);

    // List available displays
    std::vector<DisplayInfo> listDisplays();

private:
    ComPtr<ID3D11Device> device_;
    ComPtr<ID3D11DeviceContext> context_;
    
    // Device feature level achieved
    D3D_FEATURE_LEVEL featureLevel_;
    
    // Helper to convert HRESULT to string
    std::string hrToString(HRESULT hr);
    
    // Try to enumerate adapters and outputs
    bool enumerateOutputs(const std::string& displayId,
                         ComPtr<IDXGIOutputDuplication>& outDup,
                         int& outAdapter,
                         int& outOutput,
                         std::string& outMatchedId);
};

} // namespace capscr

#endif // _WIN32
