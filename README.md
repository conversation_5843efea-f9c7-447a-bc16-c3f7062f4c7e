# capscr — cross-platform screen capture library (C++17)

This repository provides a cross-platform C++17 library skeleton for desktop screen/window capture. The goal is to support multiple platform backends and expose a unified API.

Current contents:
- Public API header: `include/capscr/capture.hpp`
- Example: `examples/example_capture.cpp`
- CMake build

Planned backends (priority):
- Windows: GDI, DXGI, Magnification, WGC (GDI implemented first)
- Linux: X11, Wayland/pipewire
- macOS: CoreGraphics

Notes:
- The library returns `Result::Unsupported` for features/backends that aren't implemented or available.
- See referenced projects for implementation ideas: rustdesk scrap, Captura, etc.

## Build & Usage

This project uses CMake. Below are example commands for common platforms and toolchains.

Windows (MSVC - Visual Studio)

1. (Optional) Install vcpkg and lodepng if you want PNG support:

```bash
# from PowerShell or bash
git clone https://github.com/microsoft/vcpkg.git
.\vcpkg\bootstrap-vcpkg.bat
.\vcpkg\vcpkg install lodepng:x64-windows
```

1. Configure and build with the vcpkg toolchain (recommended if using vcpkg):

```bash
mkdir build
cd build
cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_TOOLCHAIN_FILE=C:/path/to/vcpkg/scripts/buildsystems/vcpkg.cmake ..
cmake --build . --config Release
```

1. Or without vcpkg (uses system SDK/GDI+):

```bash
mkdir build && cd build
cmake -G "Visual Studio 17 2022" -A x64 ..
cmake --build . --config Release
```

Windows (MinGW)

```bash
mkdir build && cd build
cmake -G "MinGW Makefiles" ..
cmake --build . --config Release
```

Linux (Unix Makefiles / Ninja)

```bash
mkdir build && cd build
cmake -G "Unix Makefiles" ..
make -j$(nproc)
```

Using vcpkg with CMake

- When you install `lodepng` via vcpkg and pass the `-DCMAKE_TOOLCHAIN_FILE=.../vcpkg.cmake` to CMake, the project will automatically detect the `lodepng.h` header and define `HAVE_LODEPNG` so PNG saving uses lodepng. If vcpkg is not used, the project uses GDI+ on Windows or falls back to a simple BMP writer on other platforms.

Run the example

- After building, run the example executable in `examples/Release/example_capture.exe` (or the equivalent built path). You can optionally pass a window title as the first argument to capture a specific window non-interactively:

```bash
examples/Release/example_capture.exe "Untitled - Notepad"
```

Notes

- If Visual Studio reports `pwsh.exe` not found during custom steps, that only affects some post-build helper steps and does not prevent the binaries from being produced; install PowerShell Core or ignore the message.
- The library is a work in progress; Linux/macOS backends are planned and will be added.
