#pragma once
#include <windows.h>
#include <string>
#include <memory>

/**
 * Test window helper - creates a controlled test window with known content
 * for reliable unit testing of capture functionality
 */
class TestWindow {
public:
    struct TestPattern {
        int width = 400;
        int height = 300;
        COLORREF backgroundColor = RGB(255, 0, 0); // Red background
        COLORREF textColor = RGB(255, 255, 255);   // White text
        std::string text = "TEST_CAPTURE_CONTENT";
        
        // Known test pattern areas for verification
        struct {
            int x = 50, y = 50, width = 100, height = 50;
            COLORREF color = RGB(0, 255, 0); // Green rectangle
        } testRect;
        
        struct {
            int x = 200, y = 100, width = 80, height = 80;
            COLORREF color = RGB(0, 0, 255); // Blue circle area
        } testCircle;
    };

    TestWindow();
    ~TestWindow();
    
    // Create and show the test window
    bool Create(const TestPattern& pattern = TestPattern{});
    
    // Get window handle for capture testing
    HWND GetHandle() const { return hwnd_; }
    
    // Get expected pattern for verification
    const TestPattern& GetPattern() const { return pattern_; }
    
    // Force window to repaint (useful for testing updates)
    void Repaint();
    
    // Move window to specific position
    void MoveTo(int x, int y);
    
    // Resize window
    void Resize(int width, int height);
    
    // Destroy the window
    void Destroy();
    
    // Check if a pixel in captured data matches expected color (with tolerance)
    static bool VerifyPixel(const uint8_t* frameData, int frameWidth, int frameStride,
                           int x, int y, COLORREF expectedColor, int tolerance = 10);
    
    // Verify that a rectangular area matches expected color
    static bool VerifyRect(const uint8_t* frameData, int frameWidth, int frameStride,
                          int rectX, int rectY, int rectW, int rectH, 
                          COLORREF expectedColor, int tolerance = 10, float matchThreshold = 0.8f);

private:
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    void OnPaint(HDC hdc);
    void DrawTestPattern(HDC hdc);
    
    HWND hwnd_ = nullptr;
    TestPattern pattern_;
    bool created_ = false;
    
    static const char* WINDOW_CLASS_NAME;
    static bool classRegistered_;
};
