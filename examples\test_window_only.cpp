#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include "capscr/capture.hpp"
#ifdef _WIN32
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include <windows.h>
#endif
#ifdef HAVE_LODEPNG
#include <lodepng.h>
#endif

using namespace capscr;

int main() {
    auto capturer = createBestCapturer();
    if (!capturer) {
        std::cerr << "No capturer available.\n";
        return 1;
    }

    std::string windowTitle = "Char As Gem";
    
    // First, let's verify the window state
    HWND hwnd = FindWindowA(nullptr, windowTitle.c_str());
    if (hwnd) {
        RECT rect;
        GetWindowRect(hwnd, &rect);
        std::cout << "Direct window check:\n";
        std::cout << "  HWND: " << hwnd << "\n";
        std::cout << "  Rect: (" << rect.left << "," << rect.top << "," << rect.right << "," << rect.bottom << ")\n";
        std::cout << "  Size: " << (rect.right-rect.left) << "x" << (rect.bottom-rect.top) << "\n";
        std::cout << "  Visible: " << (IsWindowVisible(hwnd) ? "Yes" : "No") << "\n";
        std::cout << "  Minimized: " << (IsIconic(hwnd) ? "Yes" : "No") << "\n";
        
        char className[256];
        GetClassNameA(hwnd, className, sizeof(className));
        std::cout << "  Class: " << className << "\n";
    } else {
        std::cout << "Window not found with FindWindowA\n";
    }
    
    Result r = capturer->init(CaptureTargetType::Window, "", nullptr, windowTitle);
    if (r != Result::Ok) {
        std::cout << "Window init failed with result: " << (int)r << "\n";
        return 1;
    }

    std::cout << "Window capture initialized successfully.\n";
    
    Frame f;
    r = capturer->capture(f);
    if (r == Result::Ok) {
        std::cout << "SUCCESS - Captured window frame: " << f.width << "x" << f.height << "\n";
        
        // Save as PNG
        bool wrote = false;
        std::string filename = "test_window_capture.png";
        
#ifdef HAVE_LODEPNG
        // Convert BGRA -> RGBA for lodepng
        std::vector<uint8_t> rgba;
        rgba.resize(f.width * f.height * 4);
        for (int y = 0; y < f.height; ++y) {
            const uint8_t* src = f.data.data() + y * f.stride;
            for (int x = 0; x < f.width; ++x) {
                int si = x * 4;
                int di = (y * f.width + x) * 4;
                rgba[di+0] = src[si+2]; // R
                rgba[di+1] = src[si+1]; // G
                rgba[di+2] = src[si+0]; // B
                rgba[di+3] = src[si+3]; // A
            }
        }
        unsigned err = lodepng::encode(filename, rgba, f.width, f.height);
        wrote = (err == 0);
        if (err != 0) {
            std::cout << "PNG encode error: " << lodepng_error_text(err) << "\n";
        }
#else
    std::cout << "HAVE_LODEPNG not defined - skipping saving window capture (no BMP fallback)\n";
#endif
        
        if (wrote) {
            std::cout << "Saved window capture as: " << filename << "\n";
        } else {
            std::cout << "Failed to save window capture\n";
        }
        
        // Also try direct GDI capture for comparison
        std::cout << "\nTrying direct GDI capture for comparison...\n";
        if (hwnd && IsWindowVisible(hwnd)) {
            RECT rect;
            GetWindowRect(hwnd, &rect);
            int width = rect.right - rect.left;
            int height = rect.bottom - rect.top;
            
            HDC hScreenDC = GetDC(nullptr);
            HDC hMemDC = CreateCompatibleDC(hScreenDC);
            HBITMAP hBitmap = CreateCompatibleBitmap(hScreenDC, width, height);
            SelectObject(hMemDC, hBitmap);
            
            // Try different capture methods
            std::cout << "Method 1: BitBlt from screen coordinates\n";
            BOOL result1 = BitBlt(hMemDC, 0, 0, width, height, hScreenDC, rect.left, rect.top, SRCCOPY);
            std::cout << "BitBlt result: " << (result1 ? "Success" : "Failed") << "\n";
            
            // Cleanup
            DeleteObject(hBitmap);
            DeleteDC(hMemDC);
            ReleaseDC(nullptr, hScreenDC);
        }
    } else {
        std::cout << "ERROR - Window capture failed with result: " << (int)r << "\n";
    }

    capturer->shutdown();
    return 0;
}
