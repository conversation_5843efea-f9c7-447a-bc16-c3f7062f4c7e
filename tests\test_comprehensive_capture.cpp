#include <gtest/gtest.h>
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include "capscr/capture.hpp"
#include "capture_test_environment.hpp"
#include <windows.h>
#include <memory>
#include <thread>
#include <chrono>

using namespace capscr;

/**
 * Comprehensive capture tests covering all scenarios:
 * - Window capture, full screen, region capture
 * - Multi-monitor scenarios
 * - Exception handling (minimized, closed, cross-screen windows)
 * - Dynamic capture during window changes
 */
class ComprehensiveCaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        env_ = CaptureTestEnvironment::Instance();
        capturer_ = createDXGICapturer();
        ASSERT_NE(capturer_, nullptr);
    }
    
    void TearDown() override {
        if (capturer_) {
            capturer_->shutdown();
        }
    }
    
    // Helper to verify capture success and basic properties
    bool VerifyBasicCapture(const Frame& frame, const std::string& testName) {
        if (frame.width <= 0 || frame.height <= 0) {
            std::cout << testName << ": Invalid frame dimensions " << frame.width << "x" << frame.height << std::endl;
            return false;
        }
        
        if (frame.data.empty() || frame.data.size() != frame.stride * frame.height) {
            std::cout << testName << ": Invalid frame data size" << std::endl;
            return false;
        }
        
        std::cout << testName << ": Captured " << frame.width << "x" << frame.height << " frame" << std::endl;
        return true;
    }
    
    // Helper to perform content verification
    bool VerifyTestWindowContent(const Frame& frame, TestWindow* window, const std::string& testName) {
        if (!VerifyBasicCapture(frame, testName)) return false;
        
        const TestWindow::TestPattern& pattern = window->GetPattern();
        
        // Check background color (sample multiple areas to be more robust)
        bool hasBackground = false;
        std::vector<std::pair<int, int>> samplePoints = {{50, 50}, {100, 100}, {30, 30}};
        
        for (const auto& point : samplePoints) {
            if (point.first < frame.width && point.second < frame.height) {
                if (TestWindow::VerifyPixel(frame.data.data(), frame.width, frame.stride,
                                          point.first, point.second, pattern.backgroundColor, 50)) {
                    hasBackground = true;
                    break;
                }
            }
        }
        
        if (hasBackground) {
            std::cout << testName << ": [OK] Content verification passed" << std::endl;
            return true;
        } else {
            std::cout << testName << ": [WARN] Content verification failed (may be occluded)" << std::endl;
            return false; // Still not a fatal error
        }
    }
    
    CaptureTestEnvironment* env_;
    std::unique_ptr<ICapturer> capturer_;
};

// === Window Capture Tests ===

TEST_F(ComprehensiveCaptureTest, PrimaryWindowCapture) {
    TestWindow* window = env_->GetPrimaryTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    if (result == Result::Ok) {
        Frame frame;
        EXPECT_EQ(capturer_->capture(frame), Result::Ok);
        EXPECT_TRUE(VerifyBasicCapture(frame, "PrimaryWindowCapture"));
        // Content verification is optional as window may be occluded
        VerifyTestWindowContent(frame, window, "PrimaryWindowCapture");
    } else {
        GTEST_SKIP() << "Window capture initialization failed";
    }
}

TEST_F(ComprehensiveCaptureTest, SecondaryWindowCapture) {
    TestWindow* window = env_->GetSecondaryTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    if (result == Result::Ok) {
        Frame frame;
        EXPECT_EQ(capturer_->capture(frame), Result::Ok);
        EXPECT_TRUE(VerifyBasicCapture(frame, "SecondaryWindowCapture"));
        // Content verification is optional as window may be occluded  
        VerifyTestWindowContent(frame, window, "SecondaryWindowCapture");
    } else {
        GTEST_SKIP() << "Window capture initialization failed";
    }
}

// === Full Screen Capture Tests ===

TEST_F(ComprehensiveCaptureTest, PrimaryMonitorFullScreenCapture) {
    Result result = capturer_->init(CaptureTargetType::FullScreen, "", nullptr, "");
    
    if (result == Result::Ok) {
        Frame frame;
        EXPECT_EQ(capturer_->capture(frame), Result::Ok);
        
        RECT primaryRect = env_->GetPrimaryDesktopRect();
        int expectedWidth = primaryRect.right - primaryRect.left;
        int expectedHeight = primaryRect.bottom - primaryRect.top;
        
        std::cout << "Full screen capture: " << frame.width << "x" << frame.height 
                  << " (expected ~" << expectedWidth << "x" << expectedHeight << ")" << std::endl;
        
        // Allow for some variation due to DPI scaling
        EXPECT_GT(frame.width, expectedWidth / 2);
        EXPECT_GT(frame.height, expectedHeight / 2);
        EXPECT_TRUE(VerifyBasicCapture(frame, "PrimaryMonitorFullScreen"));
    } else {
        GTEST_SKIP() << "Full screen capture not supported";
    }
}

// === Region Capture Tests ===

TEST_F(ComprehensiveCaptureTest, RegionCapture) {
    // Define a specific region to capture
    Rect region = {100, 100, 200, 150}; // 200x150 region at (100,100)
    
    Result result = capturer_->init(CaptureTargetType::Region, "", &region, "");
    
    if (result == Result::Ok) {
        Frame frame;
        EXPECT_EQ(capturer_->capture(frame), Result::Ok);
        EXPECT_TRUE(VerifyBasicCapture(frame, "RegionCapture"));
        
        // Verify region dimensions (allowing for scaling)
        EXPECT_GT(frame.width, 50);  // At least some reasonable size
        EXPECT_GT(frame.height, 50);
        
        std::cout << "Region capture: requested " << region.width << "x" << region.height 
                  << ", got " << frame.width << "x" << frame.height << std::endl;
    } else {
        GTEST_SKIP() << "Region capture not supported";
    }
}

// === Exception Handling Tests ===

TEST_F(ComprehensiveCaptureTest, MinimizedWindowCapture) {
    TestWindow* window = env_->GetDynamicTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    Result initResult = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    if (initResult != Result::Ok) {
        GTEST_SKIP() << "Window capture initialization failed";
    }
    
    // First capture normal window
    Frame normalFrame;
    Result normalResult = capturer_->capture(normalFrame);
    EXPECT_EQ(normalResult, Result::Ok);
    
    // Minimize window and try to capture
    env_->MinimizeWindow(window);
    
    Frame minimizedFrame;
    Result minimizedResult = capturer_->capture(minimizedFrame);
    
    // Should either fail gracefully or succeed with different content
    EXPECT_TRUE(minimizedResult == Result::Ok || 
                minimizedResult == Result::WindowMinimized ||
                minimizedResult == Result::Error);
    
    std::cout << "Minimized window capture result: " << static_cast<int>(minimizedResult) << std::endl;
    
    // Restore window
    env_->RestoreWindow(window);
}

TEST_F(ComprehensiveCaptureTest, HiddenWindowCapture) {
    TestWindow* window = env_->GetDynamicTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    Result initResult = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    if (initResult != Result::Ok) {
        GTEST_SKIP() << "Window capture initialization failed";
    }
    
    // Hide window and try to capture
    env_->CloseWindow(window);
    
    Frame hiddenFrame;
    Result hiddenResult = capturer_->capture(hiddenFrame);
    
    // Should fail gracefully
    EXPECT_TRUE(hiddenResult == Result::WindowClosed ||
                hiddenResult == Result::WindowNotFound ||
                hiddenResult == Result::Error);
    
    std::cout << "Hidden window capture result: " << static_cast<int>(hiddenResult) << std::endl;
    
    // Restore window
    env_->RestoreWindow(window);
}

// === Multi-Monitor Tests ===

TEST_F(ComprehensiveCaptureTest, MultiMonitorWindowMovement) {
    if (!env_->HasMultipleMonitors()) {
        GTEST_SKIP() << "Multiple monitors not available";
    }
    
    TestWindow* window = env_->GetDynamicTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    
    auto monitors = env_->GetMonitors();
    for (size_t i = 0; i < monitors.size() && i < 3; ++i) { // Test first 3 monitors max
        std::cout << "Testing capture on monitor " << i << std::endl;
        
        // Move window to this monitor
        env_->MoveWindowToMonitor(window, static_cast<int>(i));
        
        // Initialize capturer for this monitor
        capturer_->shutdown();
        capturer_ = createDXGICapturer();
        
        Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
        if (result == Result::Ok) {
            Frame frame;
            Result captureResult = capturer_->capture(frame);
            EXPECT_EQ(captureResult, Result::Ok);
            EXPECT_TRUE(VerifyBasicCapture(frame, "Monitor" + std::to_string(i) + "Capture"));
        }
    }
}

// === Dynamic Capture Tests ===

TEST_F(ComprehensiveCaptureTest, ContinuousCapturesDuringWindowMovement) {
    TestWindow* window = env_->GetDynamicTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    if (result != Result::Ok) {
        GTEST_SKIP() << "Window capture initialization failed";
    }
    
    std::cout << "Starting continuous capture during window movement..." << std::endl;
    
    // Perform captures while moving window
    std::vector<std::pair<int, int>> positions = {{200, 200}, {400, 300}, {600, 150}, {300, 400}};
    
    for (size_t i = 0; i < positions.size(); ++i) {
        // Move window
        window->MoveTo(positions[i].first, positions[i].second);
        
        // Small delay for movement
        Sleep(50);
        
        // Capture
        Frame frame;
        Result captureResult = capturer_->capture(frame);
        
        if (captureResult == Result::Ok) {
            bool verified = VerifyTestWindowContent(frame, window, 
                "MovementCapture" + std::to_string(i));
            // Don't fail if content verification fails during movement - just note it
            std::cout << "Movement capture " << i << ": " << (verified ? "OK" : "WARN") << std::endl;
        }
        
        // Another capture after brief pause
        Sleep(100);
        Frame frame2;
        Result captureResult2 = capturer_->capture(frame2);
        EXPECT_TRUE(captureResult2 == Result::Ok || captureResult2 == Result::Error);
    }
    
    std::cout << "Continuous capture test completed" << std::endl;
}

TEST_F(ComprehensiveCaptureTest, ContinuousCapturesDuringWindowResize) {
    TestWindow* window = env_->GetDynamicTestWindow();
    ASSERT_NE(window, nullptr);
    
    std::string windowId = std::to_string(reinterpret_cast<uintptr_t>(window->GetHandle()));
    Result result = capturer_->init(CaptureTargetType::Window, "", nullptr, windowId);
    
    if (result != Result::Ok) {
        GTEST_SKIP() << "Window capture initialization failed";
    }
    
    std::cout << "Starting continuous capture during window resize..." << std::endl;
    
    // Perform captures while resizing window
    std::vector<std::pair<int, int>> sizes = {{300, 200}, {500, 400}, {200, 150}, {400, 300}};
    
    for (size_t i = 0; i < sizes.size(); ++i) {
        // Resize window
        window->Resize(sizes[i].first, sizes[i].second);
        
        // Small delay for resize
        Sleep(100);
        
        // Capture
        Frame frame;
        Result captureResult = capturer_->capture(frame);
        
        if (captureResult == Result::Ok) {
            EXPECT_TRUE(VerifyBasicCapture(frame, "ResizeCapture" + std::to_string(i)));
            std::cout << "Resize " << i << ": window=" << sizes[i].first << "x" << sizes[i].second 
                      << " capture=" << frame.width << "x" << frame.height << std::endl;
        }
    }
    
    std::cout << "Resize capture test completed" << std::endl;
}
