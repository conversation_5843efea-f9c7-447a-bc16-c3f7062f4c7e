#pragma once
#ifdef _WIN32

#include <windows.h>
#include <d3d11.h>
#include <wrl.h>
#include <memory>

#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/logging.hpp"

using Microsoft::WRL::ComPtr;

namespace capscr {

/**
 * Manages D3D11 texture operations for screen capture
 */
class DXGITexture {
public:
    DXGITexture(ComPtr<ID3D11Device> device, ComPtr<ID3D11DeviceContext> context);
    ~DXGITexture();

    // Create staging texture for CPU readback
    Result createStagingTexture(UINT width, UINT height, DXGI_FORMAT format,
                               ComPtr<ID3D11Texture2D>& outTexture);

    // Create GPU texture for processing
    Result createGpuTexture(UINT width, UINT height, DXGI_FORMAT format,
                           ComPtr<ID3D11Texture2D>& outTexture);

    // Copy full texture to staging and read back to Frame
    Result copyFullTexture(ComPtr<ID3D11Texture2D> source, Frame& outFrame);

    // Copy subregion to staging and read back to Frame
    Result copySubregion(ComPtr<ID3D11Texture2D> source, const Rect& region, Frame& outFrame);

    // Map staging texture and copy data to Frame
    Result mapAndCopyTexture(ComPtr<ID3D11Texture2D> stagingTexture, 
                           UINT width, UINT height, Frame& outFrame);

    // Create GPU texture with specific usage and bind flags
    Result createTexture2D(const D3D11_TEXTURE2D_DESC& desc, 
                          ComPtr<ID3D11Texture2D>& outTexture);

private:
    ComPtr<ID3D11Device> device_;
    ComPtr<ID3D11DeviceContext> context_;

    // Helper to convert HRESULT to string
    std::string hrToString(HRESULT hr);

    // Validate texture dimensions and format
    bool validateTextureDesc(const D3D11_TEXTURE2D_DESC& desc);
};

} // namespace capscr

#endif // _WIN32
