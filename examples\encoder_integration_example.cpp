/**
 * @file encoder_integration_example.cpp
 * @brief Example showing how to integrate NV12 capture with a video encoder
 * 
 * This example demonstrates:
 * - NV12 capture with shared handles
 * - Cross-device texture sharing for encoder integration
 * - Proper resource management
 * - Error handling for encoder scenarios
 */

#include <capscr/capture.hpp>
#include <iostream>
#include <memory>
#include <vector>

#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <wrl/client.h>
using Microsoft::WRL::ComPtr;
#endif

class EncoderIntegrationExample {
private:
    std::unique_ptr<capscr::ICapturer> capturer_;
#ifdef _WIN32
    ComPtr<ID3D11Device> encoderDevice_;
    ComPtr<ID3D11DeviceContext> encoderContext_;
#endif

public:
    bool initialize() {
        std::cout << "Initializing Encoder Integration Example..." << std::endl;

        // Create capturer
        capturer_ = capscr::createDxgiCapturer();
        if (!capturer_) {
            std::cerr << "Error: Failed to create DXGI capturer" << std::endl;
            return false;
        }

        // Initialize for full screen capture
        auto result = capturer_->init(capscr::CaptureTargetType::FullScreen);
        if (result != capscr::Result::Ok) {
            std::cerr << "Error: Failed to initialize capturer: " << static_cast<int>(result) << std::endl;
            return false;
        }

        if (!capturer_->isGpuCaptureAvailable()) {
            std::cerr << "Error: GPU capture not available" << std::endl;
            return false;
        }

#ifdef _WIN32
        // Create encoder device (separate from capture device)
        D3D_FEATURE_LEVEL featureLevel;
        HRESULT hr = D3D11CreateDevice(
            nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr,
            0, nullptr, 0, D3D11_SDK_VERSION,
            &encoderDevice_, &featureLevel, &encoderContext_
        );

        if (FAILED(hr)) {
            std::cerr << "Error: Failed to create encoder device: 0x" << std::hex << hr << std::dec << std::endl;
            return false;
        }
#endif

        std::cout << "✓ Initialization successful" << std::endl;
        return true;
    }

    bool captureForEncoder(capscr::ZeroCopyFrame& frame) {
        capscr::ZeroCopyOptions opts;
        opts.prefer_nv12 = true;
        opts.prefer_shared_handle = true;  // Essential for encoder integration

        auto result = capturer_->captureZeroCopy(frame, opts);
        if (result != capscr::Result::Ok) {
            std::cerr << "Error: Capture failed: " << static_cast<int>(result) << std::endl;
            return false;
        }

        if (!frame.is_nv12()) {
            std::cerr << "Error: Captured frame is not NV12 format" << std::endl;
            return false;
        }

        if (frame.mode != capscr::ZeroCopyFrame::Mode::SharedHandle) {
            std::cerr << "Error: Shared handles not available" << std::endl;
            return false;
        }

        return true;
    }

#ifdef _WIN32
    bool processWithEncoder(const capscr::ZeroCopyFrame& frame) {
        std::cout << "Processing frame with encoder..." << std::endl;

        // Open shared textures in encoder device
        ComPtr<ID3D11Texture2D> yTexture, uvTexture;
        
        HRESULT hr = encoderDevice_->OpenSharedResource(
            frame.shared_handle, IID_PPV_ARGS(&yTexture)
        );
        if (FAILED(hr)) {
            std::cerr << "Error: Failed to open Y shared texture: 0x" << std::hex << hr << std::dec << std::endl;
            return false;
        }

        hr = encoderDevice_->OpenSharedResource(
            frame.shared_handle_uv, IID_PPV_ARGS(&uvTexture)
        );
        if (FAILED(hr)) {
            std::cerr << "Error: Failed to open UV shared texture: 0x" << std::hex << hr << std::dec << std::endl;
            return false;
        }

        // Verify texture properties
        D3D11_TEXTURE2D_DESC yDesc, uvDesc;
        yTexture->GetDesc(&yDesc);
        uvTexture->GetDesc(&uvDesc);

        std::cout << "Encoder texture verification:" << std::endl;
        std::cout << "  Y plane: " << yDesc.Width << "x" << yDesc.Height 
                  << " (Format: " << yDesc.Format << ")" << std::endl;
        std::cout << "  UV plane: " << uvDesc.Width << "x" << uvDesc.Height 
                  << " (Format: " << uvDesc.Format << ")" << std::endl;

        // Basic validation
        bool validNV12 = (yDesc.Format == DXGI_FORMAT_R8_UNORM) && 
                        (uvDesc.Format == DXGI_FORMAT_R8G8_UNORM) &&
                        (uvDesc.Width == yDesc.Width / 2) &&
                        (uvDesc.Height == yDesc.Height / 2);

        if (!validNV12) {
            std::cerr << "Error: Invalid NV12 texture properties" << std::endl;
            return false;
        }

        // Simulate encoder operations
        std::cout << "✓ Encoder processing successful" << std::endl;
        return true;
    }
#endif

    void runEncodingLoop() {
        std::cout << "\n--- Running Encoding Loop ---" << std::endl;

        const int frameCount = 5;
        int successCount = 0;

        for (int i = 0; i < frameCount; ++i) {
            std::cout << "\nFrame " << (i + 1) << ":" << std::endl;

            capscr::ZeroCopyFrame frame;
            if (!captureForEncoder(frame)) {
                std::cerr << "  Capture failed" << std::endl;
                continue;
            }

            std::cout << "  ✓ Captured: " << frame.width << "x" << frame.height << std::endl;

#ifdef _WIN32
            if (!processWithEncoder(frame)) {
                std::cerr << "  Encoder processing failed" << std::endl;
                continue;
            }
#endif

            successCount++;
            std::cout << "  ✓ Frame processed successfully" << std::endl;
        }

        std::cout << "\nEncoding loop completed:" << std::endl;
        std::cout << "  Success rate: " << successCount << "/" << frameCount 
                  << " (" << (successCount * 100.0f / frameCount) << "%)" << std::endl;
    }

    void demonstrateSharedHandleLifetime() {
        std::cout << "\n--- Shared Handle Lifetime Test ---" << std::endl;

        std::vector<HANDLE> capturedHandles;
        
        // Capture multiple frames and collect handles
        for (int i = 0; i < 3; ++i) {
            capscr::ZeroCopyFrame frame;
            if (!captureForEncoder(frame)) {
                continue;
            }

#ifdef _WIN32
            std::cout << "Frame " << (i + 1) << " handles:" << std::endl;
            std::cout << "  Y: 0x" << std::hex << reinterpret_cast<uintptr_t>(frame.shared_handle) << std::dec << std::endl;
            std::cout << "  UV: 0x" << std::hex << reinterpret_cast<uintptr_t>(frame.shared_handle_uv) << std::dec << std::endl;
#endif
        }

        std::cout << "✓ Handle lifetime test completed" << std::endl;
    }

    void runDemo() {
        if (!initialize()) {
            return;
        }

        runEncodingLoop();
        demonstrateSharedHandleLifetime();

        std::cout << "\n=== Encoder Integration Demo Completed ===" << std::endl;
    }
};

int main() {
    std::cout << "Encoder Integration Example" << std::endl;
    std::cout << "===========================" << std::endl;

    try {
        EncoderIntegrationExample example;
        example.runDemo();
    }
    catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }

    return 0;
}
