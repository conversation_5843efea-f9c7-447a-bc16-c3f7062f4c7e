#include <gtest/gtest.h>
#include "capscr/capture.hpp"
#include "capscr/platform/windows/capturer_dxgi.hpp"
#include "capture_test_environment.hpp"
#include <memory>
#ifdef _WIN32
#include <dxgi.h>
#include <d3d11.h>
#include <windows.h>
#include <wrl/client.h>
using Microsoft::WRL::ComPtr;

// Helper macro for HRESULT testing
#define EXPECT_SUCCEEDED(hr) EXPECT_TRUE(SUCCEEDED(hr))
#endif

using namespace capscr;

class DXGICapturerNV12ZeroCopyTest : public ::testing::Test {
protected:
    void SetUp() override {
#ifdef _WIN32
        capturer = createDXGICapturer();
        if (capturer) {
            auto r = capturer->init(CaptureTargetType::FullScreen, "", nullptr, "");
            initialized = (r == Result::Ok);
        }
#endif
    }

    void TearDown() override {
        capturer.reset();
    }

    std::unique_ptr<ICapturer> capturer;
    bool initialized = false;
};

#ifdef _WIN32

// 测试 DXGICapturer::captureZeroCopy 的基本NV12支持
TEST_F(DXGICapturerNV12ZeroCopyTest, CaptureZeroCopyNV12Basic) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    ZeroCopyFrame frame;
    auto result = capturer->captureZeroCopy(frame, opts);
    
    if (result != Result::Ok) {
        GTEST_SKIP() << "captureZeroCopy with NV12 not supported: " << static_cast<int>(result);
    }

    // 验证基本属性
    EXPECT_GT(frame.width, 0);
    EXPECT_GT(frame.height, 0);
    EXPECT_EQ(frame.mode, ZeroCopyFrame::Mode::LocalBorrow);
    
    // 验证NV12格式
    EXPECT_EQ(frame.format, PixelFormat::NV12);
    EXPECT_TRUE(frame.is_nv12());
    
    // 验证双纹理
    EXPECT_NE(frame.d3d11_texture, nullptr);      // Y平面
    EXPECT_NE(frame.d3d11_texture_uv, nullptr);   // UV平面
    EXPECT_NE(frame.d3d11_device, nullptr);
    EXPECT_NE(frame.d3d11_context, nullptr);
    
    // 验证Y和UV纹理是不同的对象
    EXPECT_NE(frame.d3d11_texture, frame.d3d11_texture_uv);
    
    std::cout << "NV12 ZeroCopy success: " << frame.width << "x" << frame.height << std::endl;
    std::cout << "Y texture: " << frame.d3d11_texture << std::endl;
    std::cout << "UV texture: " << frame.d3d11_texture_uv << std::endl;
}

// 测试NV12纹理的格式验证
TEST_F(DXGICapturerNV12ZeroCopyTest, CaptureZeroCopyNV12TextureFormat) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    ZeroCopyFrame frame;
    auto result = capturer->captureZeroCopy(frame, opts);
    
    if (result != Result::Ok) {
        GTEST_SKIP() << "captureZeroCopy with NV12 not supported: " << static_cast<int>(result);
    }

    // 验证纹理格式
    if (frame.d3d11_texture && frame.d3d11_texture_uv) {
        D3D11_TEXTURE2D_DESC yDesc, uvDesc;
        frame.d3d11_texture->GetDesc(&yDesc);
        frame.d3d11_texture_uv->GetDesc(&uvDesc);
        
        // Y平面应该是单通道格式 (R8_UNORM)
        EXPECT_EQ(yDesc.Format, DXGI_FORMAT_R8_UNORM);
        EXPECT_EQ(yDesc.Width, static_cast<UINT>(frame.width));
        EXPECT_EQ(yDesc.Height, static_cast<UINT>(frame.height));
        
        // UV平面应该是双通道格式 (R8G8_UNORM) 且尺寸是Y平面的一半
        EXPECT_EQ(uvDesc.Format, DXGI_FORMAT_R8G8_UNORM);
        EXPECT_EQ(uvDesc.Width, (yDesc.Width + 1) / 2);  // 向上取整
        EXPECT_EQ(uvDesc.Height, (yDesc.Height + 1) / 2);
        
        std::cout << "Y plane format: " << yDesc.Format << " (" << yDesc.Width << "x" << yDesc.Height << ")" << std::endl;
        std::cout << "UV plane format: " << uvDesc.Format << " (" << uvDesc.Width << "x" << uvDesc.Height << ")" << std::endl;
    }
}

// 测试fallback行为：NV12失败时应该回退到BGRA
TEST_F(DXGICapturerNV12ZeroCopyTest, CaptureZeroCopyNV12Fallback) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    // 先尝试常规捕获确保基本功能正常
    ZeroCopyOptions opts_normal;
    opts_normal.prefer_nv12 = false;
    
    ZeroCopyFrame frame_normal;
    auto result_normal = capturer->captureZeroCopy(frame_normal, opts_normal);
    
    if (result_normal != Result::Ok) {
        GTEST_SKIP() << "Basic captureZeroCopy not working: " << static_cast<int>(result_normal);
    }
    
    // 验证常规捕获返回BGRA格式
    EXPECT_EQ(frame_normal.format, PixelFormat::BGRA32);
    EXPECT_FALSE(frame_normal.is_nv12());
    EXPECT_NE(frame_normal.d3d11_texture, nullptr);
    EXPECT_EQ(frame_normal.d3d11_texture_uv, nullptr);  // 不应该有UV纹理
    
    std::cout << "Normal ZeroCopy format: " << static_cast<int>(frame_normal.format) << std::endl;
}

// 测试资源释放
TEST_F(DXGICapturerNV12ZeroCopyTest, CaptureZeroCopyNV12ResourceRelease) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    ZeroCopyFrame frame;
    auto result = capturer->captureZeroCopy(frame, opts);
    
    if (result != Result::Ok) {
        GTEST_SKIP() << "captureZeroCopy with NV12 not supported: " << static_cast<int>(result);
    }

    // 验证资源存在
    EXPECT_NE(frame.d3d11_texture, nullptr);
    EXPECT_NE(frame.d3d11_texture_uv, nullptr);
    EXPECT_NE(frame.release_callback, nullptr);
    
    // 手动释放资源
    frame.release();
    
    // 验证资源已被清理
    EXPECT_EQ(frame.d3d11_texture, nullptr);
    EXPECT_EQ(frame.d3d11_texture_uv, nullptr);
    EXPECT_EQ(frame.d3d11_device, nullptr);
    EXPECT_EQ(frame.d3d11_context, nullptr);
    EXPECT_EQ(frame.mode, ZeroCopyFrame::Mode::Unsupported);
    
    std::cout << "NV12 ZeroCopy resource release verified" << std::endl;
}

// 多次捕获测试
TEST_F(DXGICapturerNV12ZeroCopyTest, CaptureZeroCopyNV12Multiple) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    int successful_captures = 0;
    const int max_attempts = 5;
    
    for (int i = 0; i < max_attempts; ++i) {
        ZeroCopyFrame frame;
        auto result = capturer->captureZeroCopy(frame, opts);
        
        if (result == Result::Ok) {
            successful_captures++;
            EXPECT_TRUE(frame.is_nv12());
            EXPECT_NE(frame.d3d11_texture, nullptr);
            EXPECT_NE(frame.d3d11_texture_uv, nullptr);
            
            // 自动释放 (析构时)
        } else if (result == Result::Error) {
            // 可能没有新帧，这是正常的
            continue;
        } else {
            GTEST_SKIP() << "NV12 captureZeroCopy failed on attempt " << i+1 
                        << ": " << static_cast<int>(result);
        }
    }
    
    EXPECT_GT(successful_captures, 0) << "Should have at least one successful NV12 capture";
    std::cout << "Successful captures: " << successful_captures << "/" << max_attempts << std::endl;
}

// 测试NV12纹理的共享句柄访问（模拟编码器场景）
TEST_F(DXGICapturerNV12ZeroCopyTest, CaptureZeroCopyNV12SharedHandleAccess) {
    if (!capturer) GTEST_SKIP() << "DXGI capturer not available";
    if (!initialized) GTEST_SKIP() << "DXGI capturer failed to init";
    if (!capturer->isGpuCaptureAvailable()) GTEST_SKIP() << "GPU capture not available";

    ZeroCopyOptions opts;
    opts.prefer_nv12 = true;
    
    ZeroCopyFrame frame;
    auto result = capturer->captureZeroCopy(frame, opts);
    
    if (result != Result::Ok) {
        GTEST_SKIP() << "captureZeroCopy with NV12 not supported: " << static_cast<int>(result);
    }

    // 验证我们有Y和UV纹理
    ASSERT_NE(frame.d3d11_texture, nullptr);
    ASSERT_NE(frame.d3d11_texture_uv, nullptr);
    ASSERT_NE(frame.d3d11_device, nullptr);
    
    // 获取纹理的共享句柄
    ComPtr<IDXGIResource> yResource, uvResource;
    HANDLE ySharedHandle = nullptr, uvSharedHandle = nullptr;
    
    // 获取Y平面的共享句柄
    HRESULT hr = frame.d3d11_texture->QueryInterface(IID_PPV_ARGS(&yResource));
    EXPECT_SUCCEEDED(hr) << "Failed to query Y texture IDXGIResource interface";
    
    if (SUCCEEDED(hr)) {
        hr = yResource->GetSharedHandle(&ySharedHandle);
        EXPECT_SUCCEEDED(hr) << "Failed to get Y texture shared handle";
    }
    
    // 获取UV平面的共享句柄
    hr = frame.d3d11_texture_uv->QueryInterface(IID_PPV_ARGS(&uvResource));
    EXPECT_SUCCEEDED(hr) << "Failed to query UV texture IDXGIResource interface";
    
    if (SUCCEEDED(hr)) {
        hr = uvResource->GetSharedHandle(&uvSharedHandle);
        EXPECT_SUCCEEDED(hr) << "Failed to get UV texture shared handle";
    }
    
    // 验证共享句柄有效
    EXPECT_NE(ySharedHandle, nullptr) << "Y plane shared handle should not be null";
    EXPECT_NE(uvSharedHandle, nullptr) << "UV plane shared handle should not be null";
    EXPECT_NE(ySharedHandle, uvSharedHandle) << "Y and UV shared handles should be different";
    
    // 模拟编码器：创建新的设备并通过共享句柄访问纹理
    ComPtr<ID3D11Device> encoderDevice;
    ComPtr<ID3D11DeviceContext> encoderContext;
    
    D3D_FEATURE_LEVEL featureLevels[] = { D3D_FEATURE_LEVEL_11_1, D3D_FEATURE_LEVEL_11_0 };
    hr = D3D11CreateDevice(
        nullptr,                    // adapter
        D3D_DRIVER_TYPE_HARDWARE,   // driver type
        nullptr,                    // software
        0,                          // flags
        featureLevels,              // feature levels
        ARRAYSIZE(featureLevels),   // num feature levels
        D3D11_SDK_VERSION,          // sdk version
        &encoderDevice,             // device
        nullptr,                    // feature level
        &encoderContext             // context
    );
    
    if (FAILED(hr)) {
        GTEST_SKIP() << "Failed to create encoder simulation device: " << std::hex << hr;
    }
    
    // 通过共享句柄在编码器设备中打开纹理
    ComPtr<ID3D11Texture2D> encoderYTexture, encoderUVTexture;
    
    if (ySharedHandle) {
        hr = encoderDevice->OpenSharedResource(ySharedHandle, IID_PPV_ARGS(&encoderYTexture));
        EXPECT_SUCCEEDED(hr) << "Failed to open shared Y texture in encoder device";
    }
    
    if (uvSharedHandle) {
        hr = encoderDevice->OpenSharedResource(uvSharedHandle, IID_PPV_ARGS(&encoderUVTexture));
        EXPECT_SUCCEEDED(hr) << "Failed to open shared UV texture in encoder device";
    }
    
    // 验证编码器可以访问纹理属性
    if (encoderYTexture && encoderUVTexture) {
        D3D11_TEXTURE2D_DESC yDesc, uvDesc;
        encoderYTexture->GetDesc(&yDesc);
        encoderUVTexture->GetDesc(&uvDesc);
        
        // 验证纹理格式和尺寸
        EXPECT_EQ(yDesc.Format, DXGI_FORMAT_R8_UNORM);
        EXPECT_EQ(uvDesc.Format, DXGI_FORMAT_R8G8_UNORM);
        EXPECT_EQ(yDesc.Width, static_cast<UINT>(frame.width));
        EXPECT_EQ(yDesc.Height, static_cast<UINT>(frame.height));
        EXPECT_EQ(uvDesc.Width, (yDesc.Width + 1) / 2);
        EXPECT_EQ(uvDesc.Height, (yDesc.Height + 1) / 2);
        
        std::cout << "Encoder simulation: Successfully accessed NV12 textures via shared handles" << std::endl;
        std::cout << "Y shared handle: " << ySharedHandle << " -> " << encoderYTexture.Get() << std::endl;
        std::cout << "UV shared handle: " << uvSharedHandle << " -> " << encoderUVTexture.Get() << std::endl;
        
        // 模拟编码器读取操作：创建staging纹理并复制部分数据
        D3D11_TEXTURE2D_DESC stagingDesc = yDesc;
        stagingDesc.Usage = D3D11_USAGE_STAGING;
        stagingDesc.BindFlags = 0;
        stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        stagingDesc.Width = min(64, yDesc.Width);   // 只复制一小块作为验证
        stagingDesc.Height = min(64, yDesc.Height);
        
        ComPtr<ID3D11Texture2D> stagingTexture;
        hr = encoderDevice->CreateTexture2D(&stagingDesc, nullptr, &stagingTexture);
        EXPECT_SUCCEEDED(hr) << "Failed to create staging texture for encoder simulation";
        
        if (SUCCEEDED(hr) && stagingTexture) {
            // 复制Y平面的一小部分到staging纹理
            D3D11_BOX sourceBox = {};
            sourceBox.right = stagingDesc.Width;
            sourceBox.bottom = stagingDesc.Height;
            sourceBox.back = 1;
            
            encoderContext->CopySubresourceRegion(
                stagingTexture.Get(), 0,    // dest
                0, 0, 0,                    // dest coords
                encoderYTexture.Get(), 0,   // source
                &sourceBox                  // source region
            );
            
            // 尝试映射纹理以验证数据可访问性
            D3D11_MAPPED_SUBRESOURCE mapped = {};
            hr = encoderContext->Map(stagingTexture.Get(), 0, D3D11_MAP_READ, 0, &mapped);
            if (SUCCEEDED(hr)) {
                EXPECT_NE(mapped.pData, nullptr) << "Mapped data should not be null";
                EXPECT_GT(mapped.RowPitch, 0U) << "Row pitch should be positive";
                
                // 检查像素数据不全为零（表明有真实内容）
                bool hasNonZeroPixels = false;
                const uint8_t* pixels = static_cast<const uint8_t*>(mapped.pData);
                for (UINT row = 0; row < min(16U, stagingDesc.Height); ++row) {
                    for (UINT col = 0; col < min(16U, stagingDesc.Width); ++col) {
                        if (pixels[row * mapped.RowPitch + col] != 0) {
                            hasNonZeroPixels = true;
                            break;
                        }
                    }
                    if (hasNonZeroPixels) break;
                }
                
                encoderContext->Unmap(stagingTexture.Get(), 0);
                std::cout << "Encoder data validation: " << (hasNonZeroPixels ? "Found non-zero pixels" : "All sampled pixels were zero") << std::endl;
            }
        }
    }
}

#endif // _WIN32
