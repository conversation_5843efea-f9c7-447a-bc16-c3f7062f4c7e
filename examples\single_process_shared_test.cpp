#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <iostream>
#include <vector>
#include <cstdint>
#include <d3d11sdklayers.h>

using Microsoft::WRL::ComPtr;

static void printHR(const char* msg, HRESULT hr) {
    std::cerr << msg << " hr=0x" << std::hex << hr << std::dec << std::endl;
}

int main() {
    std::cout << "Single-process shared handle test\n";

    // Create device A (producer) - try debug layer if available
    ComPtr<ID3D11Device> devA;
    ComPtr<ID3D11DeviceContext> ctxA;
    D3D_FEATURE_LEVEL flA;
    UINT createFlags = 0;
    bool haveDebug = false;
    HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &devA, &flA, &ctxA);
    if (SUCCEEDED(hr)) {
        haveDebug = true;
        std::cout << "Created device A with D3D11_CREATE_DEVICE_DEBUG" << std::endl;
    } else {
        // fallback to non-debug
        hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &devA, &flA, &ctxA);
        if (FAILED(hr)) { printHR("CreateDevice A failed", hr); return 1; }
    }

    // We'll try multiple combinations of MiscFlags and CreateSharedHandle access flags.
    struct MiscCase { UINT misc; const char* name; bool forceDefaultUsage; } miscCases[] = {
        { D3D11_RESOURCE_MISC_SHARED_NTHANDLE, "SHARED_NTHANDLE", true },
        { D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX, "SHARED_KEYEDMUTEX", false },
        { D3D11_RESOURCE_MISC_SHARED_NTHANDLE | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX, "NTHANDLE|KEYEDMUTEX", true },
        { D3D11_RESOURCE_MISC_SHARED, "SHARED", true },
        { D3D11_RESOURCE_MISC_SHARED | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX, "SHARED|KEYEDMUTEX", true },
        { 0, "NONE", false }
    };

    struct AccessCase { DWORD access; const char* name; } accessCases[] = {
        { 0, "ACCESS_0" },
        { GENERIC_READ, "GENERIC_READ" },
        { GENERIC_ALL, "GENERIC_ALL" }
    };

    for (auto m : miscCases) {
        for (auto a : accessCases) {
            std::cout << "\n--- Test misc=" << m.name << " access=" << a.name << " ---\n";

            // Additional matrix: formats / usage / bindflags
            struct FormatCase { DXGI_FORMAT fmt; const char* name; } formats[] = {
                { DXGI_FORMAT_R8G8B8A8_UNORM, "R8G8B8A8_UNORM" },
                { DXGI_FORMAT_B8G8R8A8_UNORM, "B8G8R8A8_UNORM" },
            };
            struct UsageCase { D3D11_USAGE usage; const char* name; } usages[] = {
                { D3D11_USAGE_DEFAULT, "USAGE_DEFAULT" },
                { D3D11_USAGE_STAGING, "USAGE_STAGING" }
            };
            struct BindCase { UINT bind; const char* name; } binds[] = {
                { D3D11_BIND_SHADER_RESOURCE, "BIND_SHADER_RESOURCE" },
                { D3D11_BIND_RENDER_TARGET, "BIND_RENDER_TARGET" },
                { 0, "BIND_NONE" }
            };

            for (auto f : formats) for (auto u : usages) for (auto b : binds) {
                // If misc case requires DEFAULT usage, skip other usage entries
                if (m.forceDefaultUsage && u.usage != D3D11_USAGE_DEFAULT) continue;
                std::cout << "--> format=" << f.name << " usage=" << u.name << " bind=" << b.name << "\n";

                // Create a texture on A with this misc flags and additional properties
                D3D11_TEXTURE2D_DESC desc = {};
                desc.Width = 128; desc.Height = 64; desc.MipLevels = 1; desc.ArraySize = 1;
                desc.Format = f.fmt; desc.SampleDesc.Count = 1;
                desc.Usage = u.usage; desc.BindFlags = b.bind;
                desc.CPUAccessFlags = (u.usage == D3D11_USAGE_STAGING) ? D3D11_CPU_ACCESS_READ : 0;
                desc.MiscFlags = m.misc;

            std::vector<uint8_t> bmp(desc.Width * desc.Height * 4);
            for (int y = 0; y < (int)desc.Height; ++y) for (int x = 0; x < (int)desc.Width; ++x) {
                int i = (y*desc.Width + x)*4; bmp[i+0] = (uint8_t)x; bmp[i+1] = (uint8_t)y; bmp[i+2] = 0; bmp[i+3] = 255;
            }
            D3D11_SUBRESOURCE_DATA sd = {}; sd.pSysMem = bmp.data(); sd.SysMemPitch = desc.Width * 4;
            ComPtr<ID3D11Texture2D> texA;
            hr = devA->CreateTexture2D(&desc, &sd, &texA);
            if (FAILED(hr) || !texA) { printHR("CreateTexture2D A failed", hr); continue; }

            ComPtr<IDXGIResource1> res1;
            if (FAILED(texA.As(&res1))) { printHR("QI IDXGIResource1 failed", E_FAIL); continue; }

            HANDLE h = nullptr;
            SECURITY_ATTRIBUTES sa = {}; sa.nLength = sizeof(sa); sa.bInheritHandle = FALSE; sa.lpSecurityDescriptor = nullptr;
            hr = res1->CreateSharedHandle(&sa, a.access, nullptr, &h);
            std::cout << "CreateSharedHandle returned hr=0x" << std::hex << hr << std::dec;
            if (SUCCEEDED(hr) && h) std::cout << " handle=0x" << std::hex << reinterpret_cast<uintptr_t>(h) << std::dec;
            std::cout << std::endl;

                // Create device B on same adapter as A
                ComPtr<IDXGIDevice> dxDevA; ComPtr<IDXGIAdapter> adapterA;
                if (SUCCEEDED(devA.As(&dxDevA))) { if (FAILED(dxDevA->GetAdapter(&adapterA))) adapterA.Reset(); }
                ComPtr<ID3D11Device> devB; ComPtr<ID3D11DeviceContext> ctxB; D3D_FEATURE_LEVEL flB;
                if (adapterA) {
                    if (haveDebug) hr = D3D11CreateDevice(adapterA.Get(), D3D_DRIVER_TYPE_UNKNOWN, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &devB, &flB, &ctxB);
                    else hr = D3D11CreateDevice(adapterA.Get(), D3D_DRIVER_TYPE_UNKNOWN, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &devB, &flB, &ctxB);
                } else {
                    if (haveDebug) hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, D3D11_CREATE_DEVICE_DEBUG, nullptr, 0, D3D11_SDK_VERSION, &devB, &flB, &ctxB);
                    else hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0, nullptr, 0, D3D11_SDK_VERSION, &devB, &flB, &ctxB);
                }
            if (FAILED(hr)) { printHR("CreateDevice B failed", hr); if (h) CloseHandle(h); continue; }

            // Try open shared resource (regular API)
            ComPtr<ID3D11Texture2D> texB;
            if (h) hr = devB->OpenSharedResource(h, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(texB.GetAddressOf()));
            else hr = E_INVALIDARG;
            std::cout << "OpenSharedResource returned hr=0x" << std::hex << hr << std::dec << std::endl;

            // If that failed, try the D3D11.1 OpenSharedResource1 fallback (if available)
            if ((FAILED(hr) || !texB) && h) {
                ComPtr<ID3D11Device1> devB1;
                if (SUCCEEDED(devB.As(&devB1)) && devB1) {
                    HRESULT hr1 = devB1->OpenSharedResource1(h, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(texB.GetAddressOf()));
                    std::cout << "OpenSharedResource1 returned hr=0x" << std::hex << hr1 << std::dec << std::endl;
                    if (SUCCEEDED(hr1) && texB) hr = hr1;
                }
            }

                if (texB) {
                    D3D11_TEXTURE2D_DESC descB; texB->GetDesc(&descB);
                    std::cout << "Opened resource: " << descB.Width << "x" << descB.Height << " fmt=" << descB.Format << std::endl;
                }

                // If debug available, dump ID3D11InfoQueue messages from both devices
                if (haveDebug) {
                    ComPtr<ID3D11InfoQueue> iq;
                    if (SUCCEEDED(devB.As(&iq))) {
                        UINT64 n = iq->GetNumStoredMessagesAllowedByRetrievalFilter();
                        if (n > 0) std::cout << "ID3D11InfoQueue messages (devB): " << n << std::endl;
                        for (UINT64 i = 0; i < n; ++i) {
                            SIZE_T msgLen = 0;
                            iq->GetMessage(i, nullptr, &msgLen);
                            D3D11_MESSAGE* msg = (D3D11_MESSAGE*)malloc(msgLen);
                            if (msg) {
                                iq->GetMessage(i, msg, &msgLen);
                                std::cout << "  D3D11 msg: " << msg->pDescription << std::endl;
                                free(msg);
                            }
                        }
                    }
                    if (SUCCEEDED(devA.As(&iq))) {
                        UINT64 n2 = iq->GetNumStoredMessagesAllowedByRetrievalFilter();
                        if (n2 > 0) std::cout << "ID3D11InfoQueue messages (devA): " << n2 << std::endl;
                        for (UINT64 i = 0; i < n2; ++i) {
                            SIZE_T msgLen = 0;
                            iq->GetMessage(i, nullptr, &msgLen);
                            D3D11_MESSAGE* msg = (D3D11_MESSAGE*)malloc(msgLen);
                            if (msg) {
                                iq->GetMessage(i, msg, &msgLen);
                                std::cout << "  D3D11 msg: " << msg->pDescription << std::endl;
                                free(msg);
                            }
                        }
                    }
                }

                if (h) CloseHandle(h);
            }
        }
    }

    std::cout << "All combinations tested" << std::endl;
    return 0;
}
#else
int main() { return 0; }
#endif
