# capscr demo

This demo shows a minimal ImGui + GLFW + OpenGL front-end that calls into the capscr library to perform screen capture and display information.

## Notes

- This repository already contains the capscr library sources and headers under `include/` and `src/`.
- For a full UI build, add ImGui, GLFW and an OpenGL loader (e.g., glad) and update `CMakeLists.txt` to link them.

## Quick test (without ImGui)

From repo root:

```sh
mkdir -p build_demo && cd build_demo
cmake -S .. -B . -DCMAKE_BUILD_TYPE=Release
cmake --build . --target capscr_demo --config Release
```

The minimal `main.cpp` will call `capscr::createBestCapturer()` and attempt a single capture, printing the result.

## Next steps

- Integrate ImGui and implement the UI: parameter panel (capture backend, display list, region/window selection) and preview area.
- Implement frame-to-texture upload and draw in ImGui window.
- Display statistics: FPS, resolution, CPU/GPU usage.

## Build & Run (OpenGL + GLFW + ImGui)

Requirements:

- CMake >= 3.15
- A C++17 toolchain (MSVC/clang/gcc)
- GLFW (dev headers and lib)
- glad (or another GL loader)
- ImGui (core + backends: imgui_impl_glfw.cpp + imgui_impl_opengl3.cpp)

Options:

- To use the OpenGL path (default): ensure `glfw`, `glad` and ImGui are available to the build.

Typical steps:

- Add ImGui and glad sources to the demo target. Example (in `demo/CMakeLists.txt`):

```cmake
# Add ImGui sources
target_sources(capscr_demo PRIVATE
  ${PROJECT_SOURCE_DIR}/third_party/imgui/imgui.cpp
  ${PROJECT_SOURCE_DIR}/third_party/imgui/imgui_draw.cpp
  ${PROJECT_SOURCE_DIR}/third_party/imgui/imgui_widgets.cpp
  ${PROJECT_SOURCE_DIR}/third_party/imgui/backends/imgui_impl_glfw.cpp
  ${PROJECT_SOURCE_DIR}/third_party/imgui/backends/imgui_impl_opengl3.cpp
  ${PROJECT_SOURCE_DIR}/third_party/glad/src/glad.c)
```

- Configure and build:

```sh
mkdir -p build_demo
cd build_demo
cmake -S .. -B . -DCMAKE_BUILD_TYPE=Release
cmake --build . --target capscr_demo --config Release
```

## Windows D3D11 zero-copy path

On Windows, if you prefer testing the zero-copy GPU path (DXGI/D3D11), you can enable the D3D11 path in CMake:

  cmake -S .. -B . -DUSE_D3D11=ON

Notes:

- The demo attempts to call `capturer->captureGpu()` when `USE_D3D11` is enabled and the backend reports `ZeroCopyGpu` capability.
- Proper D3D11 -> ImGui texture rendering often requires using the ImGui D3D11 backend or platform-specific interop (WGL_NV_DX_interop or shared handles). The demo currently prints receipt of a GPU texture; for real zero-copy display implement platform interop or use the ImGui D3D11 backend.
The demo attempts to call `capturer->captureGpu()` when `USE_D3D11` is enabled and the backend reports `ZeroCopyGpu` capability.

On Windows when built with D3D11, the demo will prefer the GPU zero-copy path when available and create a D3D11 Shader Resource View (SRV) from the returned `ID3D11Texture2D*` so the ImGui DX11 backend can render it directly. If SRV creation fails or `captureGpu()` is not available the demo falls back to CPU capture (GDI or other software paths) and uploads the pixels to a GL texture for display.

Proper D3D11 -> OpenGL zero-copy interop (to display a D3D11 texture in an OpenGL context) requires platform-specific extensions (e.g. `WGL_NV_dx_interop`) or shared-handle mechanisms and is not provided by default in this demo. For most cases on Windows prefer the ImGui DX11 backend to render D3D11 textures directly.

## Pixel formats

- The demo expects the capture frame to be convertible to BGRA32 (packed BGRA) for direct GL upload. If the backend returns another format, the demo calls `capscr::ImageUtils::convertPixelFormat` to convert before uploading.
- For best performance with GPU zero-copy, avoid CPU conversions and use `captureGpu()`.

If you want, I can:

- Add ImGui and glad as git submodules and update `CMakeLists.txt` to automatically build them.
- Implement D3D11 -> OpenGL interop for true zero-copy texture display on Windows (requires platform extensions).
- Document recommended platform build flags: on Windows use `-DUSE_D3D11=ON` to enable the DX11 path; on macOS/Linux leave it OFF to use the OpenGL path.
