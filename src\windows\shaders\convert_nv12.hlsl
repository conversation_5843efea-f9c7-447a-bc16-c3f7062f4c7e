// Convert to NV12 two-pass compute shaders

// Dimensions: dimensions.x = width, dimensions.y = height
cbuffer NV12Params : register(b0)
{
    uint4 dimensions; // x = width, y = height
}

Texture2D<float4> InputTexture : register(t0);
RWTexture2D<float> YPlane : register(u0);     // single-channel R for luma
RWTexture2D<float2> UVPlane : register(u1);   // interleaved U,V at half resolution

#define THREAD_GROUP_SIZE_X 16
#define THREAD_GROUP_SIZE_Y 16

// Luma coefficients (Rec. 601)
static const float3 LUMA_COEFF = float3(0.299, 0.587, 0.114);

[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSNV12_Y_Plane(uint3 id : SV_DispatchThreadID)
{
    uint x = id.x;
    uint y = id.y;
    if (x >= dimensions.x || y >= dimensions.y) return;

    float4 c = InputTexture.Load(int3(x, y, 0));
    float luma = dot(c.rgb, LUMA_COEFF);
    YPlane[uint2(x, y)] = luma;
}

[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSNV12_UV_Plane(uint3 id : SV_DispatchThreadID)
{
    // UV plane has half resolution
    uint ux = id.x;
    uint uy = id.y;
    uint uvWidth = (dimensions.x + 1) / 2;
    uint uvHeight = (dimensions.y + 1) / 2;
    if (ux >= uvWidth || uy >= uvHeight) return;

    // Compute sample coordinates (2x2 block -> average)
    uint sx = ux * 2;
    uint sy = uy * 2;

    float3 sumUV = float3(0.0, 0.0, 0.0);
    int samples = 0;
    for (int oy = 0; oy < 2; ++oy) {
        for (int ox = 0; ox < 2; ++ox) {
            uint px = sx + ox;
            uint py = sy + oy;
            if (px < dimensions.x && py < dimensions.y) {
                float4 c = InputTexture.Load(int3(px, py, 0));
                // Convert to YUV (BT.601)
                float yv = dot(c.rgb, LUMA_COEFF);
                float u = (c.r - yv) * 0.493;
                float v = (c.b - yv) * 0.877;
                sumUV += float3(u, v, 0.0);
                samples++;
            }
        }
    }

    if (samples == 0) {
        UVPlane[uint2(ux, uy)] = float2(0.5, 0.5);
    } else {
        float uAvg = sumUV.x / samples + 0.5; // offset to [0,1]
        float vAvg = sumUV.y / samples + 0.5;
        UVPlane[uint2(ux, uy)] = float2(uAvg, vAvg);
    }
}
