#include <iostream>
#include <windows.h>
#include <vector>
#include <string>

struct WindowInfo {
    HWND hwnd;
    std::string title;
    RECT rect;
};

std::vector<WindowInfo> windows;

BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    char title[512] = {0};
    if (IsWindowVisible(hwnd) && GetWindowTextA(hwnd, title, sizeof(title)) > 0) {
        WindowInfo info;
        info.hwnd = hwnd;
        info.title = title;
        GetWindowRect(hwnd, &info.rect);
        windows.push_back(info);
    }
    return TRUE;
}

int main() {
    windows.clear();
    EnumWindows(EnumWindowsProc, 0);
    
    std::cout << "All visible windows:\n";
    for (const auto& win : windows) {
        std::cout << "HWND: " << win.hwnd 
                  << " Title: '" << win.title << "'"
                  << " Size: " << (win.rect.right - win.rect.left) << "x" << (win.rect.bottom - win.rect.top)
                  << " Pos: (" << win.rect.left << "," << win.rect.top << ")"
                  << "\n";
    }
    
    std::cout << "\nWindows containing 'Char':\n";
    for (const auto& win : windows) {
        if (win.title.find("Char") != std::string::npos) {
            std::cout << "FOUND: HWND: " << win.hwnd 
                      << " Title: '" << win.title << "'"
                      << " Size: " << (win.rect.right - win.rect.left) << "x" << (win.rect.bottom - win.rect.top)
                      << "\n";
        }
    }
    
    return 0;
}
