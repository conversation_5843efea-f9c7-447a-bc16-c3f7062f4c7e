#include <windows.h>
#include <iostream>
#include <vector>
#include <fstream>
#ifdef HAVE_LODEPNG
#include <lodepng.h>
#endif

bool savePNG(const std::string& filename, const std::vector<uint8_t>& bgraData, int width, int height) {
#ifdef HAVE_LODEPNG
    // Convert BGRA -> RGBA
    std::vector<uint8_t> rgba;
    rgba.resize(width * height * 4);
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int idx = (y * width + x) * 4;
            rgba[idx + 0] = bgraData[idx + 2]; // R <- B
            rgba[idx + 1] = bgraData[idx + 1]; // G <- G  
            rgba[idx + 2] = bgraData[idx + 0]; // B <- R
            rgba[idx + 3] = bgraData[idx + 3]; // A <- A
        }
    }
    unsigned error = lodepng::encode(filename, rgba, width, height);
    return error == 0;
#else
    return false;
#endif
}

int main(int argc, char** argv) {
    if (argc != 2) {
        std::cout << "Usage: test_printwindow_debug <window_title>" << std::endl;
        return 1;
    }
    
    std::string title = argv[1];
    HWND hwnd = FindWindowA(nullptr, title.c_str());
    
    if (!hwnd) {
        std::cout << "Window '" << title << "' not found" << std::endl;
        return 1;
    }
    
    RECT rect;
    if (!GetWindowRect(hwnd, &rect)) {
        std::cout << "Failed to get window rect" << std::endl;
        return 1;
    }
    
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    
    std::cout << "Window: " << hwnd << " Size: " << width << "x" << height << std::endl;
    
    HDC hScreenDC = GetDC(nullptr);
    HDC hMemDC = CreateCompatibleDC(hScreenDC);
    HBITMAP hBitmap = CreateCompatibleBitmap(hScreenDC, width, height);
    HGDIOBJ oldBitmap = SelectObject(hMemDC, hBitmap);
    
    // Clear the memory DC first
    RECT memRect = {0, 0, width, height};
    FillRect(hMemDC, &memRect, (HBRUSH)GetStockObject(BLACK_BRUSH));
    
    std::cout << "Attempting PrintWindow..." << std::endl;
    BOOL result = PrintWindow(hwnd, hMemDC, PW_RENDERFULLCONTENT);
    
    if (result) {
        std::cout << "PrintWindow succeeded!" << std::endl;
        
        // Get bitmap data
        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = -height; // negative for top-down
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 32;
        bmi.bmiHeader.biCompression = BI_RGB;
        
        std::vector<uint8_t> data(width * height * 4);
        int scanlines = GetDIBits(hMemDC, hBitmap, 0, height, data.data(), &bmi, DIB_RGB_COLORS);
        
        if (scanlines > 0) {
            std::cout << "Got " << scanlines << " scanlines of data" << std::endl;
            
            // Check if we have non-black pixels
            bool hasContent = false;
            int nonBlackCount = 0;
            for (size_t i = 0; i < data.size(); i += 4) {
                if (data[i] != 0 || data[i+1] != 0 || data[i+2] != 0) {
                    hasContent = true;
                    nonBlackCount++;
                    if (nonBlackCount < 10) {
                        printf("Pixel %zu: B=%d G=%d R=%d A=%d\n", i/4, data[i], data[i+1], data[i+2], data[i+3]);
                    }
                }
            }
            
            std::cout << "Non-black pixels: " << nonBlackCount << " out of " << (data.size()/4) << std::endl;
            
            if (hasContent) {
                std::cout << "Image has content!" << std::endl;
                bool saved = savePNG("printwindow_debug.png", data, width, height);
                if (saved) {
                    std::cout << "Saved as printwindow_debug.png" << std::endl;
                } else {
                    std::cout << "Failed to save PNG" << std::endl;
                }
            } else {
                std::cout << "Image is completely black" << std::endl;
            }
        } else {
            std::cout << "Failed to get bitmap data" << std::endl;
        }
    } else {
        DWORD error = GetLastError();
        std::cout << "PrintWindow failed with error: " << error << std::endl;
    }
    
    // Cleanup
    SelectObject(hMemDC, oldBitmap);
    DeleteObject(hBitmap);
    DeleteDC(hMemDC);
    ReleaseDC(nullptr, hScreenDC);
    
    return 0;
}
