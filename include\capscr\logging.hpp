#pragma once

#include <memory>
#include <string>

namespace capscr {

// Log levels matching spdlog levels
enum class LogLevel {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warn = 3,
    Error = 4,
    Critical = 5,
    Off = 6
};

// Logger interface for dependency injection
class ILogger {
public:
    virtual ~ILogger() = default;
    
    virtual void log(LogLevel level, const std::string& message) = 0;
    virtual void set_level(LogLevel level) = 0;
    virtual LogLevel level() const = 0;
    
    // Convenience methods
    void trace(const std::string& msg) { log(LogLevel::Trace, msg); }
    void debug(const std::string& msg) { log(LogLevel::Debug, msg); }
    void info(const std::string& msg) { log(LogLevel::Info, msg); }
    void warn(const std::string& msg) { log(LogLevel::Warn, msg); }
    void error(const std::string& msg) { log(LogLevel::Error, msg); }
    void critical(const std::string& msg) { log(LogLevel::Critical, msg); }
};

// Global logger management
namespace logging {
    // Initialize logging with spdlog (call once at startup)
    void init();
    void init(const std::string& log_file_path);
    
    // Get global logger instance
    std::shared_ptr<ILogger> get_logger();
    
    // Set custom logger (for testing or custom implementations)
    void set_logger(std::shared_ptr<ILogger> logger);
    
    // Set global log level
    void set_level(LogLevel level);
    
    // Shutdown logging (call at exit)
    void shutdown();
}

// Convenience macros for logging
#define CAPSCR_LOG_TRACE(msg) capscr::logging::get_logger()->trace(msg)
#define CAPSCR_LOG_DEBUG(msg) capscr::logging::get_logger()->debug(msg)
#define CAPSCR_LOG_INFO(msg) capscr::logging::get_logger()->info(msg)
#define CAPSCR_LOG_WARN(msg) capscr::logging::get_logger()->warn(msg)
#define CAPSCR_LOG_ERROR(msg) capscr::logging::get_logger()->error(msg)
#define CAPSCR_LOG_CRITICAL(msg) capscr::logging::get_logger()->critical(msg)

// Formatted logging macros
#define CAPSCR_LOG_TRACE_F(fmt, ...) capscr::logging::get_logger()->trace(fmt, __VA_ARGS__)
#define CAPSCR_LOG_DEBUG_F(fmt, ...) capscr::logging::get_logger()->debug(fmt, __VA_ARGS__)
#define CAPSCR_LOG_INFO_F(fmt, ...) capscr::logging::get_logger()->info(fmt, __VA_ARGS__)
#define CAPSCR_LOG_WARN_F(fmt, ...) capscr::logging::get_logger()->warn(fmt, __VA_ARGS__)
#define CAPSCR_LOG_ERROR_F(fmt, ...) capscr::logging::get_logger()->error(fmt, __VA_ARGS__)
#define CAPSCR_LOG_CRITICAL_F(fmt, ...) capscr::logging::get_logger()->critical(fmt, __VA_ARGS__)

} // namespace capscr
