#include <gtest/gtest.h>
#include <memory>
#include <vector>
#include <chrono>
#include <algorithm>
#include "../include/capscr/capture.hpp"
#include "../include/capscr/platform/windows/capturer_dxgi.hpp"

using namespace capscr;

class CaptureProcessingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize DXGI capturer
        dxgiCapturer = std::make_unique<DXGICapturer>();
        
        // Initialize with full screen capture
        Result result = dxgiCapturer->init(CaptureTargetType::FullScreen);
        ASSERT_EQ(result, Result::Ok) << "Failed to initialize DXGI capturer";
    }

    void TearDown() override {
        dxgiCapturer.reset();
    }

    std::unique_ptr<DXGICapturer> dxgiCapturer;
};

// Test basic capture functionality
TEST_F(CaptureProcessingTest, BasicCapture) {
    Frame frame;
    Result result = dxgiCapturer->capture(frame);
    
    EXPECT_EQ(result, Result::Ok);
    EXPECT_GT(frame.width, 0);
    EXPECT_GT(frame.height, 0);
    EXPECT_FALSE(frame.data.empty());
    EXPECT_GT(frame.stride, 0);
}

// Test CPU resize with different quality levels
TEST_F(CaptureProcessingTest, CPUResizeQualityLevels) {
    CaptureParams params;
    params.output_width = 800;
    params.output_height = 600;
    params.use_gpu_processing = false; // Force CPU processing
    
    // Test Fastest (Nearest neighbor)
    params.resize_quality = CaptureParams::ResizeQuality::Fastest;
    Frame fastFrame;
    Result result = dxgiCapturer->capture(fastFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(fastFrame.width, 800);
    EXPECT_EQ(fastFrame.height, 600);
    EXPECT_FALSE(fastFrame.data.empty());
    
    // Test Balanced (Bilinear)
    params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    Frame balancedFrame;
    result = dxgiCapturer->capture(balancedFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(balancedFrame.width, 800);
    EXPECT_EQ(balancedFrame.height, 600);
    EXPECT_FALSE(balancedFrame.data.empty());
    
    // Test Highest (Mitchell-Netravali)
    params.resize_quality = CaptureParams::ResizeQuality::Highest;
    Frame highestFrame;
    result = dxgiCapturer->capture(highestFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(highestFrame.width, 800);
    EXPECT_EQ(highestFrame.height, 600);
    EXPECT_FALSE(highestFrame.data.empty());
}

// Test GPU resize functionality
TEST_F(CaptureProcessingTest, GPUResize) {
    if (!dxgiCapturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available";
    }
    
    CaptureParams params;
    params.output_width = 640;
    params.output_height = 480;
    params.use_gpu_processing = true;
    params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    
    Frame frame;
    Result result = dxgiCapturer->capture(frame, params);
    
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(frame.width, 640);
    EXPECT_EQ(frame.height, 480);
    EXPECT_FALSE(frame.data.empty());
}

// Test GPU vs CPU performance comparison
TEST_F(CaptureProcessingTest, GPUvsCPUPerformance) {
    if (!dxgiCapturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available";
    }
    
    CaptureParams params;
    params.output_width = 960;
    params.output_height = 540;
    params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    
    // Measure CPU performance
    params.use_gpu_processing = false;
    auto cpuStart = std::chrono::high_resolution_clock::now();
    Frame cpuFrame;
    Result cpuResult = dxgiCapturer->capture(cpuFrame, params);
    auto cpuEnd = std::chrono::high_resolution_clock::now();
    auto cpuTime = std::chrono::duration_cast<std::chrono::milliseconds>(cpuEnd - cpuStart);
    
    EXPECT_EQ(cpuResult, Result::Ok);
    EXPECT_EQ(cpuFrame.width, 960);
    EXPECT_EQ(cpuFrame.height, 540);
    
    // Measure GPU performance  
    params.use_gpu_processing = true;
    auto gpuStart = std::chrono::high_resolution_clock::now();
    Frame gpuFrame;
    Result gpuResult = dxgiCapturer->capture(gpuFrame, params);
    auto gpuEnd = std::chrono::high_resolution_clock::now();
    auto gpuTime = std::chrono::duration_cast<std::chrono::milliseconds>(gpuEnd - gpuStart);
    
    EXPECT_EQ(gpuResult, Result::Ok);
    EXPECT_EQ(gpuFrame.width, 960);
    EXPECT_EQ(gpuFrame.height, 540);
    
    // Log performance comparison
    std::cout << "CPU time: " << cpuTime.count() << "ms" << std::endl;
    std::cout << "GPU time: " << gpuTime.count() << "ms" << std::endl;
    if (gpuTime.count() > 0) {
        double speedup = static_cast<double>(cpuTime.count()) / gpuTime.count();
        std::cout << "GPU speedup: " << speedup << "x" << std::endl;
    }
}

// Test pixel format conversion
TEST_F(CaptureProcessingTest, PixelFormatConversion) {
    CaptureParams params;
    params.output_width = 400;
    params.output_height = 300;
    params.use_gpu_processing = false; // Use CPU for reliable testing
    
    // Test different output formats
    std::vector<PixelFormat> formats = {
        PixelFormat::BGRA32,
        PixelFormat::RGBA32,
        PixelFormat::BGR24,
        PixelFormat::RGB24
    };
    
    for (PixelFormat format : formats) {
        params.output_format = format;
        Frame frame;
        Result result = dxgiCapturer->capture(frame, params);
        
        EXPECT_EQ(result, Result::Ok) << "Failed for format: " << static_cast<int>(format);
        EXPECT_EQ(frame.width, 400);
        EXPECT_EQ(frame.height, 300);
        EXPECT_EQ(frame.format, format);
        EXPECT_FALSE(frame.data.empty());
        
        // Verify data size matches expected format
        int expectedBytesPerPixel;
        switch (format) {
            case PixelFormat::BGRA32:
            case PixelFormat::RGBA32:
            case PixelFormat::BGRX32:
            case PixelFormat::RGBX32:
                expectedBytesPerPixel = 4;
                break;
            case PixelFormat::BGR24:
            case PixelFormat::RGB24:
                expectedBytesPerPixel = 3;
                break;
            default:
                expectedBytesPerPixel = 4;
        }
        
        EXPECT_GE(frame.data.size(), frame.width * frame.height * expectedBytesPerPixel);
    }
}

// Test parameter validation
TEST_F(CaptureProcessingTest, ParameterValidation) {
    CaptureParams params;
    Frame frame;
    
    // Test invalid negative dimensions
    params.output_width = -100;
    params.output_height = 200;
    Result result = dxgiCapturer->capture(frame, params);
    EXPECT_NE(result, Result::Ok) << "Should reject negative width";
    
    params.output_width = 200;
    params.output_height = -100;
    result = dxgiCapturer->capture(frame, params);
    EXPECT_NE(result, Result::Ok) << "Should reject negative height";
    
    // Test zero dimensions
    params.output_width = 0;
    params.output_height = 200;
    result = dxgiCapturer->capture(frame, params);
    EXPECT_NE(result, Result::Ok) << "Should reject zero width";
    
    params.output_width = 200;
    params.output_height = 0;
    result = dxgiCapturer->capture(frame, params);
    EXPECT_NE(result, Result::Ok) << "Should reject zero height";
    
    // Test extremely large dimensions (should fail due to memory constraints)
    params.output_width = 100000;
    params.output_height = 100000;
    result = dxgiCapturer->capture(frame, params);
    EXPECT_NE(result, Result::Ok) << "Should reject extremely large dimensions";
    
    // Test valid dimensions
    params.output_width = 640;
    params.output_height = 480;
    result = dxgiCapturer->capture(frame, params);
    EXPECT_EQ(result, Result::Ok) << "Should accept valid dimensions";
}

// Test GPU fallback to CPU
TEST_F(CaptureProcessingTest, GPUFallbackToCPU) {
    CaptureParams params;
    params.output_width = 800;
    params.output_height = 600;
    params.use_gpu_processing = true; // Request GPU processing
    params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    
    Frame frame;
    Result result = dxgiCapturer->capture(frame, params);
    
    // Should succeed whether GPU is available or falls back to CPU
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(frame.width, 800);
    EXPECT_EQ(frame.height, 600);
    EXPECT_FALSE(frame.data.empty());
}

// Test color space conversion
TEST_F(CaptureProcessingTest, ColorSpaceConversion) {
    CaptureParams params;
    params.output_width = 400;
    params.output_height = 300;
    params.use_gpu_processing = false; // Use CPU for reliable testing
    
    // Test sRGB color space
    params.color_space = CaptureParams::ColorSpace::sRGB;
    Frame srgbFrame;
    Result result = dxgiCapturer->capture(srgbFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_FALSE(srgbFrame.data.empty());
    
    // Test Linear color space
    params.color_space = CaptureParams::ColorSpace::Linear;
    Frame linearFrame;
    result = dxgiCapturer->capture(linearFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_FALSE(linearFrame.data.empty());
}

// Test resize scale factors
TEST_F(CaptureProcessingTest, ResizeScaleFactors) {
    // Get original dimensions first
    Frame originalFrame;
    Result result = dxgiCapturer->capture(originalFrame);
    ASSERT_EQ(result, Result::Ok);
    
    CaptureParams params;
    params.use_gpu_processing = false;
    params.resize_quality = CaptureParams::ResizeQuality::Balanced;
    
    // Test downscaling (50%)
    params.output_width = originalFrame.width / 2;
    params.output_height = originalFrame.height / 2;
    Frame downscaledFrame;
    result = dxgiCapturer->capture(downscaledFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(downscaledFrame.width, params.output_width);
    EXPECT_EQ(downscaledFrame.height, params.output_height);
    
    // Test upscaling (150%)
    params.output_width = (originalFrame.width * 3) / 2;
    params.output_height = (originalFrame.height * 3) / 2;
    Frame upscaledFrame;
    result = dxgiCapturer->capture(upscaledFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(upscaledFrame.width, params.output_width);
    EXPECT_EQ(upscaledFrame.height, params.output_height);
    
    // Test aspect ratio change
    params.output_width = 1920;
    params.output_height = 1080;
    Frame aspectChangedFrame;
    result = dxgiCapturer->capture(aspectChangedFrame, params);
    EXPECT_EQ(result, Result::Ok);
    EXPECT_EQ(aspectChangedFrame.width, 1920);
    EXPECT_EQ(aspectChangedFrame.height, 1080);
}

// Test GPU texture capture (zero-copy)
TEST_F(CaptureProcessingTest, GPUTextureCapture) {
    if (!dxgiCapturer->isGpuCaptureAvailable()) {
        GTEST_SKIP() << "GPU capture not available";
    }
    
    GpuTexture gpuTexture;
    Result result = dxgiCapturer->captureGpu(gpuTexture);
    
    EXPECT_EQ(result, Result::Ok);
    EXPECT_NE(gpuTexture.d3d11_texture, nullptr);
}

// Test stress conditions
TEST_F(CaptureProcessingTest, StressTest) {
    CaptureParams params;
    params.output_width = 640;
    params.output_height = 480;
    params.use_gpu_processing = false; // Use CPU for reliability
    params.resize_quality = CaptureParams::ResizeQuality::Fastest;
    
    // Perform multiple captures in quick succession
    const int numCaptures = 10;
    for (int i = 0; i < numCaptures; ++i) {
        Frame frame;
        Result result = dxgiCapturer->capture(frame, params);
        EXPECT_EQ(result, Result::Ok) << "Failed on iteration " << i;
        EXPECT_EQ(frame.width, 640);
        EXPECT_EQ(frame.height, 480);
        EXPECT_FALSE(frame.data.empty());
    }
}

// Test Mitchell-Netravali filter quality
TEST_F(CaptureProcessingTest, MitchellNetravaliFilteeQuality) {
    CaptureParams params;
    params.output_width = 800;
    params.output_height = 600;
    params.resize_quality = CaptureParams::ResizeQuality::Highest; // Mitchell-Netravali
    
    // Test both CPU and GPU if available
    std::vector<bool> gpuSettings = {false};
    if (dxgiCapturer->isGpuCaptureAvailable()) {
        gpuSettings.push_back(true);
    }
    
    for (bool useGpu : gpuSettings) {
        params.use_gpu_processing = useGpu;
        Frame frame;
        Result result = dxgiCapturer->capture(frame, params);
        
        EXPECT_EQ(result, Result::Ok) << "Mitchell-Netravali failed for " 
                                      << (useGpu ? "GPU" : "CPU");
        EXPECT_EQ(frame.width, 800);
        EXPECT_EQ(frame.height, 600);
        EXPECT_FALSE(frame.data.empty());
        
        // Verify frame data is not all zeros or all the same value
        if (frame.data.size() >= 8) {
            bool hasVariation = false;
            uint8_t firstByte = frame.data[0];
            for (size_t i = 1; i < (std::min)(frame.data.size(), size_t(100)); ++i) {
                if (frame.data[i] != firstByte) {
                    hasVariation = true;
                    break;
                }
            }
            EXPECT_TRUE(hasVariation) << "Frame appears to be uniform (possible error)";
        }
    }
}
