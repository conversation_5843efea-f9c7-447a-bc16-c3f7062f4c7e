#include <windows.h>
#include <iostream>
#include <string>

BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    char windowText[256];
    char className[256];
    
    if (IsWindowVisible(hwnd) && GetWindowText(hwnd, windowText, sizeof(windowText))) {
        GetClassName(hwnd, className, sizeof(className));
        std::cout << "HWND: " << hwnd << " Title: \"" << windowText << "\" Class: \"" << className << "\"" << std::endl;
    }
    return TRUE;
}

int main() {
    std::cout << "Enumerating visible windows:" << std::endl;
    EnumWindows(EnumWindowsProc, 0);
    return 0;
}
