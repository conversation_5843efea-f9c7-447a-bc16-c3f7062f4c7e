#include "capscr/config.hpp"
#include "capscr/logging.hpp"
#include <nlohmann/json.hpp>
#include <fstream>
#include <filesystem>
#include <cstdlib>
#include <algorithm>
#include <cctype>
#include <sys/stat.h>

namespace capscr {
namespace config {

// JSON serialization helpers
NLOHMANN_JSON_SERIALIZE_ENUM(LogLevel, {
    {LogLevel::TRACE, "trace"},
    {LogLevel::DEBUG, "debug"},
    {LogLevel::INFO, "info"},
    {LogLevel::WARN, "warn"},
    {LogLevel::ERROR, "error"},
    {LogLevel::CRITICAL, "critical"},
    {LogLevel::OFF, "off"}
})

void to_json(nlohmann::json& j, const LogConfig& config) {
    j = nlohmann::json{
        {"level", config.level},
        {"console_enabled", config.console_enabled},
        {"file_enabled", config.file_enabled},
        {"file_path", config.file_path},
        {"max_file_size", config.max_file_size},
        {"max_files", config.max_files},
        {"pattern", config.pattern},
        {"auto_flush", config.auto_flush},
        {"module_levels", config.module_levels}
    };
}

void from_json(const nlohmann::json& j, LogConfig& config) {
    j.at("level").get_to(config.level);
    j.at("console_enabled").get_to(config.console_enabled);
    j.at("file_enabled").get_to(config.file_enabled);
    j.at("file_path").get_to(config.file_path);
    j.at("max_file_size").get_to(config.max_file_size);
    j.at("max_files").get_to(config.max_files);
    j.at("pattern").get_to(config.pattern);
    j.at("auto_flush").get_to(config.auto_flush);
    
    if (j.contains("module_levels")) {
        j.at("module_levels").get_to(config.module_levels);
    }
}

void to_json(nlohmann::json& j, const CaptureConfig& config) {
    j = nlohmann::json{
        {"gpu_texture_enabled", config.gpu_texture_enabled},
        {"performance_logging", config.performance_logging},
        {"capture_timeout_ms", config.capture_timeout_ms},
        {"preferred_adapter", config.preferred_adapter}
    };
}

void from_json(const nlohmann::json& j, CaptureConfig& config) {
    j.at("gpu_texture_enabled").get_to(config.gpu_texture_enabled);
    j.at("performance_logging").get_to(config.performance_logging);
    j.at("capture_timeout_ms").get_to(config.capture_timeout_ms);
    j.at("preferred_adapter").get_to(config.preferred_adapter);
}

void to_json(nlohmann::json& j, const AppConfig& config) {
    j = nlohmann::json{
        {"logging", config.logging},
        {"capture", config.capture},
        {"config_file_path", config.config_file_path},
        {"auto_reload", config.auto_reload},
        {"reload_interval_ms", config.reload_interval_ms}
    };
}

void from_json(const nlohmann::json& j, AppConfig& config) {
    j.at("logging").get_to(config.logging);
    j.at("capture").get_to(config.capture);
    
    if (j.contains("config_file_path")) {
        j.at("config_file_path").get_to(config.config_file_path);
    }
    if (j.contains("auto_reload")) {
        j.at("auto_reload").get_to(config.auto_reload);
    }
    if (j.contains("reload_interval_ms")) {
        j.at("reload_interval_ms").get_to(config.reload_interval_ms);
    }
}

// JsonConfigProvider implementation
bool JsonConfigProvider::load(const std::string& file_path, AppConfig& config) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        nlohmann::json j;
        file >> j;
        config = j.get<AppConfig>();
        config.config_file_path = file_path;
        
        return true;
    } catch (const std::exception& e) {
        CAPSCR_LOG_ERROR("Failed to load JSON config from '{}': {}", file_path, e.what());
        return false;
    }
}

bool JsonConfigProvider::save(const std::string& file_path, const AppConfig& config) {
    try {
        // Ensure directory exists
        std::filesystem::path path(file_path);
        std::filesystem::create_directories(path.parent_path());
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        nlohmann::json j = config;
        file << j.dump(2); // Pretty print with 2-space indentation
        
        return true;
    } catch (const std::exception& e) {
        CAPSCR_LOG_ERROR("Failed to save JSON config to '{}': {}", file_path, e.what());
        return false;
    }
}

// ConfigManager implementation
ConfigManager& ConfigManager::instance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::load_config(const std::string& file_path) {
    std::string config_path = file_path;
    
    // Use environment variable if no path specified
    if (config_path.empty()) {
        const char* env_path = std::getenv(ENV_CONFIG_FILE);
        if (env_path) {
            config_path = env_path;
        } else {
            config_path = "capscr_config.json"; // Default
        }
    }
    
    // Get appropriate provider
    provider_ = get_provider(config_path);
    if (!provider_) {
        CAPSCR_LOG_ERROR("No config provider found for file: {}", config_path);
        return false;
    }
    
    // Load configuration
    if (!provider_->load(config_path, config_)) {
        CAPSCR_LOG_WARN("Failed to load config from '{}', using defaults", config_path);
        // Create default config file if it doesn't exist
        if (!std::filesystem::exists(config_path)) {
            create_default_config(config_path);
        }
        return false;
    }
    
    current_file_path_ = config_path;
    
    // Apply environment variable overrides
    apply_env_overrides();
    
    // Update last modified time for auto-reload
    struct stat file_stat;
    if (stat(config_path.c_str(), &file_stat) == 0) {
        last_modified_time_ = file_stat.st_mtime;
    }
    
    CAPSCR_LOG_INFO("Configuration loaded from: {}", config_path);
    return true;
}

bool ConfigManager::save_config(const std::string& file_path) {
    std::string save_path = file_path.empty() ? current_file_path_ : file_path;
    
    if (save_path.empty()) {
        CAPSCR_LOG_ERROR("No config file path specified for saving");
        return false;
    }
    
    if (!provider_) {
        provider_ = get_provider(save_path);
    }
    
    if (!provider_) {
        CAPSCR_LOG_ERROR("No config provider available for saving to: {}", save_path);
        return false;
    }
    
    return provider_->save(save_path, config_);
}

void ConfigManager::apply_env_overrides() {
    // Log level override
    const char* log_level_env = std::getenv(ENV_LOG_LEVEL);
    if (log_level_env) {
        apply_log_level_override(log_level_env);
    }
    
    // Log file override
    const char* log_file_env = std::getenv(ENV_LOG_FILE);
    if (log_file_env) {
        apply_log_file_override(log_file_env);
    }
    
    // GPU capture override
    const char* gpu_enabled_env = std::getenv(ENV_GPU_ENABLED);
    if (gpu_enabled_env) {
        std::string gpu_str = gpu_enabled_env;
        std::transform(gpu_str.begin(), gpu_str.end(), gpu_str.begin(), ::tolower);
        config_.capture.gpu_texture_enabled = (gpu_str == "true" || gpu_str == "1" || gpu_str == "yes");
        CAPSCR_LOG_INFO("GPU capture override from environment: {}", 
                        config_.capture.gpu_texture_enabled ? "enabled" : "disabled");
    }
}

bool ConfigManager::create_default_config(const std::string& file_path) {
    AppConfig default_config;
    // default_config already has sensible defaults from the struct
    
    auto provider = get_provider(file_path);
    if (!provider) {
        return false;
    }
    
    if (provider->save(file_path, default_config)) {
        CAPSCR_LOG_INFO("Created default configuration file: {}", file_path);
        return true;
    }
    
    return false;
}

void ConfigManager::enable_auto_reload(bool enable) {
    config_.auto_reload = enable;
    if (enable) {
        CAPSCR_LOG_INFO("Configuration auto-reload enabled (interval: {}ms)", 
                        config_.reload_interval_ms);
    }
}

void ConfigManager::check_for_reload() {
    if (!config_.auto_reload || current_file_path_.empty()) {
        return;
    }
    
    struct stat file_stat;
    if (stat(current_file_path_.c_str(), &file_stat) == 0) {
        if (file_stat.st_mtime > last_modified_time_) {
            CAPSCR_LOG_INFO("Configuration file changed, reloading...");
            load_config(current_file_path_);
        }
    }
}

std::unique_ptr<IConfigProvider> ConfigManager::get_provider(const std::string& file_path) {
    std::filesystem::path path(file_path);
    std::string extension = path.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    
    if (extension == ".json") {
        return std::make_unique<JsonConfigProvider>();
    }
    
    // Future: Add YAML provider, INI provider, etc.
    
    return nullptr;
}

void ConfigManager::apply_log_level_override(const std::string& env_value) {
    LogLevel level = parse_log_level(env_value);
    config_.logging.level = level;
    CAPSCR_LOG_INFO("Log level override from environment: {}", log_level_to_string(level));
}

void ConfigManager::apply_log_file_override(const std::string& env_value) {
    config_.logging.file_path = env_value;
    CAPSCR_LOG_INFO("Log file override from environment: {}", env_value);
}

// Utility functions
LogLevel parse_log_level(const std::string& level_str) {
    std::string lower_str = level_str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "trace") return LogLevel::TRACE;
    if (lower_str == "debug") return LogLevel::DEBUG;
    if (lower_str == "info") return LogLevel::INFO;
    if (lower_str == "warn" || lower_str == "warning") return LogLevel::WARN;
    if (lower_str == "error") return LogLevel::ERROR;
    if (lower_str == "critical" || lower_str == "fatal") return LogLevel::CRITICAL;
    if (lower_str == "off" || lower_str == "none") return LogLevel::OFF;
    
    return LogLevel::INFO; // Default fallback
}

std::string log_level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "trace";
        case LogLevel::DEBUG: return "debug";
        case LogLevel::INFO: return "info";
        case LogLevel::WARN: return "warn";
        case LogLevel::ERROR: return "error";
        case LogLevel::CRITICAL: return "critical";
        case LogLevel::OFF: return "off";
        default: return "info";
    }
}

} // namespace config
} // namespace capscr
