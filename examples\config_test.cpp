#include "capscr/config.hpp"
#include "capscr/logging.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace capscr;

void test_config_loading() {
    std::cout << "=== Testing Configuration System ===" << std::endl;
    
    // Load configuration
    auto& config_mgr = config::ConfigManager::instance();
    
    if (config_mgr.load_config("capscr_config.json")) {
        std::cout << "✓ Configuration loaded successfully" << std::endl;
        
        const auto& cfg = config_mgr.get_config();
        
        // Display loaded configuration
        std::cout << "Log level: " << config::log_level_to_string(cfg.logging.level) << std::endl;
        std::cout << "Console enabled: " << (cfg.logging.console_enabled ? "yes" : "no") << std::endl;
        std::cout << "File enabled: " << (cfg.logging.file_enabled ? "yes" : "no") << std::endl;
        std::cout << "File path: " << cfg.logging.file_path << std::endl;
        std::cout << "GPU texture enabled: " << (cfg.capture.gpu_texture_enabled ? "yes" : "no") << std::endl;
        std::cout << "Performance logging: " << (cfg.capture.performance_logging ? "yes" : "no") << std::endl;
        
        // Initialize logging with the loaded configuration
        logging::init(cfg.logging);
        
    } else {
        std::cout << "✗ Failed to load configuration, using defaults" << std::endl;
        logging::init();
    }
}

void test_enhanced_logging() {
    std::cout << "\n=== Testing Enhanced Logging ===" << std::endl;
    
    // Basic logging
    CAPSCR_LOG_INFO("Configuration system test started");
    CAPSCR_LOG_DEBUG("Debug message - you might not see this if level is INFO");
    CAPSCR_LOG_WARN("Warning message with parameter: {}", 42);
    
    // Performance logging test
    {
        CAPSCR_LOG_PERF_START("test_operation");
        
        // Simulate some work
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        CAPSCR_LOG_INFO("Performing test operation...");
        
        // Performance timer will automatically log when it goes out of scope
    }
    
    // Format string test
    try {
        std::string user = "developer";
        int count = 5;
        double ratio = 0.856;
        
        CAPSCR_LOG_INFO("User '{}' processed {} items with {:.2f}% success rate", 
                        user, count, ratio * 100);
                        
        // Test with different data types
        void* ptr = &user;
        CAPSCR_LOG_DEBUG("Memory address: {}, size: {}", ptr, sizeof(user));
        
    } catch (const std::exception& e) {
        CAPSCR_LOG_ERROR("Format string test failed: {}", e.what());
    }
}

void test_environment_overrides() {
    std::cout << "\n=== Testing Environment Variable Overrides ===" << std::endl;
    
    // Test environment variable parsing
    std::cout << "Set environment variables to test overrides:" << std::endl;
    std::cout << "  SET CAPSCR_LOG_LEVEL=debug" << std::endl;
    std::cout << "  SET CAPSCR_LOG_FILE=custom_log.txt" << std::endl;
    std::cout << "  SET CAPSCR_GPU_ENABLED=false" << std::endl;
    
    auto& config_mgr = config::ConfigManager::instance();
    config_mgr.apply_env_overrides();
    
    const auto& cfg = config_mgr.get_config();
    CAPSCR_LOG_INFO("After environment overrides:");
    CAPSCR_LOG_INFO("  Log level: {}", config::log_level_to_string(cfg.logging.level));
    CAPSCR_LOG_INFO("  Log file: {}", cfg.logging.file_path);
    CAPSCR_LOG_INFO("  GPU enabled: {}", cfg.capture.gpu_texture_enabled ? "yes" : "no");
}

void test_config_creation() {
    std::cout << "\n=== Testing Default Config Creation ===" << std::endl;
    
    auto& config_mgr = config::ConfigManager::instance();
    
    if (config_mgr.create_default_config("default_config.json")) {
        std::cout << "✓ Default configuration file created: default_config.json" << std::endl;
        CAPSCR_LOG_INFO("Created default configuration file");
    } else {
        std::cout << "✗ Failed to create default configuration" << std::endl;
        CAPSCR_LOG_ERROR("Failed to create default configuration file");
    }
}

int main() {
    try {
        test_config_loading();
        test_enhanced_logging();
        test_environment_overrides();
        test_config_creation();
        
        std::cout << "\n=== Configuration System Test Complete ===" << std::endl;
        CAPSCR_LOG_INFO("All configuration tests completed successfully");
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    // Keep logs flushed
    logging::shutdown();
    return 0;
}
