#ifdef _WIN32
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>
#include <iostream>
#include <memory>
#include <vector>
#include <thread>
#include <chrono>
#include <algorithm>

#include "capscr/capture.hpp"

using namespace capscr;

int wmain(int argc, wchar_t** argv) {
    auto capturer = createDxgiCapturer();
    if (!capturer) { std::cout << "DXGI capturer not available\n"; return 1; }

    // Initialize for full-screen
    if (capturer->init(CaptureTargetType::FullScreen) != Result::Ok) {
        std::cout << "capturer init failed\n"; return 2;
    }

    ZeroCopyFrame frame;
    ZeroCopyOptions opts;
    opts.prefer_shared_handle = false; // in-process
    opts.prefer_nv12 = false; // keep simple for demo

    Result r = capturer->captureZeroCopy(frame, opts);
    if (r != Result::Ok) {
        std::cout << "captureZeroCopy failed: " << static_cast<int>(r) << "\n";
        return 3;
    }

    if (frame.mode != ZeroCopyFrame::Mode::LocalBorrow || !frame.d3d11_texture) {
        std::cout << "ZeroCopy not in LocalBorrow mode or no texture returned\n";
        return 4;
    }

    // We have a D3D11 texture held by ZeroCopyFrame (non-owning pointer). We'll copy to a staging texture and Map
    ID3D11Device* dev = frame.d3d11_device;
    ID3D11DeviceContext* ctx = frame.d3d11_context;
    ID3D11Texture2D* src = frame.d3d11_texture;

    // Get desc
    D3D11_TEXTURE2D_DESC desc = {};
    src->GetDesc(&desc);

    D3D11_TEXTURE2D_DESC stagingDesc = desc;
    stagingDesc.Usage = D3D11_USAGE_STAGING;
    stagingDesc.BindFlags = 0;
    stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stagingDesc.MiscFlags = 0;

    ID3D11Texture2D* staging = nullptr;
    HRESULT hr = dev->CreateTexture2D(&stagingDesc, nullptr, &staging);
    if (FAILED(hr) || !staging) { std::cout << "Create staging failed hr=" << std::hex << hr << std::dec << "\n"; frame.release(); return 5; }

    // Copy and flush
    ctx->CopyResource(staging, src);
    ctx->Flush();

    D3D11_MAPPED_SUBRESOURCE mapped;
    hr = ctx->Map(staging, 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) { std::cout << "Map failed hr=" << std::hex << hr << std::dec << "\n"; staging->Release(); frame.release(); return 6; }

    // Print first 16 bytes of mapped data (or as many as available)
    uint8_t* ptr = reinterpret_cast<uint8_t*>(mapped.pData);
    int toPrint = std::min(16, static_cast<int>(mapped.RowPitch));
    std::cout << "Mapped first bytes:";
    for (int i = 0; i < toPrint; ++i) std::cout << " " << (int)ptr[i];
    std::cout << "\n";

    ctx->Unmap(staging, 0);
    staging->Release();

    // Release ZeroCopyFrame (will call backend release)
    frame.release();

    std::cout << "dxgi_zero_copy_demo done\n";
    return 0;
}
#else
int main(){return 0;}
#endif
