#ifdef _WIN32
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dxgi1_2.h>
#include <wrl.h>
#include <iostream>
#include <vector>
#include <string>

using Microsoft::WRL::ComPtr;

// Simple consumer that opens a shared handle produced by capscr and
// (optionally) uses IDXGIKeyedMutex to sync, then copies the texture
// to a staging resource and writes plane raw data to disk.

static void usage() {
    std::cout << "consumer_shared_texture <shared_handle_hex> [--uv <shared_handle_uv_hex>] [--dump base_name]" << std::endl;
}

int main(int argc, char** argv) {
    if (argc < 2) {
        usage();
        return 1;
    }

    std::string handleHex = argv[1];
    HANDLE sharedHandle = reinterpret_cast<HANDLE>(std::stoull(handleHex, nullptr, 16));

    HANDLE sharedHandleUV = nullptr;
    std::string dumpBase = "dump";

    for (int i = 2; i < argc; ++i) {
        std::string a = argv[i];
        if (a == "--uv" && i + 1 < argc) {
            sharedHandleUV = reinterpret_cast<HANDLE>(std::stoull(argv[++i], nullptr, 16));
        } else if (a == "--dump" && i + 1 < argc) {
            dumpBase = argv[++i];
        }
    }

    // Create D3D11 device
    ComPtr<ID3D11Device> device;
    ComPtr<ID3D11DeviceContext> context;
    D3D_FEATURE_LEVEL featureLevel;
    HRESULT hr = D3D11CreateDevice(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr,
        0, nullptr, 0, D3D11_SDK_VERSION,
        &device, &featureLevel, &context);
    if (FAILED(hr)) {
        std::cerr << "Failed to create D3D11 device: " << std::hex << hr << std::endl;
        return 1;
    }

    // Open shared texture
    ComPtr<ID3D11Texture2D> sharedTex;
    hr = device->OpenSharedResource(sharedHandle, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(sharedTex.GetAddressOf()));
    if (FAILED(hr) || !sharedTex) {
        std::cerr << "OpenSharedResource failed: " << std::hex << hr << std::endl;
        return 1;
    }

    // Query desc
    D3D11_TEXTURE2D_DESC desc; sharedTex->GetDesc(&desc);
    std::cout << "Opened shared texture: " << desc.Width << "x" << desc.Height << " format=" << desc.Format << std::endl;

    // Try keyed mutex
    ComPtr<IDXGIKeyedMutex> km;
    if (SUCCEEDED(sharedTex.As(&km))) {
        std::cout << "IDXGIKeyedMutex available, acquiring key=0..." << std::endl;
        hr = km->AcquireSync(0, 5000);
        if (FAILED(hr)) {
            std::cerr << "AcquireSync failed: " << std::hex << hr << std::endl;
        } else {
            std::cout << "Acquired keyed mutex" << std::endl;
        }
    }

    // Copy to staging
    D3D11_TEXTURE2D_DESC stagingDesc = desc;
    stagingDesc.Usage = D3D11_USAGE_STAGING;
    stagingDesc.BindFlags = 0;
    stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    stagingDesc.MiscFlags = 0;
    ComPtr<ID3D11Texture2D> staging;
    hr = device->CreateTexture2D(&stagingDesc, nullptr, &staging);
    if (FAILED(hr) || !staging) {
        std::cerr << "Create staging failed: " << std::hex << hr << std::endl;
        if (km) km->ReleaseSync(0);
        return 1;
    }

    context->CopyResource(staging.Get(), sharedTex.Get());
    context->Flush();

    D3D11_MAPPED_SUBRESOURCE mapped;
    hr = context->Map(staging.Get(), 0, D3D11_MAP_READ, 0, &mapped);
    if (FAILED(hr)) {
        std::cerr << "Map failed: " << std::hex << hr << std::endl;
        if (km) km->ReleaseSync(0);
        return 1;
    }

    // Save first plane raw
    std::string outFile = dumpBase + "_plane0.raw";
    FILE* f = nullptr;
    fopen_s(&f, outFile.c_str(), "wb");
    if (f) {
        for (UINT row = 0; row < desc.Height; ++row) {
            fwrite(reinterpret_cast<uint8_t*>(mapped.pData) + row * mapped.RowPitch, 1, desc.Width * 4, f);
        }
        fclose(f);
        std::cout << "Wrote " << outFile << std::endl;
    }

    context->Unmap(staging.Get(), 0);

    if (km) {
        km->ReleaseSync(0);
        std::cout << "Released keyed mutex" << std::endl;
    }

    // If UV handle provided, open and dump similarly
    if (sharedHandleUV) {
        ComPtr<ID3D11Texture2D> sharedUVTex;
        hr = device->OpenSharedResource(sharedHandleUV, __uuidof(ID3D11Texture2D), reinterpret_cast<void**>(sharedUVTex.GetAddressOf()));
        if (SUCCEEDED(hr) && sharedUVTex) {
            D3D11_TEXTURE2D_DESC uvDesc; sharedUVTex->GetDesc(&uvDesc);
            std::cout << "Opened UV shared texture: " << uvDesc.Width << "x" << uvDesc.Height << " format=" << uvDesc.Format << std::endl;
            D3D11_TEXTURE2D_DESC uvSt = uvDesc; uvSt.Usage = D3D11_USAGE_STAGING; uvSt.BindFlags = 0; uvSt.CPUAccessFlags = D3D11_CPU_ACCESS_READ; uvSt.MiscFlags = 0;
            ComPtr<ID3D11Texture2D> stUV; hr = device->CreateTexture2D(&uvSt, nullptr, &stUV);
            if (SUCCEEDED(hr) && stUV) {
                context->CopyResource(stUV.Get(), sharedUVTex.Get());
                context->Flush();
                D3D11_MAPPED_SUBRESOURCE m2; hr = context->Map(stUV.Get(), 0, D3D11_MAP_READ, 0, &m2);
                if (SUCCEEDED(hr)) {
                    std::string outUV = dumpBase + "_uv.raw";
                    FILE* f2 = nullptr; fopen_s(&f2, outUV.c_str(), "wb");
                    if (f2) {
                        for (UINT row = 0; row < uvDesc.Height; ++row) {
                            fwrite(reinterpret_cast<uint8_t*>(m2.pData) + row * m2.RowPitch, 1, uvDesc.Width * 2, f2);
                        }
                        fclose(f2);
                        std::cout << "Wrote " << outUV << std::endl;
                    }
                    context->Unmap(stUV.Get(), 0);
                }
            }
        }
    }

    return 0;
}
#else
int main() { return 0; }
#endif
