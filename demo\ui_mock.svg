<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="720" viewBox="0 0 1200 720" xmlns="http://www.w3.org/2000/svg">
  <rect x="0" y="0" width="1200" height="720" fill="#222" />
  <text x="20" y="32" fill="#fff" font-family="Segoe UI, Arial" font-size="18">capscr ImGui Demo</text>

  <!-- Left panel -->
  <rect x="16" y="56" width="360" height="640" rx="6" ry="6" fill="#2b2b2b" stroke="#444" />
  <text x="28" y="84" fill="#fff" font-family="Segoe UI, Arial" font-size="14">Parameters</text>
  <rect x="28" y="96" width="320" height="1" fill="#3f3f3f" />

  <!-- Controls inside left panel -->
  <text x="36" y="124" fill="#ddd" font-family="Segoe UI, Arial" font-size="13">[ ] Prefer GPU zero-copy</text>
  <text x="36" y="148" fill="#ddd" font-family="Segoe UI, Arial" font-size="12">Backend caps: 0x00000001</text>
  <!-- capture target is determined by user interaction on virtual screens / window list -->
  <!-- Apply Target removed: selection is applied immediately on user actions -->

  <!-- Minimal status -->
  <text x="36" y="206" fill="#ccc" font-family="Segoe UI, Arial" font-size="12">Status: Showing primary screen (auto)</text>

  <!-- Capture methods (platform-specific) -->
  <text x="36" y="236" fill="#eee" font-family="Segoe UI, Arial" font-size="13">Capture methods</text>
  <text x="36" y="254" fill="#ccc" font-family="Segoe UI, Arial" font-size="11">Windows: [ ] GDI   [ ] DXGI</text>
  <text x="36" y="270" fill="#ccc" font-family="Segoe UI, Arial" font-size="11">Linux:   [ ] X11   [ ] Wayland</text>
  <text x="36" y="286" fill="#ccc" font-family="Segoe UI, Arial" font-size="11">macOS:   [ ] quantz</text>

  <!-- Virtual screens thumbnails -->
  <text x="36" y="308" fill="#eee" font-family="Segoe UI, Arial" font-size="13">Virtual screens</text>
  <rect x="36" y="332" width="96" height="56" rx="4" ry="4" fill="#111" stroke="#555" />
  <text x="48" y="438" fill="#999" font-family="Segoe UI, Arial" font-size="11">Screen 0</text>
  <rect x="140" y="332" width="120" height="56" rx="4" ry="4" fill="#111" stroke="#5a5" />
  <text x="152" y="366" fill="#9f9" font-family="Segoe UI, Arial" font-size="11">Screen 1 (selected)</text>
  <!-- Selection box drawn on virtual screen to indicate current region -->
  <rect x="170" y="350" width="64" height="28" fill="none" stroke="#0af" stroke-width="2" stroke-dasharray="4 3" />
  <circle cx="228" cy="352" r="6" fill="#0af" />
  <rect x="36" y="392" width="224" height="56" rx="4" ry="4" fill="#111" stroke="#555" />
  <text x="48" y="426" fill="#999" font-family="Segoe UI, Arial" font-size="11">Screen 2</text>
  <text x="36" y="452" fill="#777" font-family="Segoe UI, Arial" font-size="11">Drag on a virtual screen to create/move/resize selection. Del to delete selection.</text>

  <!-- Window list -->
  <text x="36" y="480" fill="#eee" font-family="Segoe UI, Arial" font-size="13">Windows</text>
  <rect x="36" y="492" width="320" height="160" rx="4" ry="4" fill="#1a1a1a" stroke="#333" />
  <text x="48" y="514" fill="#ddd" font-family="Segoe UI, Arial" font-size="12">Calculator.exe</text>
  <text x="48" y="534" fill="#ddd" font-family="Segoe UI, Arial" font-size="12">Browser - capscr example</text>
  <text x="48" y="554" fill="#ddd" font-family="Segoe UI, Arial" font-size="12">Explorer</text>
  <text x="48" y="574" fill="#ddd" font-family="Segoe UI, Arial" font-size="12">Notepad</text>
  <text x="48" y="594" fill="#777" font-family="Segoe UI, Arial" font-size="11">Select a window to set capture target</text>

  <!-- Right preview area -->
  <rect x="392" y="56" width="792" height="560" rx="6" ry="6" fill="#111" stroke="#444" />
  <text x="404" y="84" fill="#fff" font-family="Segoe UI, Arial" font-size="14">Preview</text>
  <rect x="404" y="100" width="760" height="504" fill="#111" stroke="#333" />
  <text x="420" y="140" fill="#777" font-family="Segoe UI, Arial" font-size="12">Preview (shows only the selected content: screen/window/region)</text>
  <!-- Screenshot button (save lossless PNG) -->
  <rect x="1080" y="116" width="84" height="28" rx="4" ry="4" fill="#3a7" />
  <text x="1092" y="135" fill="#022" font-family="Segoe UI, Arial" font-size="12">Save PNG</text>
  <!-- Preview displays only selected content; selection box appears on virtual screen thumbnails -->

  <!-- Floating overlay -->
  <rect x="860" y="64" width="320" height="28" rx="4" ry="4" fill="#000" fill-opacity="0.6" stroke="#666" />
  <text x="872" y="84" fill="#fff" font-family="Segoe UI, Arial" font-size="12">FPS: 59.9  |  Res: 1920x1080  |  GPU: 12%</text>

  <!-- Footer note -->
  <text x="18" y="706" fill="#888" font-family="Segoe UI, Arial" font-size="11">Left: controls/targets. Right: preview. Overlay: live stats. GPU path uses SRV on Windows.</text>
</svg>
