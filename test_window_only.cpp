#include <iostream>
#include <memory>
#include "capscr/capture.hpp"
#ifdef _WIN32
#include "capscr/platform/windows/capturer_dxgi.hpp"
#endif

using namespace capscr;

int main() {
    auto capturer = createBestCapturer();
    if (!capturer) {
        std::cerr << "No capturer available.\n";
        return 1;
    }

    std::string windowTitle = "Char As Gem";
    Result r = capturer->init(CaptureTargetType::Window, "", nullptr, windowTitle);
    if (r != Result::Ok) {
        std::cout << "Window init failed with result: " << (int)r << "\n";
        return 1;
    }

    std::cout << "Window capture initialized successfully.\n";
    
    Frame f;
    r = capturer->capture(f);
    if (r == Result::Ok) {
        std::cout << "SUCCESS - Captured window frame: " << f.width << "x" << f.height << "\n";
    } else {
        std::cout << "ERROR - Window capture failed with result: " << (int)r << "\n";
    }

    capturer->shutdown();
    return 0;
}
