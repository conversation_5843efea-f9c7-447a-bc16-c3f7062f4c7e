#ifdef _WIN32
#include "dxgi_device.hpp"
#include <iostream>

namespace capscr {

DXGIDevice::DXGIDevice() : featureLevel_(D3D_FEATURE_LEVEL_11_0) {
}

DXGIDevice::~DXGIDevice() {
    shutdown();
}

Result DXGIDevice::initialize() {
    if (device_) {
        CAPSCR_LOG_DEBUG("DXGIDevice already initialized");
        return Result::Ok;
    }

    D3D_FEATURE_LEVEL levels[] = { 
        D3D_FEATURE_LEVEL_11_0, 
        D3D_FEATURE_LEVEL_10_1, 
        D3D_FEATURE_LEVEL_10_0 
    };
    
    UINT creationFlags = D3D11_CREATE_DEVICE_BGRA_SUPPORT;
    
    HRESULT hr = D3D11CreateDevice(
        nullptr,                    // adapter
        D3D_DRIVER_TYPE_HARDWARE,   // driver type
        nullptr,                    // software
        creationFlags,              // flags
        levels,                     // feature levels
        ARRAYSIZE(levels),          // feature levels count
        D3D11_SDK_VERSION,          // sdk version
        &device_,                   // device
        &featureLevel_,             // feature level
        &context_                   // context
    );

    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("D3D11CreateDevice failed");
        return Result::Unsupported;
    }

    if (!device_ || !context_) {
        CAPSCR_LOG_ERROR("D3D11CreateDevice succeeded but device/context is null");
        return Result::Unsupported;
    }

    CAPSCR_LOG_INFO("DXGIDevice initialized successfully");
    return Result::Ok;
}

void DXGIDevice::shutdown() {
    if (context_) {
        context_->ClearState();
        context_->Flush();
    }
    
    context_.Reset();
    device_.Reset();
    
    CAPSCR_LOG_DEBUG("DXGIDevice shutdown complete");
}

Result DXGIDevice::recreateDevice() {
    CAPSCR_LOG_INFO("Recreating DXGI device after device lost/removed");
    
    // Clean up existing device
    shutdown();
    
    // Try to create new device
    return initialize();
}

bool DXGIDevice::isValid() const {
    if (!device_ || !context_) {
        return false;
    }
    
    // Check if device was removed/reset
    HRESULT reason = device_->GetDeviceRemovedReason();
    if (FAILED(reason)) {
        CAPSCR_LOG_WARN("Device removed/reset detected");
        return false;
    }
    
    return true;
}

bool DXGIDevice::createDuplication(const std::string& displayId,
                                  ComPtr<IDXGIOutputDuplication>& outDup,
                                  int& outAdapter,
                                  int& outOutput,
                                  std::string& outMatchedId) {
    outDup.Reset();
    outAdapter = -1;
    outOutput = -1;
    outMatchedId.clear();
    
    if (!isValid()) {
        CAPSCR_LOG_ERROR("Cannot create duplication: device is not valid");
        return false;
    }
    
    return enumerateOutputs(displayId, outDup, outAdapter, outOutput, outMatchedId);
}

std::vector<DisplayInfo> DXGIDevice::listDisplays() {
    std::vector<DisplayInfo> displays;
    
    ComPtr<IDXGIFactory1> factory;
    HRESULT hr = CreateDXGIFactory1(__uuidof(IDXGIFactory1), 
                                   reinterpret_cast<void**>(factory.GetAddressOf()));
    if (FAILED(hr)) {
        CAPSCR_LOG_ERROR("CreateDXGIFactory1 failed");
        return displays;
    }

    ComPtr<IDXGIAdapter1> adapter;
    for (UINT adapterIndex = 0; ; ++adapterIndex) {
        adapter.Reset();
        if (FAILED(factory->EnumAdapters1(adapterIndex, &adapter))) {
            break; // No more adapters
        }

        ComPtr<IDXGIOutput> output;
        for (UINT outputIndex = 0; ; ++outputIndex) {
            output.Reset();
            if (FAILED(adapter->EnumOutputs(outputIndex, &output))) {
                break; // No more outputs
            }

            DXGI_OUTPUT_DESC desc;
            if (FAILED(output->GetDesc(&desc))) {
                continue;
            }

            // Convert device name from wide string
            int needed = WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, 
                                           nullptr, 0, nullptr, nullptr);
            std::string deviceName;
            if (needed > 0) {
                deviceName.resize(needed);
                WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, 
                                  &deviceName[0], needed, nullptr, nullptr);
                if (!deviceName.empty() && deviceName.back() == '\0') {
                    deviceName.pop_back();
                }
            }

            DisplayInfo display;
            display.id = "dxgi:" + std::to_string(adapterIndex) + ":" + std::to_string(outputIndex);
            display.name = deviceName.empty() ? display.id : deviceName;
            display.bounds.x = desc.DesktopCoordinates.left;
            display.bounds.y = desc.DesktopCoordinates.top;
            display.bounds.width = desc.DesktopCoordinates.right - desc.DesktopCoordinates.left;
            display.bounds.height = desc.DesktopCoordinates.bottom - desc.DesktopCoordinates.top;
            display.scale = 1.0f; // TODO: Get actual DPI scale

            displays.push_back(display);
            
            CAPSCR_LOG_DEBUG("Found display");
        }
    }

    return displays;
}

std::string DXGIDevice::hrToString(HRESULT hr) {
    char buf[512] = {0};
    DWORD flags = FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS;
    DWORD len = FormatMessageA(flags, NULL, static_cast<DWORD>(hr), 
                              MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), 
                              buf, static_cast<DWORD>(sizeof(buf)), NULL);
    
    if (len == 0) {
        // Fallback to hex string if FormatMessage failed
        char tmp[64];
        sprintf_s(tmp, sizeof(tmp), "0x%08x", hr);
        return std::string(tmp);
    }
    
    // Trim trailing newlines
    while (len > 0 && (buf[len-1] == '\n' || buf[len-1] == '\r')) {
        buf[--len] = '\0';
    }
    
    return std::string(buf);
}

bool DXGIDevice::enumerateOutputs(const std::string& displayId,
                                 ComPtr<IDXGIOutputDuplication>& outDup,
                                 int& outAdapter,
                                 int& outOutput,
                                 std::string& outMatchedId) {
    ComPtr<IDXGIDevice> dxgiDevice;
    if (FAILED(device_.As(&dxgiDevice))) {
        CAPSCR_LOG_ERROR("Failed to get IDXGIDevice interface");
        return false;
    }

    ComPtr<IDXGIAdapter> adapter;
    if (FAILED(dxgiDevice->GetAdapter(&adapter))) {
        CAPSCR_LOG_ERROR("Failed to get DXGI adapter");
        return false;
    }

    ComPtr<IDXGIOutput> output;
    ComPtr<IDXGIOutput1> output1;
    
    for (UINT outputIndex = 0; ; ++outputIndex) {
        output.Reset();
        if (FAILED(adapter->EnumOutputs(outputIndex, &output))) {
            break; // No more outputs
        }
        
        output1.Reset();
        if (FAILED(output.As(&output1))) {
            continue;
        }

        DXGI_OUTPUT_DESC desc;
        if (FAILED(output->GetDesc(&desc))) {
            continue;
        }

        // Convert device name
        int needed = WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, 
                                       nullptr, 0, nullptr, nullptr);
        std::string deviceName;
        if (needed > 0) {
            deviceName.resize(needed);
            WideCharToMultiByte(CP_UTF8, 0, desc.DeviceName, -1, 
                              &deviceName[0], needed, nullptr, nullptr);
            if (!deviceName.empty() && deviceName.back() == '\0') {
                deviceName.pop_back();
            }
        }

        std::string candidateId = "dxgi:0:" + std::to_string(outputIndex);
        
        // Check if this output matches the requested display
        bool matches = displayId.empty() || 
                      displayId == deviceName || 
                      displayId == candidateId;
        
        if (!matches) {
            continue;
        }

        CAPSCR_LOG_DEBUG("Trying to create duplication");

        ComPtr<IDXGIOutputDuplication> duplication;
        HRESULT hr = output1->DuplicateOutput(device_.Get(), &duplication);
        
        if (FAILED(hr)) {
            CAPSCR_LOG_WARN("DuplicateOutput failed");
            
            // If specific display was requested, this is an error
            if (!displayId.empty()) {
                return false;
            }
            continue;
        }

        // Success
        outDup = duplication;
        outAdapter = 0; // Currently only supporting adapter 0
        outOutput = static_cast<int>(outputIndex);
        outMatchedId = displayId.empty() ? candidateId : displayId;
        
        CAPSCR_LOG_INFO("Created duplication for display");
        return true;
    }

    CAPSCR_LOG_ERROR("No matching output found");
    return false;
}

} // namespace capscr

#endif // _WIN32
