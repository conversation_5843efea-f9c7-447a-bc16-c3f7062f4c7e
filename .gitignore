# CMake build
/build/
/CMakeFiles/
/CMakeCache.txt
/cmake_install.cmake

# Visual Studio
*.sln
*.vcxproj
*.vcxproj.*
*.vcxproj.filters
*.user
\x64/
\Release/
\Debug/

# Generated libraries and executables
*.lib
*.dll
*.exe
*.pdb
*.obj
*.tlog
*.stamp
*.recipe

# Examples build output
/examples/Release/

# Generated images and logs
*.png
*.bmp
*.log

# vcpkg
/vcpkg_installed/
/packages/

# OS files
Thumbs.db
.DS_Store

# IDEs
.idea/
.vscode/
.vs/
out/
build_test/
build_simple/
demo/build/

imgui.ini
