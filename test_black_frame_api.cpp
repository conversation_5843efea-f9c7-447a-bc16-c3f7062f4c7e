#include "include/capscr/capture.hpp"
#include "include/capscr/platform/windows/capturer_dxgi.hpp"
#include <iostream>
#include <memory>

int main() {
    try {
        // Create DXGI capturer
        auto capturer = std::make_unique<capscr::DXGICapturer>();
        
        // Test default configuration
        auto defaultConfig = capturer->getBlackFrameConfig();
        std::cout << "Default config:" << std::endl;
        std::cout << "  enable_detection: " << defaultConfig.enable_detection << std::endl;
        std::cout << "  enable_auto_retry: " << defaultConfig.enable_auto_retry << std::endl;
        std::cout << "  max_retries: " << defaultConfig.max_retries << std::endl;
        std::cout << "  retry_delay_ms: " << defaultConfig.retry_delay_ms << std::endl;
        std::cout << "  black_threshold: " << defaultConfig.black_threshold << std::endl;
        
        // Test setting configuration
        capscr::BlackFrameConfig config;
        config.enable_detection = true;
        config.enable_auto_retry = true;
        config.max_retries = 3;
        config.retry_delay_ms = 100;
        config.black_threshold = 0.05f;
        config.log_black_frames = true;
        config.validate_tiny_probe = true;
        config.validate_full_frame = true;
        config.consecutive_failures_reset = 5;
        
        capturer->setBlackFrameConfig(config);
        
        // Verify configuration was set
        auto retrievedConfig = capturer->getBlackFrameConfig();
        std::cout << "\nConfigured settings:" << std::endl;
        std::cout << "  enable_detection: " << retrievedConfig.enable_detection << std::endl;
        std::cout << "  enable_auto_retry: " << retrievedConfig.enable_auto_retry << std::endl;
        std::cout << "  max_retries: " << retrievedConfig.max_retries << std::endl;
        std::cout << "  retry_delay_ms: " << retrievedConfig.retry_delay_ms << std::endl;
        std::cout << "  black_threshold: " << retrievedConfig.black_threshold << std::endl;
        std::cout << "  log_black_frames: " << retrievedConfig.log_black_frames << std::endl;
        std::cout << "  validate_tiny_probe: " << retrievedConfig.validate_tiny_probe << std::endl;
        std::cout << "  validate_full_frame: " << retrievedConfig.validate_full_frame << std::endl;
        std::cout << "  consecutive_failures_reset: " << retrievedConfig.consecutive_failures_reset << std::endl;
        
        // Test initialization and basic capture
        auto result = capturer->init(capscr::CaptureTargetType::FullScreen);
        if (result == capscr::Result::Ok) {
            std::cout << "\nCapturer initialized successfully!" << std::endl;
            
            // Test a few captures to see if retry logic works
            capscr::Frame frame;
            for (int i = 0; i < 3; ++i) {
                auto captureResult = capturer->capture(frame);
                std::cout << "Capture " << i + 1 << " result: ";
                switch (captureResult) {
                    case capscr::Result::Ok:
                        std::cout << "Ok (frame: " << frame.width << "x" << frame.height << ")";
                        break;
                    case capscr::Result::BlackFrameDetected:
                        std::cout << "BlackFrameDetected";
                        break;
                    case capscr::Result::RetryExhausted:
                        std::cout << "RetryExhausted";
                        break;
                    case capscr::Result::Error:
                        std::cout << "Error";
                        break;
                    default:
                        std::cout << "Other (" << static_cast<int>(captureResult) << ")";
                        break;
                }
                std::cout << std::endl;
            }
        } else {
            std::cout << "\nFailed to initialize capturer: " << static_cast<int>(result) << std::endl;
        }
        
        capturer->shutdown();
        std::cout << "\nBlack frame API test completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
