#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/platform/windows/capturer_dxgi.hpp"
#ifdef _WIN32

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <mutex>

namespace capscr {

class GdiCapturer : public ICapturer {
public:
    GdiCapturer() = default;
    ~GdiCapturer() override { shutdown(); }

    Result init(CaptureTargetType type, const std::string& displayId, const Rect* optTarget, const std::string& optWindowId) override {
        // Only support FullScreen and Region for now.
        targetType = type;
        chosenDisplayId = displayId;
        if (type == CaptureTargetType::Region) {
            if (!optTarget) return Result::Error;
            std::lock_guard<std::mutex> g(targetMutex);
            rect = *optTarget;
        }
        if (type == CaptureTargetType::Window && !optWindowId.empty()) {
            // parse optWindowId: if starts with 0x or all digits, treat as HWND value; otherwise use FindWindowA
            try {
                uintptr_t val = 0;
                if (optWindowId.substr(0, 2) == "0x" || optWindowId.substr(0, 2) == "0X") {
                    // Hexadecimal format
                    val = std::stoull(optWindowId, nullptr, 16);
                    fprintf(stderr, "GDI: Parsed window ID '%s' as hex HWND=0x%lx\n", optWindowId.c_str(), val);
                } else {
                    // Try decimal format first
                    val = std::stoull(optWindowId, nullptr, 10);
                    fprintf(stderr, "GDI: Parsed window ID '%s' as decimal HWND=0x%lx\n", optWindowId.c_str(), val);
                }
                targetHwnd = reinterpret_cast<HWND>(val);
            } catch(...) {
                // Not a number, treat as window title
                fprintf(stderr, "GDI: Treating window ID '%s' as window title\n", optWindowId.c_str());
                targetHwnd = FindWindowA(nullptr, optWindowId.c_str());
            }
            if (!targetHwnd) {
                fprintf(stderr, "GDI: Window not found for ID '%s'\n", optWindowId.c_str());
                return Result::WindowNotFound;
            }
            fprintf(stderr, "GDI: Target window HWND=%p for ID '%s'\n", targetHwnd, optWindowId.c_str());
        }
        return Result::Ok;
    }

    Result setTarget(CaptureTargetType type, const std::string& displayId, const Rect* optTarget, const std::string& optWindowId) override {
        std::lock_guard<std::mutex> g(targetMutex);
        targetType = type;
        chosenDisplayId = displayId;
        if (type == CaptureTargetType::Region) {
            if (!optTarget) return Result::Error;
            rect = *optTarget;
        }
        return Result::Ok;
    }

    Rect getTarget() const override {
        std::lock_guard<std::mutex> g(targetMutex);
        return rect;
    }

    Result capture(Frame& outFrame) override {
        CaptureParams defaultParams; // Use default parameters (no resize/conversion)
        return capture(outFrame, defaultParams);
    }

    Result capture(Frame& outFrame, const CaptureParams& params) override {
        // Parameter validation 
        if (params.output_width < 0 || params.output_height < 0) {
            fprintf(stderr, "GDI: Invalid output dimensions: %dx%d (negative values not allowed)\n", 
                    params.output_width, params.output_height);
            return Result::Error;
        }

        if (params.output_format == PixelFormat::Unknown) {
            fprintf(stderr, "GDI: Invalid output format: Unknown\n");
            return Result::Error;
        }

        // Prepare state for optional window topmost handling
        HWND prevForeground = nullptr;
        HWND hwnd = nullptr;

    // If targeting a window, check its state first
        if (targetType == CaptureTargetType::Window) {
            hwnd = targetHwnd;
            fprintf(stderr, "GDI: Checking window HWND=%p\n", hwnd);
            if (!IsWindow(hwnd)) {
                fprintf(stderr, "GDI: IsWindow failed - window not valid\n");
                return Result::WindowClosed;
            }
            WINDOWPLACEMENT wp{};
            wp.length = sizeof(wp);
            if (GetWindowPlacement(hwnd, &wp)) {
                if (wp.showCmd == SW_SHOWMINIMIZED) {
                    fprintf(stderr, "GDI: Window is minimized, attempting to restore...\n");
                    // Try to restore the window temporarily
                    ShowWindow(hwnd, SW_RESTORE);
                    Sleep(100); // Give it time to restore
                    
                    // Check again
                    WINDOWPLACEMENT wp2{};
                    wp2.length = sizeof(wp2);
                    if (GetWindowPlacement(hwnd, &wp2) && wp2.showCmd == SW_SHOWMINIMIZED) {
                        fprintf(stderr, "GDI: Window remains minimized after restore attempt\n");
                        return Result::WindowMinimized;
                    }
                    fprintf(stderr, "GDI: Window restored successfully\n");
                }
            }
                // need to get window rect for capture coordinates
                RECT wrc;
                if (!GetWindowRect(hwnd, &wrc)) {
                    fprintf(stderr, "GDI: GetWindowRect failed with error %lu\n", GetLastError());
                    return Result::Error;
                }
            // adjust sx,sy,width,height from window rect if region not specified
            rect.x = wrc.left;
            rect.y = wrc.top;
            rect.width = wrc.right - wrc.left;
            rect.height = wrc.bottom - wrc.top;
            
            fprintf(stderr, "GDI: Window rect from GetWindowRect: (%ld,%ld,%ld,%ld) -> size %dx%d at (%d,%d)\n",
                wrc.left, wrc.top, wrc.right, wrc.bottom, rect.width, rect.height, rect.x, rect.y);
            // Try to bring target window to top so we capture its visible content even if it was obscured.
            // Save previous foreground to restore later.
            // prevForeground = GetForegroundWindow();
            
            if (IsWindow(hwnd)) {
                // Bring window to top and set as foreground
                // BringWindowToTop(hwnd);
                // SetForegroundWindow(hwnd);
                // Sleep(50); // Short delay to allow window to redraw its contents
            }
        }
        HDC hScreenDC = GetDC(nullptr);
        HDC hMemDC = CreateCompatibleDC(hScreenDC);
        if (!hMemDC) return Result::Error;

        int w = 0, h = 0;
        int display_x = 0, display_y = 0;
        
        // For multi-display support, find the correct display bounds
        if (targetType == CaptureTargetType::FullScreen) {
            // Find the display info for chosenDisplayId
            auto displays = listDisplays();
            bool found = false;
            for (const auto& display : displays) {
                if (display.id == chosenDisplayId || display.name == chosenDisplayId) {
                    w = display.bounds.width;
                    h = display.bounds.height;
                    display_x = display.bounds.x;
                    display_y = display.bounds.y;
                    found = true;
                    // fprintf(stderr, "GDI: Using display %s (%dx%d at %d,%d)\n", 
                    //         chosenDisplayId.c_str(), w, h, display_x, display_y);
                    break;
                }
            }
            if (!found) {
                // Fallback to primary display
                w = GetSystemMetrics(SM_CXSCREEN);
                h = GetSystemMetrics(SM_CYSCREEN);
                display_x = 0;
                display_y = 0;
                fprintf(stderr, "GDI: Display %s not found, using primary display (%dx%d)\n", 
                        chosenDisplayId.c_str(), w, h);
            }
        } else {
            w = rect.width;
            h = rect.height;
        }

        HBITMAP hBitmap = CreateCompatibleBitmap(hScreenDC, w, h);
        if (!hBitmap) {
            DeleteDC(hMemDC);
            ReleaseDC(nullptr, hScreenDC);
            return Result::Error;
        }

        SelectObject(hMemDC, hBitmap);

        Rect activeRect;
        CaptureTargetType activeType;
        {
            std::lock_guard<std::mutex> g(targetMutex);
            activeRect = rect;
            activeType = targetType;
        }

        int sx, sy;
        if (activeType == CaptureTargetType::FullScreen) {
            sx = display_x;
            sy = display_y;
        } else {
            sx = activeRect.x;
            sy = activeRect.y;
        }

        BOOL bitbltSuccess = FALSE;
        
        // For window capture, try PrintWindow first (better for modern apps like Flutter/DirectX)
        if (activeType == CaptureTargetType::Window && hwnd) {
            fprintf(stderr, "GDI: Attempting PrintWindow for window capture\n");
            bitbltSuccess = PrintWindow(hwnd, hMemDC, PW_RENDERFULLCONTENT);
            if (bitbltSuccess) {
                fprintf(stderr, "GDI: PrintWindow succeeded\n");
            } else {
                fprintf(stderr, "GDI: PrintWindow failed (%lu), trying BitBlt\n", GetLastError());
            }
        }
        
        // If PrintWindow failed or not applicable, use BitBlt
        if (!bitbltSuccess) {
            bitbltSuccess = BitBlt(hMemDC, 0, 0, w, h, hScreenDC, sx, sy, SRCCOPY | CAPTUREBLT);
            if (bitbltSuccess) {
                // fprintf(stderr, "GDI: BitBlt succeeded\n");
            } else {
                fprintf(stderr, "GDI: BitBlt failed (%lu)\n", GetLastError());
            }
        }

        if (!bitbltSuccess) {
            DeleteObject(hBitmap);
            DeleteDC(hMemDC);
            ReleaseDC(nullptr, hScreenDC);
            // BitBlt failed; try PrintWindow as a fallback for some windows
            HDC hWndDC = CreateCompatibleDC(hScreenDC);
            HBITMAP hWndBmp = CreateCompatibleBitmap(hScreenDC, w, h);
            if (hWndBmp) {
                SelectObject(hWndDC, hWndBmp);
                if (PrintWindow(hwnd, hWndDC, PW_CLIENTONLY)) {
                    // copy the printed bitmap into our original memDC buffer
                    BITMAPINFO bmi2 = {};
                    bmi2.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
                    bmi2.bmiHeader.biWidth = w;
                    bmi2.bmiHeader.biHeight = -h;
                    bmi2.bmiHeader.biPlanes = 1;
                    bmi2.bmiHeader.biBitCount = 32;
                    bmi2.bmiHeader.biCompression = BI_RGB;
                    outFrame.width = w;
                    outFrame.height = h;
                    outFrame.format = PixelFormat::BGRA32;
                    outFrame.stride = w * 4;
                    outFrame.data.resize(outFrame.stride * h);
                    if (GetDIBits(hWndDC, hWndBmp, 0, h, outFrame.data.data(), &bmi2, DIB_RGB_COLORS)) {
                        DeleteObject(hWndBmp);
                        DeleteDC(hWndDC);
                        // Restore foreground window before returning success
                        if (prevForeground && prevForeground != hwnd) SetForegroundWindow(prevForeground);
                        return Result::Ok;
                    }
                }
                DeleteObject(hWndBmp);
            }
            DeleteDC(hWndDC);
            // Restore foreground window before returning error
            if (prevForeground && prevForeground != hwnd) SetForegroundWindow(prevForeground);
            return Result::Error;
        } else {
            // BitBlt succeeded
        }

        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = w;
        bmi.bmiHeader.biHeight = -h; // top-down
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 32;
        bmi.bmiHeader.biCompression = BI_RGB;

    outFrame.width = w;
    outFrame.height = h;
        outFrame.format = PixelFormat::BGRA32;
        outFrame.stride = w * 4;
        outFrame.data.resize(outFrame.stride * h);

        if (!GetDIBits(hMemDC, hBitmap, 0, h, outFrame.data.data(), &bmi, DIB_RGB_COLORS)) {
            DeleteObject(hBitmap);
            DeleteDC(hMemDC);
            ReleaseDC(nullptr, hScreenDC);
            return Result::Error;
        }

        DeleteObject(hBitmap);
        DeleteDC(hMemDC);
        ReleaseDC(nullptr, hScreenDC);

        // Restore previous foreground window if we changed it
        if (prevForeground && prevForeground != hwnd) SetForegroundWindow(prevForeground);

        // Apply image processing if needed (resize/format conversion)
        bool needsProcessing = (params.output_width > 0 && params.output_width != w) ||
                              (params.output_height > 0 && params.output_height != h) ||
                              (params.output_format != PixelFormat::BGRA32);

        if (needsProcessing) {
            Frame sourceFrame = std::move(outFrame); // Move current frame as source
            return processFrame(sourceFrame, outFrame, params);
        }

        return Result::Ok;
    }

    static BOOL CALLBACK monitor_enum_proc(HMONITOR hMon, HDC, LPRECT, LPARAM dwData) {
        auto vec = reinterpret_cast<std::vector<DisplayInfo>*>(dwData);
        MONITORINFOEX mi;
        mi.cbSize = sizeof(mi);
        if (GetMonitorInfo(hMon, &mi)) {
            DisplayInfo di;
            di.name = mi.szDevice ? std::string(mi.szDevice) : std::string();
            di.id = di.name;
            di.bounds.x = mi.rcMonitor.left;
            di.bounds.y = mi.rcMonitor.top;
            di.bounds.width = mi.rcMonitor.right - mi.rcMonitor.left;
            di.bounds.height = mi.rcMonitor.bottom - mi.rcMonitor.top;
            di.scale = 1.0f; // TODO: query DPI for scale if needed
            vec->push_back(di);
        }
        return TRUE;
    }

    std::vector<DisplayInfo> listDisplays() override {
        std::vector<DisplayInfo> out;
        EnumDisplayMonitors(nullptr, nullptr, monitor_enum_proc, reinterpret_cast<LPARAM>(&out));
        return out;
    }

    BackendCapability capabilities() const override {
        return BackendCapability::SupportFullScreen | BackendCapability::SupportRegion | BackendCapability::SupportWindow;
    }

    void shutdown() override {
        // Nothing to do for GDI simple impl
    }

    // GPU-related and black-frame config stubs to match header
    Result captureGpu(GpuTexture& outTexture) override {
        // GDI does not support zero-copy GPU textures
        (void)outTexture;
        return Result::Unsupported;
    }

    bool isGpuCaptureAvailable() const override {
        return false;
    }
    BlackFrameConfig getBlackFrameConfig() const override {
        return BlackFrameConfig{};
    }

    void setBlackFrameConfig(const BlackFrameConfig& config) override {
        (void)config; // no-op for GDI
    }

private:
    CaptureTargetType targetType = CaptureTargetType::FullScreen;
    Rect rect{};
    mutable std::mutex targetMutex;
    std::string chosenDisplayId;
    HWND targetHwnd = nullptr;
};

// Prefer DXGI when available
#ifdef _WIN32
std::unique_ptr<ICapturer> createBestCapturer() {
    // Try to instantiate DXGI backend first
    try {
        auto dx = capscr::createDXGICapturer();
        if (dx) return dx;
    } catch(...) {}
    return std::make_unique<GdiCapturer>();
}

std::unique_ptr<ICapturer> createGdiCapturer() {
    return std::make_unique<GdiCapturer>();
}
#else
std::unique_ptr<capscr::ICapturer> capscr::createBestCapturer() { return nullptr; }
#endif

} // namespace capscr

#else
std::unique_ptr<capscr::ICapturer> capscr::createBestCapturer() { return nullptr; }
#endif
