#include <iostream>
#include <fstream>
#include <filesystem>
#include <chrono>
#include <thread>
#include <cassert>
#include <functional>
#include <vector>
#include <memory>
#include "capscr/logging.hpp"

// Simple test framework for standalone logging tests
class LoggingTestRunner {
private:
    int passed = 0;
    int failed = 0;
    std::string current_test;

public:
    void start_test(const std::string& name) {
        current_test = name;
        std::cout << "Running test: " << name << " ... ";
    }
    
    void assert_true(bool condition, const std::string& message = "") {
        if (!condition) {
            std::cout << "FAILED" << std::endl;
            if (!message.empty()) {
                std::cout << "  Assertion failed: " << message << std::endl;
            }
            failed++;
        }
    }
    
    void assert_no_throw(std::function<void()> func, const std::string& message = "") {
        try {
            func();
        } catch (const std::exception& e) {
            std::cout << "FAILED" << std::endl;
            std::cout << "  Unexpected exception: " << e.what() << std::endl;
            if (!message.empty()) {
                std::cout << "  " << message << std::endl;
            }
            failed++;
            return;
        }
    }
    
    void finish_test() {
        if (current_test.empty() == false) {
            std::cout << "PASSED" << std::endl;
            passed++;
        }
        current_test.clear();
    }
    
    void print_summary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Passed: " << passed << std::endl;
        std::cout << "Failed: " << failed << std::endl;
        std::cout << "Total:  " << (passed + failed) << std::endl;
        
        if (failed > 0) {
            std::cout << "\nSome tests failed!" << std::endl;
        } else {
            std::cout << "\nAll tests passed!" << std::endl;
        }
    }
    
    bool all_passed() const { return failed == 0; }
};

void test_basic_initialization(LoggingTestRunner& runner) {
    runner.start_test("Basic Logger Initialization");
    
    // Test basic initialization
    runner.assert_no_throw([]() {
        capscr::logging::init();
    }, "Basic init should not throw");
    
    // Test logger retrieval
    auto logger = capscr::logging::get_logger();
    runner.assert_true(logger != nullptr, "Logger should not be null");
    
    // Test that we can get logger multiple times with same result
    auto logger2 = capscr::logging::get_logger();
    runner.assert_true(logger == logger2, "Multiple get_logger calls should return same instance");
    
    runner.finish_test();
}

void test_file_logging(LoggingTestRunner& runner) {
    runner.start_test("File Logging");
    
    std::string test_log_file = "standalone_test.log";
    
    // Clean up any existing log file
    if (std::filesystem::exists(test_log_file)) {
        std::filesystem::remove(test_log_file);
    }
    
    // Shutdown existing logger
    capscr::logging::shutdown();
    
    // Initialize with file logging
    runner.assert_no_throw([&]() {
        capscr::logging::init(test_log_file);
    }, "File logging init should not throw");
    
    auto logger = capscr::logging::get_logger();
    runner.assert_true(logger != nullptr, "Logger should exist after file init");
    
    // Generate some log messages
    runner.assert_no_throw([&]() {
        logger->info("Test info message");
        logger->warn("Test warning message");
        logger->error("Test error message");
    }, "Logging calls should not throw");
    
    // Force flush and shutdown
    capscr::logging::shutdown();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Check if log file was created
    runner.assert_true(std::filesystem::exists(test_log_file), "Log file should be created");
    
    // Clean up - try multiple times since Windows may keep file handle open
    int attempts = 0;
    while (std::filesystem::exists(test_log_file) && attempts < 5) {
        try {
            std::filesystem::remove(test_log_file);
            break;
        } catch (const std::exception&) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            attempts++;
        }
    }
    
    runner.finish_test();
}

void test_log_level_filtering(LoggingTestRunner& runner) {
    runner.start_test("Log Level Filtering");
    
    // Reinitialize logger
    capscr::logging::init();
    auto logger = capscr::logging::get_logger();
    runner.assert_true(logger != nullptr, "Logger should exist");
    
    // Test setting different log levels
    runner.assert_no_throw([&]() {
        capscr::logging::set_level(capscr::LogLevel::Info);
    }, "Setting log level to Info should not throw");
    
    runner.assert_true(logger->level() == capscr::LogLevel::Info, "Log level should be Info");
    
    runner.assert_no_throw([&]() {
        capscr::logging::set_level(capscr::LogLevel::Debug);
    }, "Setting log level to Debug should not throw");
    
    runner.assert_true(logger->level() == capscr::LogLevel::Debug, "Log level should be Debug");
    
    runner.assert_no_throw([&]() {
        capscr::logging::set_level(capscr::LogLevel::Error);
    }, "Setting log level to Error should not throw");
    
    runner.assert_true(logger->level() == capscr::LogLevel::Error, "Log level should be Error");
    
    runner.finish_test();
}

void test_convenience_macros(LoggingTestRunner& runner) {
    runner.start_test("Convenience Macros");
    
    // Ensure we have a logger
    capscr::logging::init();
    capscr::logging::set_level(capscr::LogLevel::Trace);
    
    // Test all macro levels
    runner.assert_no_throw([]() {
        CAPSCR_LOG_TRACE("Trace message");
    }, "TRACE macro should not throw");
    
    runner.assert_no_throw([]() {
        CAPSCR_LOG_DEBUG("Debug message");
    }, "DEBUG macro should not throw");
    
    runner.assert_no_throw([]() {
        CAPSCR_LOG_INFO("Info message");
    }, "INFO macro should not throw");
    
    runner.assert_no_throw([]() {
        CAPSCR_LOG_WARN("Warning message");
    }, "WARN macro should not throw");
    
    runner.assert_no_throw([]() {
        CAPSCR_LOG_ERROR("Error message");
    }, "ERROR macro should not throw");
    
    runner.assert_no_throw([]() {
        CAPSCR_LOG_CRITICAL("Critical message");
    }, "CRITICAL macro should not throw");
    
    runner.finish_test();
}

void test_custom_logger_injection(LoggingTestRunner& runner) {
    runner.start_test("Custom Logger Injection");
    
    // Create a test logger
    class TestLogger : public capscr::ILogger {
    public:
        void log(capscr::LogLevel level, const std::string& message) override {
            messages.push_back({level, message});
        }
        
        void set_level(capscr::LogLevel level) override {
            current_level = level;
        }
        
        capscr::LogLevel level() const override {
            return current_level;
        }
        
        std::vector<std::pair<capscr::LogLevel, std::string>> messages;
        capscr::LogLevel current_level = capscr::LogLevel::Info;
    };
    
    auto test_logger = std::make_shared<TestLogger>();
    
    // Save current logger to restore later
    auto original_logger = capscr::logging::get_logger();
    
    // Set our test logger
    runner.assert_no_throw([&]() {
        capscr::logging::set_logger(test_logger);
    }, "Setting custom logger should not throw");
    
    // Verify it's active
    auto active_logger = capscr::logging::get_logger();
    runner.assert_true(active_logger == test_logger, "Active logger should be our test logger");
    
    // Test logging through it
    runner.assert_no_throw([&]() {
        test_logger->warn("Test warning");
    }, "Logging through custom logger should not throw");
    
    runner.assert_true(test_logger->messages.size() == 1, "Should have one log message");
    if (!test_logger->messages.empty()) {
        runner.assert_true(test_logger->messages[0].first == capscr::LogLevel::Warn, "Log level should be Warn");
        runner.assert_true(test_logger->messages[0].second == "Test warning", "Log message should match");
    }
    
    // Restore original logger
    capscr::logging::set_logger(original_logger);
    
    runner.finish_test();
}

void test_multiple_initialization(LoggingTestRunner& runner) {
    runner.start_test("Multiple Initialization Safety");
    
    // Multiple initializations should be safe
    runner.assert_no_throw([]() {
        capscr::logging::init();
        capscr::logging::init(); // Should not crash
    }, "Multiple init calls should be safe");
    
    auto logger = capscr::logging::get_logger();
    runner.assert_true(logger != nullptr, "Logger should still be available");
    
    runner.finish_test();
}

void test_logging_without_init(LoggingTestRunner& runner) {
    runner.start_test("Logging Without Explicit Init");
    
    // Shutdown any existing logger
    capscr::logging::shutdown();
    
    // Get logger without explicit init (should return fallback logger)
    auto logger = capscr::logging::get_logger();
    runner.assert_true(logger != nullptr, "Should get fallback logger");
    
    // Should be able to log without crashing
    runner.assert_no_throw([&]() {
        logger->log(capscr::LogLevel::Info, "Test without init");
    }, "Logging without init should not throw");
    
    runner.assert_no_throw([]() {
        CAPSCR_LOG_INFO("Macro test without init");
    }, "Macro logging without init should not throw");
    
    runner.finish_test();
}

int main() {
    std::cout << "=== Standalone Logging System Tests ===" << std::endl;
    std::cout << "Testing capscr logging functionality independently" << std::endl << std::endl;
    
    LoggingTestRunner runner;
    
    try {
        test_basic_initialization(runner);
        test_file_logging(runner);
        test_log_level_filtering(runner);
        test_convenience_macros(runner);
        test_custom_logger_injection(runner);
        test_multiple_initialization(runner);
        test_logging_without_init(runner);
        
        runner.print_summary();
        
        // Final cleanup
        capscr::logging::shutdown();
        
        return runner.all_passed() ? 0 : 1;
        
    } catch (const std::exception& e) {
        std::cerr << "Unexpected error during testing: " << e.what() << std::endl;
        return 1;
    }
}
