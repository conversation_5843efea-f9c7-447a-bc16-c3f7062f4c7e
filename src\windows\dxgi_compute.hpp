#pragma once
#ifdef _WIN32

#include <windows.h>
#include <d3d11.h>
#include <wrl.h>
#include <string>
#include <memory>

#include "../../include/capscr/capture.hpp"
#include "../../include/capscr/logging.hpp"

using Microsoft::WRL::ComPtr;

namespace capscr {

/**
 * Manages compute shaders for GPU image processing (resize, format conversion)
 */
class DXGICompute {
public:
    DXGICompute(ComPtr<ID3D11Device> device, ComPtr<ID3D11DeviceContext> context);
    ~DXGICompute();

    // Initialize compute shaders
    Result initialize();

    // Resize texture using Mitchell-Netravali filter (two-pass)
    Result resizeTexture(ComPtr<ID3D11Texture2D> inputTexture,
                        UINT inputWidth, UINT inputHeight,
                        UINT outputWidth, UINT outputHeight,
                        int quality, // ResizeQuality placeholder
                        ComPtr<ID3D11Texture2D>& outputTexture);

    // Convert pixel format (e.g., BGRA to RGBA)
    Result convertPixelFormat(ComPtr<ID3D11Texture2D> inputTexture,
                            PixelFormat inputFormat,
                            PixelFormat outputFormat,
                            int colorSpace, // ColorSpace placeholder
                            ComPtr<ID3D11Texture2D>& outputTexture);

    // Combined resize and convert operation
    Result processTexture(ComPtr<ID3D11Texture2D> inputTexture,
                         const CaptureParams& params,
                         ComPtr<ID3D11Texture2D>& outputTexture);

    // Convert input texture to NV12 planar GPU textures (Y and UV)
    // Produces two textures: Y plane (R8_UNORM) at full resolution and
    // UV plane (R8G8_UNORM) at half resolution. Caller receives GPU
    // textures and is responsible for further handling (e.g., creating
    // shared handles). Returns Result::Ok on success.
    // If enableSharing is true, textures will be created with D3D11_RESOURCE_MISC_SHARED
    Result convertToNV12(ComPtr<ID3D11Texture2D> inputTexture,
                         UINT outputWidth, UINT outputHeight,
                         ComPtr<ID3D11Texture2D>& yPlane,
                         ComPtr<ID3D11Texture2D>& uvPlane,
                         bool enableSharing = false);

    // Check if compute shaders are available
    bool isAvailable() const;

    // ...existing code...

private:
    ComPtr<ID3D11Device> device_;
    ComPtr<ID3D11DeviceContext> context_;

    // Compute shaders
    ComPtr<ID3D11ComputeShader> horizontalResizeShader_;
    ComPtr<ID3D11ComputeShader> verticalResizeShader_;
    ComPtr<ID3D11ComputeShader> singlePassResizeShader_;
    ComPtr<ID3D11ComputeShader> convertShader_;
    ComPtr<ID3D11ComputeShader> bgraToRgbaShader_;
    ComPtr<ID3D11ComputeShader> rgbaToBgraShader_;
    // NV12 conversion shaders
    ComPtr<ID3D11ComputeShader> nv12YShader_;
    ComPtr<ID3D11ComputeShader> nv12UVShader_;

    // Sampler state
    ComPtr<ID3D11SamplerState> linearSampler_;

    // Shader resource views and unordered access views cache
    struct TextureViews {
        ComPtr<ID3D11ShaderResourceView> srv;
        ComPtr<ID3D11UnorderedAccessView> uav;
    };
    
    // Initialization status
    bool initialized_;

    // Constant buffer structures (must match HLSL)
    struct ResizeConstantBuffer {
        struct {
            UINT x, y, z, w;  // dimensions
        } dimensions;
        struct {
            float x, y, z, w; // params
        } params;
        struct {
            float x, y, z, w; // scale_offset
        } scale_offset;
    };

    struct ConvertConstantBuffer {
        struct {
            UINT x, y, z, w;  // dimensions
        } dimensions;
        struct {
            UINT x, y, z, w;  // formats
        } formats;
        struct {
            float x, y, z, w; // conversion_params
        } conversion_params;
    };

    // Helper methods
    Result loadShaderFromFile(const std::string& filename, const std::string& entryPoint,
                             ComPtr<ID3D11ComputeShader>& shader);
    Result loadComputeShader(const std::string& shaderCode, const std::string& entryPoint,
                           ComPtr<ID3D11ComputeShader>& shader);
    Result createConstantBuffer(UINT size, ComPtr<ID3D11Buffer>& buffer);
    Result createTextureViews(ComPtr<ID3D11Texture2D> texture, TextureViews& views);
    Result createIntermediateTexture(UINT width, UINT height, DXGI_FORMAT format,
                                   ComPtr<ID3D11Texture2D>& texture);

    // Resize helper methods
    Result resizeSinglePass(ComPtr<ID3D11Texture2D> inputTexture,
                           UINT inputWidth, UINT inputHeight,
                           UINT outputWidth, UINT outputHeight,
                           int quality,
                           ComPtr<ID3D11Texture2D>& outputTexture);
    Result resizeTwoPass(ComPtr<ID3D11Texture2D> inputTexture,
                        UINT inputWidth, UINT inputHeight,
                        UINT outputWidth, UINT outputHeight,
                        int quality,
                        ComPtr<ID3D11Texture2D>& outputTexture);
    Result resizeHorizontal(ComPtr<ID3D11Texture2D> inputTexture,
                           UINT inputWidth, UINT inputHeight,
                           UINT outputWidth, UINT outputHeight,
                           int quality,
                           ComPtr<ID3D11Texture2D> outputTexture);
    Result resizeVertical(ComPtr<ID3D11Texture2D> inputTexture,
                         UINT inputWidth, UINT inputHeight,
                         UINT outputWidth, UINT outputHeight,
                         int quality,
                         ComPtr<ID3D11Texture2D> outputTexture);

    // Convert helper methods
    Result convertBGRAToRGBA(ComPtr<ID3D11Texture2D> inputTexture,
                            ComPtr<ID3D11Texture2D> outputTexture);
    Result convertRGBAToBGRA(ComPtr<ID3D11Texture2D> inputTexture,
                            ComPtr<ID3D11Texture2D> outputTexture);
    Result convertGeneral(ComPtr<ID3D11Texture2D> inputTexture,
                         PixelFormat inputFormat,
                         PixelFormat outputFormat,
                         int colorSpace,
                         ComPtr<ID3D11Texture2D> outputTexture);
    

    // Compute group sizes (optimized for different GPU architectures)
    static const UINT THREAD_GROUP_SIZE_X = 16;
    static const UINT THREAD_GROUP_SIZE_Y = 16;

    // Helper to get optimal dispatch dimensions
    void calculateDispatchDimensions(UINT width, UINT height, UINT& groupsX, UINT& groupsY);
    
    // Safe dispatch with error checking
    Result safeDispatch(UINT outputWidth, UINT outputHeight);
};

} // namespace capscr

#endif // _WIN32
